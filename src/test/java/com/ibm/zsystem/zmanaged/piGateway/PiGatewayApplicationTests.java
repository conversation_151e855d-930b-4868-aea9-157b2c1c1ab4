package com.ibm.zsystem.zmanaged.piGateway;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;




@SpringBootTest
public class PiGatewayApplicationTests {

	@Test
	public void contextLoads() {
	}

//	@Test
//	public void whenSendRequestTozAwareResource_thenOK() {
//		Response response = RestAssured.get("http://localhost:8085/zAware/authuser/Topology");
//		assertNotNull(response);
//	}

}
