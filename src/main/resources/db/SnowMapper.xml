<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper        
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"        
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibm.zds.ext.database.mapper.snow.SnowMapper">    
    <resultMap id="ConfigurationItemResultMap" type="com.ibm.zds.ext.database.entity.snow.ConfigurationItem">
        <id property="extID" column="ext_id" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="sysID" column="sys_id" jdbcType="VARCHAR" />
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="correlationID" column="correlation_id" jdbcType="VARCHAR" />
        <result property="ciClass" column="ci_class" jdbcType="VARCHAR" />
        <result property="description" column="description" jdbcType="VARCHAR" />
        <result property="updatedOn" column="updated_on" jdbcType="VARCHAR" />
    </resultMap>

	<!-- <select id="findConfigurationItemAll" resultMap="ConfigurationItemResultMap">        
		SELECT * FROM ext_snow_configuration_item    
	</select> -->
    <select id="findConfigurationItemByTagAndSysID" resultMap="ConfigurationItemResultMap">        
		SELECT * FROM ext_snow_configuration_item WHERE 
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>
    


    <resultMap id="UserResultMap" type="com.ibm.zds.ext.database.entity.snow.User">
        <id property="extID" column="ext_id" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="sysID" column="sys_id" jdbcType="VARCHAR" />
        <result property="active" column="active" jdbcType="BOOLEAN" />
        <result property="nameID" column="name_id" jdbcType="VARCHAR" />
        <result property="firstName" column="first_name" jdbcType="VARCHAR" />
        <result property="lastName" column="last_name" jdbcType="VARCHAR" />
        <result property="title" column="title" jdbcType="VARCHAR" />
    </resultMap>
	<!-- <select id="findUserAll" resultMap="UserResultMap">        
		SELECT * FROM ext_snow_user    
	</select> -->
    <select id="findUserByTagAndSysID" resultMap="UserResultMap" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_user WHERE
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>
    


    <resultMap id="GroupResultMap" type="com.ibm.zds.ext.database.entity.snow.Group">
        <id property="extID" column="ext_id" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="sysID" column="sys_id" jdbcType="VARCHAR" />
        <result property="active" column="active" jdbcType="BOOLEAN" />
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="description" column="description" jdbcType="VARCHAR" />
    </resultMap>
	<!-- <select id="findGroupAll" resultMap="GroupResultMap">        
		SELECT * FROM ext_snow_group 
	</select> -->
    <select id="findGroupByTagAndSysID" resultMap="GroupResultMap" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_group WHERE
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>


    <resultMap id="ServiceResultMap" type="com.ibm.zds.ext.database.entity.snow.Service">
        <id property="extID" column="ext_id" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="sysID" column="sys_id" jdbcType="VARCHAR" />
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="supportGroup" column="support_group" jdbcType="VARCHAR" />
        <result property="ownerBy" column="owner_by" jdbcType="VARCHAR" />
        <result property="managedBy" column="managed_by" jdbcType="VARCHAR" />
        <result property="version" column="version" jdbcType="VARCHAR" />
        <result property="location" column="location" jdbcType="VARCHAR" />
    </resultMap>
	<!-- <select id="findServiceAll" resultMap="ServiceResultMap">        
		SELECT * FROM ext_snow_service
	</select> -->
    <select id="findServiceByTagAndSysID" resultMap="ServiceResultMap" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_service WHERE
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>



    <resultMap id="IncidentResultMap" type="com.ibm.zds.ext.database.entity.snow.Incident">
        <id property="extID" column="ext_id" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="sysID" column="sys_id" jdbcType="VARCHAR" />
        <result property="number" column="number" jdbcType="VARCHAR" />
        <result property="state" column="state" jdbcType="VARCHAR" />
        <result property="priority" column="priority" jdbcType="VARCHAR" />
        <result property="impact" column="impact" jdbcType="VARCHAR" />
        <result property="urgency" column="urgency" jdbcType="VARCHAR" />
        <result property="caller" column="caller" jdbcType="VARCHAR" />
        <result property="configurationItem" column="configuration_item" jdbcType="VARCHAR" />
        <result property="assignmentGroup" column="assignment_group" jdbcType="VARCHAR" />
        <result property="assignedTo" column="assigned_to" jdbcType="VARCHAR" />
        <result property="lastUpdated" column="last_updated" jdbcType="VARCHAR" />
        <result property="description" column="description" jdbcType="VARCHAR" />
        <result property="shortDescription" column="short_description" jdbcType="VARCHAR" />
        <result property="workNotes" column="work_notes" jdbcType="VARCHAR" />
        <result property="resolutionCode" column="resolution_code" jdbcType="VARCHAR" />
        <result property="rfc" column="rfc" jdbcType="VARCHAR" />
        <result property="causedByChange" column="caused_by_change" jdbcType="VARCHAR" />
        <result property="problem" column="problem" jdbcType="VARCHAR" />
    </resultMap>
	<!-- <select id="findIncidentAll" resultMap="IncidentResultMap">        
		SELECT * FROM ext_snow_incident
	</select> -->
    <select id="findIncidentByTagAndSysID" resultMap="IncidentResultMap" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_incident WHERE
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>



    <resultMap id="ProblemResultMap" type="com.ibm.zds.ext.database.entity.snow.Problem">
        <id property="extID" column="ext_id" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="sysID" column="sys_id" jdbcType="VARCHAR" />
        <result property="number" column="number" jdbcType="VARCHAR" />
        <result property="firstReportedBy" column="first_reported_by" jdbcType="VARCHAR" />
        <result property="state" column="state" jdbcType="VARCHAR" />
        <result property="priority" column="priority" jdbcType="VARCHAR" />
        <result property="impact" column="impact" jdbcType="VARCHAR" />
        <result property="urgency" column="urgency" jdbcType="VARCHAR" />
        <result property="firstReported" column="first_reported" jdbcType="VARCHAR" />
        <result property="lastUpdated" column="last_updated" jdbcType="VARCHAR" />
        <result property="configurationItem" column="configuration_item" jdbcType="VARCHAR" />
        <result property="assignmentGroup" column="assignment_group" jdbcType="VARCHAR" />
        <result property="assignedTo" column="assigned_to" jdbcType="VARCHAR" />
        <result property="description" column="description" jdbcType="VARCHAR" />
        <result property="workNotes" column="work_notes" jdbcType="VARCHAR" />
        <result property="fixNote" column="fix_note" jdbcType="VARCHAR" />
        <result property="causeNote" column="cause_note" jdbcType="VARCHAR" />
        <result property="workAround" column="work_around" jdbcType="VARCHAR" />
        <result property="resolvedBy" column="resolved_by" jdbcType="VARCHAR" />
        <result property="resolvedAt" column="resolved_at" jdbcType="VARCHAR" />
        <result property="openedBy" column="opened_by" jdbcType="VARCHAR" />
        <result property="openedAt" column="opened_at" jdbcType="VARCHAR" />
        <result property="resolutionCode" column="resolution_code" jdbcType="VARCHAR" />
        <result property="rfc" column="rfc" jdbcType="VARCHAR" />
    </resultMap>
	<!-- <select id="findProblemAll" resultMap="ProblemResultMap">        
		SELECT * FROM ext_snow_problem
	</select> -->
    <select id="findProblemByTagAndSysID" resultMap="ProblemResultMap" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_problem WHERE
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>


    <resultMap id="ChangeRequestResultMap" type="com.ibm.zds.ext.database.entity.snow.ChangeRequest">
        <id property="extID" column="ext_id" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="sysID" column="sys_id" jdbcType="VARCHAR" />
        <result property="number" column="number" jdbcType="VARCHAR" />
        <result property="type" column="type" jdbcType="VARCHAR" />
        <result property="category" column="category" jdbcType="VARCHAR" />
        <result property="state" column="state" jdbcType="VARCHAR" />
        <result property="priority" column="priority" jdbcType="VARCHAR" />
        <result property="impact" column="impact" jdbcType="VARCHAR" />
        <result property="urgency" column="urgency" jdbcType="VARCHAR" />
        <result property="risk" column="risk" jdbcType="VARCHAR" />
        <result property="lastUpdated" column="last_updated" jdbcType="VARCHAR" />
        <result property="configurationItem" column="configuration_item" jdbcType="VARCHAR" />
        <result property="assignmentGroup" column="assignment_group" jdbcType="VARCHAR" />
        <result property="assignedTo" column="assigned_to" jdbcType="VARCHAR" />
        <result property="description" column="description" jdbcType="VARCHAR" />
        <result property="shortDescription" column="short_description" jdbcType="VARCHAR" />
        <result property="justification" column="justification" jdbcType="VARCHAR" />
        <result property="implementationPlan" column="implementation_plan" jdbcType="VARCHAR" />
        <result property="riskImpactAnalysis" column="risk_impact_analysis" jdbcType="VARCHAR" />
        <result property="backoutPlan" column="backout_plan" jdbcType="VARCHAR" />
        <result property="testPlan" column="test_plan" jdbcType="VARCHAR" />
        <result property="workNotes" column="work_notes" jdbcType="VARCHAR" />
        <result property="scheduledStart" column="scheduled_start" jdbcType="VARCHAR" />
        <result property="scheduledEnd" column="scheduled_end" jdbcType="VARCHAR" />
        <result property="actualStart" column="actual_start" jdbcType="VARCHAR" />
        <result property="actualEnd" column="actual_end" jdbcType="VARCHAR" />
        <result property="closeCode" column="close_code" jdbcType="VARCHAR" />
        <result property="closeNotes" column="close_notes" jdbcType="VARCHAR" />
    </resultMap>
	<!-- <select id="findChangeRequestAll" resultMap="ChangeRequestResultMap">        
		SELECT * FROM ext_snow_change_request
	</select> -->
    <select id="findChangeRequestByTagAndSysID" resultMap="ChangeRequestResultMap" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_change_request WHERE
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>


    <resultMap id="AffectedCIResultMap" type="com.ibm.zds.ext.database.entity.snow.AffectedCI">
        <id property="extID" column="ext_id" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="sysID" column="sys_id" jdbcType="VARCHAR" />
        <result property="ciItem" column="ci_item" jdbcType="VARCHAR" />
        <result property="ciID" column="ci_id" jdbcType="VARCHAR" />
        <result property="ciCorrelationID" column="ci_correlation_id" jdbcType="VARCHAR" />
        <result property="ciType" column="ci_type" jdbcType="VARCHAR" />
        <result property="task" column="task" jdbcType="VARCHAR" />
        <result property="taskID" column="task_id" jdbcType="VARCHAR" />
        <result property="taskType" column="task_type" jdbcType="VARCHAR" />
    </resultMap>
	<!-- <select id="findAffectedCIAll" resultMap="AffectedCIResultMap">        
		SELECT * FROM ext_snow_affected_ci
	</select> -->
    <select id="findAffectedCIByTagAndSysID" resultMap="AffectedCIResultMap" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_affected_ci WHERE
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>
    <select id="findAffectedCIByTagAndCIItem" resultType="java.util.Map" parameterType="java.lang.String">        
        SELECT * FROM ext_snow_affected_ci as ac
        LEFT JOIN
        (SELECT name,sys_id AS ci_sys_id,tag FROM ext_snow_configuration_item) AS ci
        ON ac.ci_item = ci.ci_sys_id AND ac.tag = ci.tag
        WHERE
        <choose>
            <when test="tag != null and tag !=''">
                ac.tag = #{tag}
            </when>
            <otherwise>
                ac.tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="ciItem != null and ciItem !=''">
                AND ac.ci_item = #{ciItem}
            </when>
        </choose>
	</select>
    <select id="findAffectedCIByTagAndCICorrelationID" resultType="java.util.Map" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_affected_ci AS ac
        LEFT JOIN
        (SELECT name,correlation_id,tag FROM ext_snow_configuration_item) AS ci
        ON ac.ci_correlation_id = ci.correlation_id AND ac.tag = ci.tag
        WHERE
        <choose>
            <when test="tag != null and tag !=''">
                ac.tag = #{tag}
            </when>
            <otherwise>
                ac.tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="ciCorrelationID != null and ciCorrelationID !=''">
                AND ac.ci_correlation_id = #{ciCorrelationID} 
            </when>
        </choose>
	</select>


    <resultMap id="TicketRelationshipResultMap" type="com.ibm.zds.ext.database.entity.snow.TicketRelationship">
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="source" column="source" jdbcType="VARCHAR" />
        <result property="sourceID" column="source_id" jdbcType="VARCHAR" />
        <result property="sourceType" column="source_type" jdbcType="VARCHAR" />
        <result property="target" column="target" jdbcType="VARCHAR" />
        <result property="targetID" column="target_id" jdbcType="VARCHAR" />
        <result property="targetType" column="target_type" jdbcType="VARCHAR" />
        <result property="reverseName" column="reverse_name" jdbcType="VARCHAR" />
    </resultMap>
	<!-- <select id="findTicketRelationshipAll" resultMap="TicketRelationshipResultMap">        
		SELECT * FROM ext_snow_ticket_relationship
	</select> -->
    <select id="findTicketRelationshipByTagAndSysID" resultMap="TicketRelationshipResultMap" parameterType="java.lang.String">        
		SELECT * FROM ext_snow_ticket_relationship WHERE
        <choose>
            <when test="tag != null and tag !=''">
                tag = #{tag}
            </when>
            <otherwise>
                tag = (SELECT tag FROM ext_snow_tag ORDER BY tag_time DESC LIMIT 1)
            </otherwise>
        </choose>
        <choose>
            <when test="sysID != null and sysID !=''">
                AND sys_id = #{sysID}
            </when>
        </choose>
	</select>


    <resultMap id="TagResultMap" type="com.ibm.zds.ext.database.entity.snow.Tag">
        <id property="tag" column="tag" jdbcType="VARCHAR" />
        <result property="version" column="version" jdbcType="VARCHAR" />
        <result property="source" column="source" jdbcType="VARCHAR" />
        <result property="sourceVersion" column="source_version" jdbcType="VARCHAR" />
        <result property="tagTime" column="tag_time" jdbcType="VARCHAR" />
    </resultMap>
	<select id="findTagAll" resultMap="TagResultMap">        
		SELECT * FROM ext_snow_tag ORDER BY tag_time DESC
	</select>
</mapper>   