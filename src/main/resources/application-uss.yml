server:
  http2:
    enabled: true
  port : 8085
  ssl:
    enabled: true
    key-store: ${GATEWAY_SSL_KEYSTORE}
    key-store-password: ${GATEWAY_SSL_PASSWORD}
    keyStoreType: PKCS12
    trust-store: ${GATEWAY_SSL_TRUSTSTORE}
    trust-store-password: ${GATEWAY_SSL_PASSWORD}
    trust-store-type: PKCS12

keycloak:
  route: ${KEYCLOAK_ROUTE}

ratelimit:
  numberOfToken: 1
  resetSec: 60
  limit: 25

spring:
  codec:
    max-in-memory-size: 500KB
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${KEYCLOAK_URI}
  application:
    name: pi-gateway
  cloud:
    gateway:
      defaultFilters:
        - name: CustomRateLimitFilter
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
          filters:
      routes:
        - id: pi-framework
          uri: ${PI_FRAMEWORK_ROUTE}
          predicates:
          - Path=/piFramework/**
          filters:
          - RewritePath=/piFramework,/piFramework/
        - id: rulesEngine
          uri: ${RULES_ENGINE_ROUTE}
          predicates:
          - Path=/pi-rulesengine/**
        - id: scorecardDashboard
          uri: ${DASHBOARD_MLSCC_ROUTE:https://localhost:8095/pi-scorecard/}
          predicates:
          - Path=/pi-scorecard/**
        - id: db2mlssc
          uri: ${DB2MLSCC_ROUTE:https://localhost:8091/pi-db2mlssc/}
          predicates:
          - Path=/pi-db2mlssc/**
        - id: cicsmlssc
          uri: ${CICSMLSCC_ROUTE:https://localhost:8090/pi-cicsmlssc/}
          predicates:
          - Path=/pi-cicsmlssc/**
        - id: mqmlssc
          uri: ${MQSMLSCC_ROUTE:https://localhost:8093/pi-mqmlssc/}
          predicates:
          - Path=/pi-mqmlssc/**
        - id: imsmlssc
          uri: ${IMSMLSCC_ROUTE:https://localhost:8092/pi-imsmlssc/}
          predicates:
          - Path=/pi-imsmlssc/**
        - id: zosmlssc
          uri: ${ZOSMLSCC_ROUTE:https://localhost:8094/pi-zosmlssc/}
          predicates:
          - Path=/pi-zosmlssc/**
        - id: keycloak
          uri: ${KEYCLOAK_ROUTE}
          predicates:
          - Path=/${ZAIOPS_KC_CONTEXT_ROOT}/**
          filters:
          - RewritePath=/${ZAIOPS_KC_CONTEXT_ROOT}$,/${ZAIOPS_KC_CONTEXT_ROOT}/
        - id: redirect-to-kc-admin
          uri: no://op
          predicates:
          - Path=/secadmin/**
          filters:
          - RedirectTo=303, https://${EXTERNAL_GATEWAY_HOST}:${IZOA_GATEWAY_PORT}/${ZAIOPS_KC_CONTEXT_ROOT}/admin/${ZAIOPS_KC_REALM}/console, true
        - id: redirect-to-kc-account
          uri: no://op
          predicates:
          - Path=/account/**
          filters:
          - RedirectTo=303, https://${EXTERNAL_GATEWAY_HOST}:${IZOA_GATEWAY_PORT}/${ZAIOPS_KC_CONTEXT_ROOT}/realms/${ZAIOPS_KC_REALM}/account, true

eureka:
  enabled: false
  client:
    fetchRegistry: false
    registerWithEureka: false
    serviceUrl:
      defaultZone: http://discovery:8761/eureka
  instance:
    preferIpAddress: true
    nonSecurePortEnabled: false
    securePortEnabled: true
