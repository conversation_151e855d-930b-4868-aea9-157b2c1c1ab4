<!-- *******************************************************************************
# Licensed Materials - Property of IBM
# 5698-ANA (C) Copyright IBM Corp. 2021
# All Rights Reserved
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
# *******************************************************************************
-->
<Configuration status="INFO" monitorInterval="30">
    <Properties>
       	<Property name="LOG_PATTERN">%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ} [%t] %-5level %logger{36} - %M %msg%n</Property>
    </Properties>
    <Appenders>
        <Console name="LogToConsole" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}" />
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="com.ibm.zsystem.zmanaged.piGateway" level="info" additivity="false">
            <AppenderRef ref="LogToConsole"/>
        </Logger>
        <Logger name="org.springframework.web" level="info" additivity="false">
            <AppenderRef ref="LogToConsole"/>
        </Logger>
        <Logger name="org.springframework.cloud" level="info" additivity="false">
            <AppenderRef ref="LogToConsole"/>
        </Logger>        
        <Logger name="org.springframework.cloud.gateway" level="info" additivity="false">
            <AppenderRef ref="LogToConsole"/>
        </Logger> 
        <Logger name="org.springframework.http.server.reactive" level="info" additivity="false">
            <AppenderRef ref="LogToConsole"/>
        </Logger> 
        <Logger name="org.springframework.web.reactive" level="info" additivity="false">
            <AppenderRef ref="LogToConsole"/>
        </Logger> 
        <Logger name="org.springframework.boot.autoconfigure.web" level="info" additivity="false">
            <AppenderRef ref="LogToConsole"/>
        </Logger>             
		<Logger name="reactor.netty" level="info" additivity="false">
            <AppenderRef ref="LogToConsole"/>
        </Logger>         
        <Root level="info">
            <AppenderRef ref="LogToConsole"/>
        </Root>
    </Loggers>
</Configuration>
