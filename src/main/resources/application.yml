server:
  http2:
    enabled: true
  port : 8085
  ssl:
    enabled: true
    key-store: /ssl/zoasvc.ks
    key-store-password: ${GW_PASS:gwpass}
    keyStoreType: PKCS12
    trust-store: /ssl/zoasvc.ts
    trust-store-password: ${GW_PASS:gwpass}
    trust-store-type: PKCS12

keycloak:
  route: ${KEYCLOAK_ROUTE:http://auth:8080/auth/}

ratelimit:
  numberOfToken: 1
  resetSec: 1
  limit: 10

spring:
  codec:
    max-in-memory-size: 500KB
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${KEYCLOAK_URI:http://auth:8080/auth/realms/IzoaKeycloak/protocol/openid-connect/certs}
  application:
    name: pi-gateway
  cloud:
    gateway:
      defaultFilters:
        - name: CustomRateLimitFilter
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
          filters:
      routes:
        - id: ensemble
          uri: http://ensembleui:8080/ensemble
          predicates:
          - Path=/ensemble/**
          filters:
          - RewritePath=/ensemble,/ensemble/
        - id: pi-framework
          uri: ${PI_FRAMEWORK_ROUTE:http://piserver:9446/piFramework/}
          predicates:
          - Path=/piFramework/**
          filters:
          - RewritePath=/piFramework,/piFramework/
        - id: zdsApi
          uri: http://${ZRDDS_API_HOST}:8888/zrdds/
          predicates:
          - Path=/zrdds/**
        - id: zdsCore
          uri: http://${ZRDDS_CORE_HOST}:8080/api/
          predicates:
          - Path=/zrddsapi/**
        - id: zdsUi
          uri: http://${TOPOLOGY_UI}:4000/zrdds-ui/
          predicates:
          - Path=/zrdds-ui/**
        - id: kafkabridge-consumers
          uri: http://${ZRDDS_KB_HOST}:8081/consumers
          predicates:
          - Path=/consumers/**
        - id: kafkabridge-topics
          uri: http://${ZRDDS_KB_HOST}:8081/topics
          predicates:
          - Path=/topics/**
        - id: zdap
          uri: ${ZDAP_ROUTE:https://dashboards:5601/insights/}
          predicates:
          - Path=/insights/**
          filters:
          - RewritePath=/insights,/insights/
        - id: keycloak
          uri: ${KEYCLOAK_ROUTE:http://auth:8080/auth/}
          predicates:
          - Path=/${ZAIOPS_KC_CONTEXT_ROOT}/**
          filters:
          - RewritePath=/${ZAIOPS_KC_CONTEXT_ROOT}$,/${ZAIOPS_KC_CONTEXT_ROOT}/
        - id: redirect-to-kc-admin
          uri: no://op
          predicates:
          - Path=/secadmin/**
          filters:
          - RedirectTo=303, https://${EXTERNAL_GATEWAY_HOST}:${IZOA_GATEWAY_PORT}/${ZAIOPS_KC_CONTEXT_ROOT}/admin/${ZAIOPS_KC_REALM}/console, true
        - id: redirect-to-kc-account
          uri: no://op
          predicates:
          - Path=/account/**
          filters:
          - RedirectTo=303, https://${EXTERNAL_GATEWAY_HOST}:${IZOA_GATEWAY_PORT}/${ZAIOPS_KC_CONTEXT_ROOT}/realms/${ZAIOPS_KC_REALM}/account, true
      x-forwarded:
        for-enabled: true
        host-enabled: true
        port-enabled: true
        proto-enabled: true
        prefix-enabled: true
      trusted-proxies: .*

eureka:
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: ${DISCOVERY_ROUTE:http://discovery:8761/eureka}
  instance:
    preferIpAddress: true

management:
  endpoint:
    gateway:
      access: unrestricted
  endpoints:
    web:
      exposure:
        include: gateway