<?xml version="1.0" encoding="UTF-8" ?>
<NamingRules modelVersion="2.9.2">

    <CorrelationPolicy name="cpc" suffix="ZSeriesComputerSystem">
        <NamingRule name="cpc.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="cpc.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-DLAId"/>
        </NamingRule>
        <NamingRule name="cpc.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-name-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="sysplex" suffix="Sysplex">
        <NamingRule name="sysplex.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="sysplex.byDLAId" priority="1">
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="DLAId"/>
        </NamingRule>
        <NamingRule name="sysplex.byZVSFormat" priority="2">
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="name-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="lpar" suffix="LPAR">
        <NamingRule name="lpar.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="lpar.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-DLAId"/>
        </NamingRule>
        <NamingRule name="lpar.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparId" required="true"/>
            <RequiredField name="logicalPartitionHost" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparId-logicalPartitionHost-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="zosServer" suffix="ZOS">
        <NamingRule name="zosServer.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="zosServer.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="zosServer.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="db2Subsystem" suffix="DB2Subsystem">
        <NamingRule name="db2Subsystem.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="db2Subsystem.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="db2Subsystem.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="db2DataSharingGroup" suffix="DB2DataSharingGroup">
        <NamingRule name="db2DataSharingGroup.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="db2DataSharingGroup.byDLAId" priority="1">
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="DLAId"/>
        </NamingRule>
        <NamingRule name="db2DataSharingGroup.byZVSFormat" priority="2">
            <RequiredField name="name" required="true"/>
            <RequiredField name="sysplexName" required="true"/>
            <CorrelationFormat value="name-sysplexName-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="db2Database" suffix="Db2Database">
        <NamingRule name="db2Database.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="db2Database.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="db2Database.byZVSFormatWithSubsystem" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="subsystemName" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-subsystemName-smfId-suffix"/>
        </NamingRule>
        <NamingRule name="db2Database.byZVSFormatWithSharingGroup" priority="3">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="sysplexName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="sharingGroupName" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-sharingGroupName-sysplexName-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="db2StoredProcedure" suffix="Db2StoredProcedure">
        <NamingRule name="db2StoredProcedure.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="db2StoredProcedure.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="db2StoredProcedure.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="subsystemName" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-subsystemName-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="cicsRegion" suffix="CICSRegion">
        <NamingRule name="cicsRegion.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="cicsRegion.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="cicsRegion.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="cicsTransaction" suffix="CICSTransaction">
        <NamingRule name="cicsTransaction.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="cicsTransaction.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="cicsTransaction.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="cicsRegionName" required="true"/>
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-cicsRegionName-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="imsSubsystem" suffix="IMSSubsystem">
        <NamingRule name="imsSubsystem.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="imsSubsystem.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="imsSubsystem.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="imsDatabase" suffix="IMSDatabase">
        <NamingRule name="imsDatabase.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="imsDatabase.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="imsDatabase.byZVSFormatWithSubsystem" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="subsystemName" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-subsystemName-smfId-suffix"/>
        </NamingRule>
        <NamingRule name="imsDatabase.byZVSFormatWithSharingGroup" priority="3">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="sysplexName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="sharingGroupName" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-sharingGroupName-sysplexName-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="imsTransaction" suffix="IMSTransaction">
        <NamingRule name="imsTransaction.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="imsTransaction.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="imsTransaction.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="subsystemName" required="true"/>
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-subsystemName-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="mqManager" suffix="MQSubsystem">
        <NamingRule name="mqManager.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="mqManager.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="mqManager.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="mqDataSharingGroup" suffix="MQDataSharingGroup">
        <NamingRule name="mqDataSharingGroup.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="mqDataSharingGroup.byDLAId" priority="1">
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="DLAId"/>
        </NamingRule>
        <NamingRule name="mqDataSharingGroup.byZVSFormat" priority="2">
            <RequiredField name="name" required="true"/>
            <RequiredField name="sysplexName" required="true"/>
            <CorrelationFormat value="name-sysplexName-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="mqLocalQueue" suffix="MQLocalQueue">
        <NamingRule name="mqLocalQueue.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="mqLocalQueue.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="mqLocalQueue.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="mqManager" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-mqManager-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="mqRemoteQueue" suffix="MQRemoteQueue">
        <NamingRule name="mqRemoteQueue.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="mqRemoteQueue.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="mqRemoteQueue.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="mqManager" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-mqManager-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="mqModelQueue" suffix="MQModelQueue">
        <NamingRule name="mqModelQueue.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="mqModelQueue.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="mqModelQueue.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="mqManager" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-mqManager-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <CorrelationPolicy name="mqAliasQueue" suffix="MQAliasQueue">
        <NamingRule name="mqAliasQueue.byCorrelationId" priority="0">
            <RequiredField name="correlationId" required="true"/>
            <CorrelationFormat value="correlationId"/>
        </NamingRule>
        <NamingRule name="mqAliasQueue.byDLAId" priority="1">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="DLAId" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-DLAId"/>
        </NamingRule>
        <NamingRule name="mqAliasQueue.byZVSFormat" priority="2">
            <RequiredField name="manufacturer" required="true"/>
            <RequiredField name="model" required="true"/>
            <RequiredField name="serialNumber" required="true"/>
            <RequiredField name="lparName" required="true"/>
            <RequiredField name="sysplexLabel" required="true"/>
            <RequiredField name="smfId" required="true"/>
            <RequiredField name="name" required="true"/>
            <RequiredField name="mqManager" required="true"/>
            <CorrelationFormat value="manufacturer-model-serialNumber-lparName-smfId-sysplexLabel-name-mqManager-smfId-suffix"/>
        </NamingRule>
    </CorrelationPolicy>

    <Mapping source="ZVS.cpc" policy="cpc"/>
    <Mapping source="ZVS.sysplex" policy="sysplex"/>
    <Mapping source="ZVS.zosServer" policy="zosServer"/>
    <Mapping source="ZVS.lpar" policy="lpar"/>
    <Mapping source="ZVS.db2Subsystem" policy="db2Subsystem"/>
    <Mapping source="ZVS.db2DataSharingGroup" policy="db2DataSharingGroup"/>
    <Mapping source="ZVS.db2Database" policy="db2Database"/>
    <Mapping source="ZVS.db2StoredProcedure" policy="db2StoredProcedure"/>
    <Mapping source="ZVS.cicsRegion" policy="cicsRegion"/>
    <Mapping source="ZVS.cicsTransaction" policy="cicsTransaction"/>
    <Mapping source="ZVS.imsSubsystem" policy="imsSubsystem"/>
    <Mapping source="ZVS.imsDatabase" policy="imsDatabase"/>
    <Mapping source="ZVS.imsTransaction" policy="imsTransaction"/>
    <Mapping source="ZVS.mqManager" policy="mqManager"/>
    <Mapping source="ZVS.mqDataSharingGroup" policy="mqDataSharingGroup"/>
    <Mapping source="ZVS.mqLocalQueue" policy="mqLocalQueue"/>
    <Mapping source="ZVS.mqRemoteQueue" policy="mqRemoteQueue"/>
    <Mapping source="ZVS.mqModelQueue" policy="mqModelQueue"/>
    <Mapping source="ZVS.mqAliasQueue" policy="mqAliasQueue"/>

</NamingRules>