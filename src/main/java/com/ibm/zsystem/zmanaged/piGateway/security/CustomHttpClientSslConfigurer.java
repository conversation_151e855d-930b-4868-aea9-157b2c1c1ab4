package com.ibm.zsystem.zmanaged.piGateway.security;

import reactor.netty.http.client.HttpClient;
import reactor.netty.http.Http11SslContextSpec;
import reactor.netty.http.Http2SslContextSpec;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import org.springframework.cloud.gateway.config.HttpClientProperties;
import org.springframework.cloud.gateway.config.HttpClientSslConfigurer;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CustomHttpClientSslConfigurer {

	@Bean
  @Primary
  public HttpClientSslConfigurer noopHttpClientSslConfigurer(HttpClientProperties httpClientProperties,
      final ServerProperties serverProperties) {
    return new HttpClientSslConfigurer(httpClientProperties.getSsl(), serverProperties, null) {
      @Override
      public HttpClient configureSsl(HttpClient client) {
        HttpClientProperties.Ssl ssl = httpClientProperties.getSsl();
        if(serverProperties.getHttp2().isEnabled()) {
          return client.secure(sslContextSpec -> {
            Http2SslContextSpec clientSslCtxt = Http2SslContextSpec.forClient()
              .configure(builder -> builder.trustManager(InsecureTrustManagerFactory.INSTANCE));
            sslContextSpec.sslContext(clientSslCtxt).handshakeTimeout(ssl.getHandshakeTimeout())
              .closeNotifyFlushTimeout(ssl.getCloseNotifyFlushTimeout())
              .closeNotifyReadTimeout(ssl.getCloseNotifyReadTimeout());
          });
        }
        if(!serverProperties.getHttp2().isEnabled()) {
          return client.secure(sslContextSpec -> {
            Http11SslContextSpec clientSslCtxt = Http11SslContextSpec.forClient()
              .configure(builder -> builder.trustManager(InsecureTrustManagerFactory.INSTANCE));
            sslContextSpec.sslContext(clientSslCtxt).handshakeTimeout(ssl.getHandshakeTimeout())
              .closeNotifyFlushTimeout(ssl.getCloseNotifyFlushTimeout())
              .closeNotifyReadTimeout(ssl.getCloseNotifyReadTimeout());
          });
        }
        return super.configureSsl(client);
      }
    };
  }

}
