package com.ibm.zsystem.zmanaged.piGateway.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;

@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {

	@Bean
	public SecurityWebFilterChain filterChain1(ServerHttpSecurity http) throws Exception {
		http.authorizeExchange(auth -> auth.pathMatchers("/actuator/**", "/*/ui/**", "/auth/**", "/piFramework/**",
				"/pi-db2mlssc/**", "/pi-cicsmlssc/**", "/pi-mqmlssc/**", "/pi-rulesengine/**", "/zrdds/**",
				"/zrddsapi/**", "/zrdds-ui/**", "/insights/**", "/ensembleapi/**", "/ensemble/**", "/secadmin/**",
        "/account/**").permitAll())
				.authorizeExchange(exchanges -> exchanges.anyExchange().authenticated())
				.oauth2ResourceServer(auth -> auth.jwt(Customizer.withDefaults()))
				.csrf(csrf -> csrf.disable());
		return http.build();
	}
}
