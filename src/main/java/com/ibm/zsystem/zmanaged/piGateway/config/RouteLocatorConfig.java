package com.ibm.zsystem.zmanaged.piGateway.config;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.gateway.filter.factory.AddRequestHeaderGatewayFilterFactory;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder.Builder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpCookie;
import org.springframework.http.ResponseCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.MultiValueMap;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.zsystem.zmanaged.piGateway.filter.CustomRateLimitFilter;

import reactor.core.publisher.Mono;

@Configuration
public class RouteLocatorConfig {

	private static final Log log = LogFactory.getLog(RouteLocatorConfig.class);

	@Value("${keycloak.route}")
	private String keycloakRoute;
/*
 * 06/20/2023: cnestor - Disabled to accommodate the refresh token flow in Keycloak-angular.
 */
//	@Bean
//	public RouteLocator gatewayRouteLocator(RouteLocatorBuilder builder, ObjectMapper objectMapper,
//			CustomRateLimitFilter ratelimitfilter) {
//		log.trace("entry ----> gatewayRouteLocator");
//		return builder.routes().route("my-route-id", p -> p.path("/auth/**")
//				.filters(f -> 
////				f.removeResponseHeader("Content-Length")
//				f.modifyResponseBody(String.class, String.class, (webExchange, originalBody) -> {
//					ServerHttpResponse response = webExchange.getResponse();              
//					ServerHttpRequest request = webExchange.getRequest();
//					String requestURI = request.getURI().toString();
//					log.debug("Request path " + request.getPath().value());
//					log.debug("Request contextPath " + request.getPath().contextPath().value());
//					try {
//						log.debug("Response originalBody " + originalBody);
//						if (requestURI.contains("openid-connect/logout")) {
//							log.debug("Add empty refresh token cookie.");
//							response.addCookie(ResponseCookie.from("refreshToken", "").maxAge(0)
//									.path("/auth/realms/IzoaKeycloak/protocol/openid-connect/").httpOnly(true).build());
//						} else if (requestURI.contains("openid-connect/token")) {
//							log.debug("Add refresh token cookie.");
//							if (originalBody != null) {
//								try {
//									// ObjectMapper objectMapper = new ObjectMapper();
//									Map<String, Object> map = objectMapper.readValue(originalBody, Map.class);
//									String refresh_token = (String) map.get("refresh_token");
//									response.addCookie(ResponseCookie.from("refreshToken", refresh_token)
//											.maxAge(2592000).path("/auth/realms/IzoaKeycloak/protocol/openid-connect").httpOnly(true).build());
//									map.remove("refresh_token");
//									String modifiedBody = objectMapper.writeValueAsString(map);
//									log.debug("Response modifiedBody " + modifiedBody);
//									return Mono.just(modifiedBody);
//								} catch (Exception ex) {
//									log.error("1. json process fail", ex);
//									return Mono.error(new Exception("1. json process fail", ex));
//								}
//							}
//						}
//					} catch (Exception e) {
//						log.error("Error occured in CustomFilter post request.", e);
//						return Mono.error(new Exception("1. json process fail", e));
//					}
//					if (originalBody != null) {
//						return Mono.just(originalBody);
//					} else {
//						return Mono.empty();
//					}
//				}).modifyRequestBody(String.class, String.class, (webExchange, originalBody) -> {
//					ServerHttpRequest request = webExchange.getRequest();
//					String requestURI = request.getURI().toString();
//					log.debug("Request originalBody " + originalBody);
//					String grantType = getRequestFormData(originalBody, "grant_type");
//					log.debug("Request grant_type " + grantType);
//					if ((grantType != null && grantType.equals("refresh_token")) || requestURI.contains("logout")) {
//						try {
//							String cookieValue = extractCookie(request, "refreshToken");
//							String modifiedBody = createFormData(requestURI, cookieValue);
//							log.debug("Request modifiedBody " + modifiedBody);
//							return Mono.just(modifiedBody);
//						} catch (Exception e) {
//							log.error("Error occured in CustomFilter post request.", e);
//							return Mono.error(new Exception("1. json process fail", e));
//						}
//					}
//					if (originalBody != null) {
//						return Mono.just(originalBody);
//					} else {
//						return Mono.empty();
//					}
//				})
////				.setResponseHeader("X-Frame-Options","SAMEORIGIN")
//						.filter(ratelimitfilter.apply(new CustomRateLimitFilter.Config()))
//				).uri(keycloakRoute)).build();
//	}

	private String getRequestFormData(String body, String name) {
		if (body != null) {
			Map<String, String> bodyMap = Arrays.stream(body.split("&")).map(s -> s.split("=", 2))
					.filter(s -> s.length >= 2)
					.collect(Collectors.toMap(s -> s[0], s -> s[1]));

			return bodyMap.get(name);
		}
		return null;
	}

	private String extractCookie(ServerHttpRequest req, String name) {
		final MultiValueMap<String, HttpCookie> cookies = req.getCookies();
		log.debug("httpCookie.size: " + cookies.size());
		for (Map.Entry<String, List<HttpCookie>> entry : cookies.entrySet()) {
			for (HttpCookie httpCookie : entry.getValue()) {
				log.debug("httpCookie.getName(): " + httpCookie.getName());
				if (httpCookie.getName().equalsIgnoreCase(name)) {
					log.debug("httpCookie.getValue(): " + httpCookie.getValue());
					return httpCookie.getValue();
				}
			}
		}
		return null;
	}

	private String createFormData(String requestURI, String cookieValue) {
		String formData = "";
		if (requestURI.contains("auth/realms/IzoaKeycloak/protocol/openid-connect/logout")) {
			formData = String.format("client_id=%s&refresh_token=%s", "zoa-client", cookieValue);
		} else {
			formData = String.format("grant_type=%s&client_id=%s&refresh_token=%s", "refresh_token", "zoa-client",
					cookieValue);
		}
		return formData;
	}
}
