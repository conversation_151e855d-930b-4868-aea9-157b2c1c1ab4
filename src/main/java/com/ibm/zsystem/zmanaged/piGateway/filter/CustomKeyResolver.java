package com.ibm.zsystem.zmanaged.piGateway.filter;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.support.ipresolver.XForwardedRemoteAddressResolver;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

import reactor.core.publisher.Mono;

@Component
public class CustomKeyResolver implements KeyResolver {

	private static final Logger logger = LogManager.getLogger(CustomKeyResolver.class);

	@Override
	public Mono<String> resolve(ServerWebExchange exchange) {
		logger.traceEntry("Parameters: {}", exchange);

		// Parses the client address from the X-Forwarded-For header.
		// If header is not present, falls back to RemoteAddressResolver and
		// ServerHttpRequest.getRemoteAddress().
		String clientIp = XForwardedRemoteAddressResolver.maxTrustedIndex(1).resolve(exchange).getAddress()
				.getHostAddress();
		logger.trace("clientIp: {}", clientIp);

		// Returns a structured representation of the full request path up to but not
		// including the query.
		String path = exchange.getRequest().getPath().toString();
		logger.trace("path: {}", path);

		// Returns the decoded query component of this URI.
		String queryString = exchange.getRequest().getURI().getQuery();
		logger.trace("queryString: {}", queryString);

		String key = clientIp + "|" + path + (StringUtils.hasText(queryString) ? "?" + queryString : "");
		logger.traceExit("results: {}", key);
		return Mono.just(key);
	}
}