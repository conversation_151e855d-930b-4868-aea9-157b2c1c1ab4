package com.ibm.zsystem.zmanaged.piGateway.filter;

import java.time.Duration;
import java.util.HashMap;
import java.util.function.Supplier;

import javax.cache.CacheManager;
import javax.cache.Caching;
import javax.cache.configuration.CompleteConfiguration;
import javax.cache.configuration.MutableConfiguration;
import javax.cache.spi.CachingProvider;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.ratelimit.AbstractRateLimiter;
import org.springframework.stereotype.Component;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Bucket4j;
import io.github.bucket4j.BucketConfiguration;
import io.github.bucket4j.ConsumptionProbe;
import io.github.bucket4j.grid.GridBucketState;
import io.github.bucket4j.grid.ProxyManager;
import io.github.bucket4j.grid.jcache.JCache;
import reactor.core.publisher.Mono;

@Component
public class CustomRateLimiter extends AbstractRateLimiter<CustomRateLimiter.Config> {

	private static final Log log = LogFactory.getLog(CustomRateLimiter.class);

	public final static String GATEWAY_RATE_LIMITING_CACHE_NAME = "gateway-rate-limiting";
	private static final String CONFIGURATION_PROPERTY_NAME = "rate-limiter";

	@Value("${ratelimit.numberOfToken}")
	private long numberOfToken;
	
	@Value("${ratelimit.resetSec}")
	private long ratelimitResetSec;
	
	@Value("${ratelimit.limit}")
	private long ratelimitLimit;
	
	private ProxyManager<String> buckets;
	private javax.cache.Cache<String, GridBucketState> cache;

	protected CustomRateLimiter() {
		super(Config.class, CONFIGURATION_PROPERTY_NAME, null);
		
		CachingProvider cachingProvider = Caching.getCachingProvider("org.ehcache.jsr107.EhcacheCachingProvider");
		CacheManager cacheManager = cachingProvider.getCacheManager();
		CompleteConfiguration<String, GridBucketState> config = new MutableConfiguration<String, GridBucketState>()
				.setTypes(String.class, GridBucketState.class);

		this.cache = cacheManager.createCache(GATEWAY_RATE_LIMITING_CACHE_NAME, config);
		this.buckets = Bucket4j.extension(JCache.class).proxyManagerForCache(cache);
	}

	@Override
	public Mono<Response> isAllowed(String routeId, String id) {
		log.trace("--> entry isAllowed");
		if (buckets == null) {
			log.error("Proxy manager multi bucket not initialized");
			throw new IllegalArgumentException("Proxy manager multi bucket not initialized");
		}

		Bucket requestBucket = null;

		try {
			requestBucket = this.buckets.getProxy(id, getConfigSupplier());
			ConsumptionProbe probe = requestBucket.tryConsumeAndReturnRemaining(numberOfToken);
			
			if (probe.isConsumed()) {
				Response response = new Response(true, this.getHeaders(probe, id));
				return Mono.just(response);
			}

			return Mono.just(new Response(false, this.getHeaders(probe, id)));

		} catch (Exception e) {
			log.error("Rate limiting failed: " + e.getMessage(), e);
		}

		return Mono.just(new Response(true, new HashMap<String, String>()));
	}

	private Supplier<BucketConfiguration> getConfigSupplier() {
		return () -> {
			return Bucket4j.configurationBuilder().addLimit(Bandwidth.simple(ratelimitLimit, Duration.ofSeconds(ratelimitResetSec))).build();
		};
	}

	private HashMap<String, String> getHeaders(ConsumptionProbe probe, String id) {

		final HashMap<String, String> headers = new HashMap<>();
		headers.put("X-RateLimit-Limit", String.valueOf(ratelimitLimit));
		headers.put("X-RateLimit-Remaining", String.valueOf(probe.getRemainingTokens()));
		headers.put("X-RateLimit-Reset", String.valueOf(probe.getNanosToWaitForRefill()));
		return headers;
	}

	public static class Config {
	}

}
