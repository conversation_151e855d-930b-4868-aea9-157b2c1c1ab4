package com.ibm.zsystem.zmanaged.piGateway.filter;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RateLimiter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import reactor.core.publisher.Mono;

@Component
public class CustomRateLimitFilter extends AbstractGatewayFilterFactory<CustomRateLimitFilter.Config> {

	private static final Log log = LogFactory.getLog(CustomRateLimitFilter.class);
	public final static int RATELIMIT_ORDER = -1;

	private final RateLimiter<?> rateLimiter;
	private final KeyResolver keyResolver;

	public CustomRateLimitFilter(final CustomRateLimiter rateLimiter, final CustomKeyResolver keyResolver) {
		super(Config.class);
		this.rateLimiter = rateLimiter;
		this.keyResolver = keyResolver;
	}

	@Override
	public GatewayFilter apply(Config config) {
		
		return new OrderedGatewayFilter((exchange, chain) -> {

			Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
			log.trace("Route ID: " + route.getId());
			
			return keyResolver.resolve(exchange).flatMap(key -> {
				log.trace("Key " + key);
				if (key == null || key.isEmpty()) {
					return Mono.error(new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY));
				}
				Mono<RateLimiter.Response> result = rateLimiter.isAllowed(route.getId(), key);
				return result.flatMap(response -> {
					
					log.trace("IsAllowed: " + response.isAllowed()+"  GetHeaders: " + response.getHeaders() );
					
					
					response.getHeaders().forEach((k, v) -> exchange.getResponse().getHeaders().add(k, v));
					if (response.isAllowed()) {
						return chain.filter(exchange);
					}
					return Mono.error(new ResponseStatusException(HttpStatus.TOO_MANY_REQUESTS));
				});
			});
		}, Ordered.HIGHEST_PRECEDENCE);
		//}, RATELIMIT_ORDER);
	}

	
	public static class Config {
	}
}
