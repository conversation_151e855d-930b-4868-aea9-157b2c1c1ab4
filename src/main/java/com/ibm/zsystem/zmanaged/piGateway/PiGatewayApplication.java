package com.ibm.zsystem.zmanaged.piGateway;
import java.io.IOException;
import jakarta.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import com.ibm.zsystem.zmanaged.piGateway.security.ServerConfig;
import com.ibm.zsystem.zmanaged.piGateway.security.Ssl;

@EnableConfigurationProperties
@SpringBootApplication
public class PiGatewayApplication {

	@Autowired
	private ServerConfig config;

	@Value("${server.ssl.keyStoreType}")
	protected String keyStoreType;
	
	private static final Logger logger = LoggerFactory.getLogger(PiGatewayApplication.class);

	public static void main(String[] args) throws IOException {
		SpringApplication.run(PiGatewayApplication.class, args);
	}

    @PostConstruct
    void postConstruct() throws IOException {
		Ssl ssl = config.getSsl();
    //logger.debug("TrustsTore/Type: " +  ssl.getTrustStore() + " / " + keyStoreType);
    logger.debug("Configured trust store and type: " +  ssl.getTrustStore() + " / " + keyStoreType);
		System.setProperty("javax.net.ssl.trustStore", ssl.getTrustStore());
    logger.debug("Registered trust store: " + System.getProperty("javax.net.ssl.trustStore"));
		System.setProperty("javax.net.ssl.trustStorePassword", ssl.getTrustStorePassword());
    logger.debug("Registered trust store password: " + System.getProperty("javax.net.ssl.trustStorePassword"));
		System.setProperty("javax.net.ssl.keyStoreType", keyStoreType);
    logger.debug("Registered key store type: " + System.getProperty("javax.net.ssl.keyStoreType"));
    System.setProperty("javax.net.ssl.trustStoreType", keyStoreType);
    logger.debug("Registered trust store type: " + System.getProperty("javax.net.ssl.trustStoreType"));
	}

}
