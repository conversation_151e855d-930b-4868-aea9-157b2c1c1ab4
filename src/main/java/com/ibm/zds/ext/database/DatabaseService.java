/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.database;

import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;


public class DatabaseService {

    private static final Logger LOG = LoggerFactory.getLogger(DatabaseService.class);

    private volatile static DatabaseService databaseService;

    private SqlSessionFactory sqlSessionFactory = null;


    public DatabaseService() {
        try {
            // load mybatis config file，and create SqlSessionFactory
            String resource = "db/MybatisConfig.xml";
            InputStream inputStream = Resources.getResourceAsStream(resource);
            this.sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
        } catch (IOException e) {
            LOG.error("Create SqlSessionFactory error:", e);
            System.exit(0);
        }
    }

    public static DatabaseService getDatabaseService() {
        if (databaseService == null) {
            synchronized (DatabaseService.class) {
                if (databaseService == null) {
                    databaseService = new DatabaseService();
                }
            }
        }
        return databaseService;
    }

    public SqlSession getSqlSession() {
        return this.sqlSessionFactory.openSession();
    }

    public void closeSqlSession(SqlSession sqlSession) {
        if (sqlSession != null) {
            sqlSession.close();
        }
    }
}