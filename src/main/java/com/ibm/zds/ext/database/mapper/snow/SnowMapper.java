/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.database.mapper.snow;

import com.ibm.zds.ext.database.entity.snow.*;

import java.util.List;
import java.util.Map;

public interface SnowMapper {


    List<ConfigurationItem> findConfigurationItemByTagAndSysID(String tag, String sysID);


    List<User> findUserByTagAndSysID(String tag, String sysID);


    List<Group> findGroupByTagAndSysID(String tag, String sysID);


    List<Incident> findIncidentByTagAndSysID(String tag, String sysID);


    List<Service> findServiceByTagAndSysID(String tag, String sysID);


    List<Problem> findProblemByTagAndSysID(String tag, String sysID);


    List<ChangeRequest> findChangeRequestByTagAndSysID(String tag, String sysID);


    List<AffectedCI> findAffectedCIByTagAndSysID(String tag, String sysID);

    List<Map<String, String>> findAffectedCIByTagAndCIItem(String tag, String ciItem);

    List<Map<String, String>> findAffectedCIByTagAndCICorrelationID(String tag, String ciCorrelationID);


    List<TicketRelationship> findTicketRelationshipByTagAndSysID(String tag, String sysID);


    List<Tag> findTagAll();
}
