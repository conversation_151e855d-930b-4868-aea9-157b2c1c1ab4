/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.BiPredicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public enum StringCompare implements BiPredicate<Object, Object> {
    wildcard {
        @Override
        public boolean test(final Object first, final Object second) {
            final Logger log = LoggerFactory.getLogger(StringCompare.class);
            if (first == null) {
                return false;
            }

            try {
                String str = first.toString();
                String regex = second.toString();

                Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(str);

                return matcher.matches();
            } catch (Exception e) {
                log.error(e.getMessage());
                // Skip invalid Regular Expressions
                log.warn("Invalid Regular Expression in StringCompare: " + second);
                return false;
            }
        }
    }
}
