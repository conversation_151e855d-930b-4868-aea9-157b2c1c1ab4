package com.ibm.zds.ext.api.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

@Configuration
@Component
public class EnvConfigUtils {

    @Getter
    @Setter
    @Value("${core.api.token}")
    protected String coreApiToken;
    @Getter
    @Setter
    @Value("${api.token}")
    protected String apiToken;
    @Getter
    @Setter
    @Value("${core.url}")
    protected String coreUrl;

    public EnvConfigUtils() {
    }
}
