/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ErrorBody {
    @JsonProperty("_error")
    private ErrorInfo _error = new ErrorInfo();

    @JsonProperty("message")
    private String message = "";

    public ErrorInfo get_error() {
        return _error;
    }

    public void set_error(Exception e) {
        this._error.setLevel("error");
        if (e.getMessage() != null) {
            this._error.setMessage(e.getMessage());
        }
        if (e.getCause() != null) {
            this._error.setDescription(e.getCause().getMessage());
        }
        if (e.getClass() != null) {
            this._error.setErrClass(e.getClass().toString());
        }
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public class ErrorInfo {
        @JsonProperty("level")
        private String level = "";

        @JsonProperty("message")
        private String message = "";

        @JsonProperty("description")
        private String description = "";

        @JsonProperty("errClass")
        private String errClass = "";

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getErrClass() {
            return errClass;
        }

        public void setErrClass(String errClass) {
            this.errClass = errClass;
        }
    }
}
