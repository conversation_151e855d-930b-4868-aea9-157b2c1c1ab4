package com.ibm.zds.ext.api.utils;

import com.google.gson.*;

import java.lang.reflect.Type;


public class GsonUtils {

    //thread-safe
    private static final Gson GSON;
    private static final Gson GSON_NULL; // with null value

    static {
        GSON = new GsonBuilder().enableComplexMapKeySerialization()     //if map key is complex object, need to enable this
                .setDateFormat("yyyy-MM-dd HH:mm:ss")       //set date format
                .disableHtmlEscaping()      //avoid garbled
                .create();
        GSON_NULL = new GsonBuilder().enableComplexMapKeySerialization()        //if map key is complex object, need to enable this
                .serializeNulls()       // when value is null or empty, still covert it
                .setDateFormat("yyyy-MM-dd HH:mm:ss")       //set date format
                .disableHtmlEscaping()      // avoid garbled
                .create();
    }

    //get gson
    public static Gson getGson() {
        return GSON;
    }

    //get gson which write null value
    public static Gson getWriteNullGson() {
        return GSON_NULL;
    }


    /**
     * return json string from object, without null and empty value
     */
    public static String toJsonString(Object object) {
        return GSON.toJson(object);
    }

    /**
     * return json string from object, with null and empty value
     */
    public static String toJsonStringWithNull(Object object) {
        return GSON_NULL.toJson(object);
    }


    /**
     * convert json to object
     *
     * @param json     json string
     * @param classOfT object type
     * @param <T>
     * @return
     */
    public static <T> T strToJavaBean(String json, Class<T> classOfT) {
        return GSON.fromJson(json, classOfT);
    }

    /**
     * convert json to object
     * new TypeToken<List<T>>() {}.getType()
     * new TypeToken<Map<String, T>>() {}.getType()
     * new TypeToken<List<Map<String, T>>>() {}.getType()
     */
    public static <T> T fromJson(String json, Type typeOfT) {
        return GSON.fromJson(json, typeOfT);
    }


    /**
     * convert jsonElement to object
     * new TypeToken<List<T>>() {}.getType()
     * new TypeToken<Map<String, T>>() {}.getType()
     * new TypeToken<List<Map<String, T>>>() {}.getType()
     */
    public static <T> T fromJson(JsonElement json, Type typeOfT) {
        return GSON.fromJson(json, typeOfT);
    }

//    /**
//     * convert json to list object
//     * @param gsonString
//     * @param typeOfT
//     * @return
//     */
//    public static <T> List<T> strToList(String gsonString, Type typeOfT) {
//        return GSON.fromJson(gsonString, typeOfT);
//    }
//
//    /**
//     * convert json to object which contain map
//     * @param gsonString
//     * @return
//     */
//    public static <T> List<Map<String, T>> strToListMaps(String gsonString) {
//        return GSON.fromJson(gsonString, new TypeToken<List<Map<String, String>>>() {
//        }.getType());
//    }
//
//    /**
//     * convert json to map
//     * @param gsonString
//     * @return
//     */
//    public static <T> Map<String, T> strToMaps(String gsonString) {
//        return GSON.fromJson(gsonString, new TypeToken<Map<String, T>>() {
//        }.getType());
//    }


    /**
     * convert json string to json element
     *
     * @param gsonString
     * @return
     */
    public static JsonElement parseString(String gsonString) {
        return JsonParser.parseString(gsonString);
    }


    /**
     * Copy several value from resource JSON to result JSON
     *
     * @param resultJson
     * @param resourceJson
     * @param keys
     * @return
     */
    public static JsonObject copyValue(JsonObject resultJson, JsonObject resourceJson, String... keys) {
        for (String key : keys) {
            resultJson.add(key, resourceJson.get(key));
        }
        return resultJson;
    }

    /**
     * convert object to JsonElement with null value
     *
     * @param src
     */
    public static JsonElement toJsonElementWithNull(Object src) {
        return GSON_NULL.toJsonTree(src);
    }

    /**
     * convert object to JsonElement without null value
     *
     * @param src
     */
    public static JsonElement toJsonElement(Object src) {
        return GSON.toJsonTree(src);
    }
}
