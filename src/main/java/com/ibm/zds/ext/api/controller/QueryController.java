/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.controller;

import com.ibm.zds.ext.api.api.QueryApi;
import com.ibm.zds.ext.api.service.ProcessService;
import io.swagger.v3.oas.annotations.Parameter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.text.MessageFormat;

@RestController
public class QueryController implements QueryApi {

    private static final Logger LOG = LoggerFactory.getLogger(QueryController.class);

    @Autowired
    ProcessService processService;

    public ResponseEntity<Object> querySpecificTicket(@PathVariable String ticketType, @PathVariable String sys_id,
            String tag) throws Exception {
        LOG.info(MessageFormat.format("Received GET request to obtain ticket type of {0} with sys_id {1}", ticketType,
                sys_id));
        return processService.querySpecificTicketBySysid(ticketType, sys_id, tag);
    }

    public ResponseEntity<Object> getTags(@PathVariable Boolean getAllTag) throws Exception {
        LOG.info("Received GET request to obtain tags.");
        return processService.apiToGetTags(getAllTag);
    }

    public ResponseEntity<Object> queryTicketsWithHighLevelInformation(String tag, @Parameter String ticketParas)
            throws Exception {
        LOG.info(MessageFormat.format("Received GET to obtain high level information for {0}", ticketParas));
        return processService.queryTicketsWithHighLevelInformation(ticketParas, tag);
    }

    public ResponseEntity<Object> queryTicketsRelatedToSourceBySysIds(String tag, @Parameter String sysIdParas)
            throws Exception {
        LOG.info("Received GET to related tickets for sys ids.");
        return processService.batchQueryTicketsRelatedToSourceBySysIds(tag, sysIdParas);
    }

    public ResponseEntity<Object> queryTicketsRelatedToSourceByCorrelationIds(String tag,
            @Parameter String correlationIdsParas) throws Exception {
        LOG.info("Received GET to related tickets for correlation ids.");
        return processService.batchQueryTicketsRelatedToSourceByCorrelationIds(tag, correlationIdsParas);
    }

    public ResponseEntity<Object> queryTicketsRelatedToSourceByNamingRulePayloads(String tag,
            @Parameter String namingRulePayloads) throws Exception {
        LOG.info("Received GET to related tickets for naming rule payloads.");
        return processService.batchQueryTicketsRelatedToSourceByNamingRulePayloads(tag, namingRulePayloads);
    }
}
