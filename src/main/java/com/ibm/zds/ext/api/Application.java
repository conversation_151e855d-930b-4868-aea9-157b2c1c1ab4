/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.HeaderParameter;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication

public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Bean
    public OpenAPI zrddsAPI(@Value("${app.version}") String appVersion) {
        return new OpenAPI()
                .components(new Components()
                        .addParameters("ApiToken", new HeaderParameter().required(false).name("ApiToken").schema(new StringSchema()).example("")
                                .description("Used for the `local`authentication.mode <br>" +
                                        "Authorization of local token for access, the value should be equal to the preset `api.token`"))
                        .addParameters("Authorization", new HeaderParameter().required(false).name("Authorization").schema(new StringSchema()).example("Bearer ")
                                .description("Used for the `keycloak` authentication.mode <br>" +
                                        "Integrate JWT to validate the `access_token` directly, the value should be in format of `Bearer ${access_token}` "))
                )
                .info(new Info()
                        .title("Z Resource Discovery Data Extension API")
                        .version(appVersion)
                        .description("This is the OpenAPI 3.0 document for `zrdds extension api` <br> " +
                                "For the access authorization, at least one of the two header parameters `ApiToken` and `Authorization` should be filled in, which is determined by the specified `authentication.mode`")
                );
    }

    @Bean
    public OpenApiCustomizer customerGlobalHeaderOpenApiCustomizer() {
        return openApi -> openApi.getPaths().values().stream().flatMap(pathItem -> pathItem.readOperations().stream())
                .forEach(operation -> operation
                        .addParametersItem(new HeaderParameter().$ref("#/components/parameters/ApiToken").required(false))
                        .addParametersItem(new HeaderParameter().$ref("#/components/parameters/Authorization").required(false)));
    }
}

