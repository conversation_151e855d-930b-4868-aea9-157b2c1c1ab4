/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class HttpUtils {

    private static final String ENCODING = "UTF-8";
    private static final Logger LOG = LoggerFactory.getLogger(HttpUtils.class);
    private static final int TIMEOUT = 30000;

    /**
     * GET request for HTTP and HTTPS
     *
     * @param address
     * @param headers
     * @param parameters
     * @param isSSL      -  true for HTTPS, false for HTTP
     * @return
     * @throws Exception
     */
    public static String get(String address, Map<String, String> headers, Map<String, String> parameters, boolean isSSL) throws Exception {
        String url = address;
        if (parameters != null && parameters.size() > 0) {
            url = address + "?" + formatMapContent(parameters, true);
        }
        return proxyHttpRequest(url, "GET", headers, "", isSSL);
    }

    /**
     * POST reqeust for HTTP and HTTPS with requestBody in Map format
     *
     * @param address
     * @param headers     -  default use "application/json?encoding=utf-8"
     * @param parameters  -  parameter appended in the url
     * @param requestBody -  request body
     * @param isSSL       -  true for HTTPS, false for HTTP
     * @return
     * @throws Exception
     */
    public static String post(String address, Map<String, String> headers, Map<String, String> parameters, Map<String, String> requestBody, boolean isSSL) throws Exception {
        String url = address;
        if (parameters != null && parameters.size() > 0) {
            url = address + "?" + formatMapContent(parameters, true);
        }
        String body = requestBody.toString();
        if (headers.get("Content-Type") == null || headers.get("Content-Type").equals("")) {
            headers.put("Content-Type", "application/json");
        } else if (headers.get("Content-Type").contains("x-www-form-urlencoded")) {
            body = formatMapContent(requestBody, false);
        }


        return proxyHttpRequest(url, "POST", headers, body, isSSL);
    }

    /**
     * POST reqeust for HTTP and HTTPS with requestBody in String format
     *
     * @param address
     * @param headers
     * @param parameters  -  parameter appended in the url
     * @param requestBody -  request body
     * @param isSSL       -  true for HTTPS, false for HTTP
     * @return
     * @throws Exception
     */
    public static String post(
            String address, Map<String, String> headers, Map<String, String> parameters, String requestBody, boolean isSSL
    ) throws Exception {
        String url = address;
        if (parameters != null && parameters.size() > 0) {
            url = address + "?" + formatMapContent(parameters, true);
        }

        return proxyHttpRequest(url, "POST", headers, requestBody, isSSL);
    }

    /**
     * POST reqeust for HTTPS with form context or file
     *
     * @param address
     * @param headers
     * @param parameters -  parameter appended in the url
     * @param textMap    -  form context text map
     * @param fileMap    -  file map to upload
     * @return
     * @throws Exception
     */
    public static String post(String address, Map<String, String> headers, Map<String, String> parameters,
                              Map<String, String> textMap, Map<String, String> fileMap)
            throws Exception {
        String url = address;
        if (parameters != null && parameters.size() > 0) {
            url = address + "?" + formatMapContent(parameters, true);
        }

        return proxyFormHttpRequest(url, "POST", headers, textMap, fileMap);
    }


    /**
     * PUT reqeust for HTTP and HTTPS with requestBody in Map format
     *
     * @param address
     * @param headers     -  default use "application/json?encoding=utf-8"
     * @param parameters  -  parameter appended in the url
     * @param requestBody -  request body
     * @param isSSL       -  true for HTTPS, false for HTTP
     * @return
     * @throws Exception
     */
    public static String put(
            String address, Map<String, String> headers, Map<String, String> parameters, Map<String, String> requestBody, boolean isSSL
    ) throws Exception {
        String url = address;
        if (parameters != null && parameters.size() > 0) {
            url = address + "?" + formatMapContent(parameters, true);
        }
        String body = requestBody.toString();
        if (headers.get("Content-Type") == null || headers.get("Content-Type").equals("")) {
            headers.put("Content-Type", "application/json");
        } else if (headers.get("Content-Type").contains("x-www-form-urlencoded")) {
            body = formatMapContent(requestBody, false);
        }


        return proxyHttpRequest(url, "PUT", headers, body, isSSL);
    }

    /**
     * PUT reqeust for HTTP and HTTPS with requestBody in String format
     *
     * @param address
     * @param headers
     * @param parameters  -  parameter appended in the url
     * @param requestBody -  request body
     * @param isSSL       -  true for HTTPS, false for HTTP
     * @return
     * @throws Exception
     */
    public static String put(
            String address, Map<String, String> headers, Map<String, String> parameters, String requestBody, boolean isSSL
    ) throws Exception {
        String url = address;
        if (parameters != null && parameters.size() > 0) {
            url = address + "?" + formatMapContent(parameters, true);
        }

        return proxyHttpRequest(url, "PUT", headers, requestBody, isSSL);
    }

    /**
     * PUT reqeust for HTTPS with form context or file
     *
     * @param address
     * @param headers
     * @param parameters -  parameter appended in the url
     * @param textMap    -  form context text map
     * @param fileMap    -  file map to upload
     * @return
     * @throws Exception
     */
    public static String put(String address, Map<String, String> headers, Map<String, String> parameters,
                             Map<String, String> textMap, Map<String, String> fileMap)
            throws Exception {
        String url = address;
        if (parameters != null && parameters.size() > 0) {
            url = address + "?" + formatMapContent(parameters, true);
        }

        return proxyFormHttpRequest(url, "PUT", headers, textMap, fileMap);
    }

    /**
     * Process the request
     *
     * @param url
     * @param method
     * @param headers
     * @param body
     * @param isSSL
     * @return
     */
    private static String proxyHttpRequest(
            String url, String method, Map<String, String> headers, String body, boolean isSSL) throws Exception {
        String result = null;
        HttpURLConnection httpConnection = null;

        try {
            httpConnection = createConnection(url, method, headers, body, isSSL);
            String encoding = ENCODING;

            if (httpConnection.getContentEncoding() != null) {
                encoding = httpConnection.getContentEncoding();
            }
            result = inputStream2String(httpConnection.getInputStream(), encoding);
            LOG.debug(MessageFormat.format("The result from URL {0} is: {1}", url, result));
        } catch (Exception e) {
            LOG.error(e.getMessage());
            throw e;
        } finally {
            if (httpConnection != null) {
                httpConnection.disconnect();
            }
        }
        return result;
    }


    /**
     * create Http connection
     *
     * @param url
     * @param method
     * @param headers
     * @param body
     * @return
     * @throws Exception
     */
    private static HttpURLConnection createConnection(
            String url, String method, Map<String, String> headers, String body, boolean isSSL) throws Exception {

        URL Url = new URL(url);

        // for HTTPS
        if (isSSL) {
            trustAllHttpsCertificates();
        }

        HttpURLConnection httpURLConnection = (HttpURLConnection) Url.openConnection();
        // handler the connection
        httpURLConnection.setConnectTimeout(TIMEOUT);
        httpURLConnection.setRequestMethod(method);
        if (headers != null) {
            Iterator<String> iteratorHeader = headers.keySet().iterator();
            while (iteratorHeader.hasNext()) {
                String key = iteratorHeader.next();
                httpURLConnection.setRequestProperty(key, headers.get(key));
            }
        }

        httpURLConnection.setDoInput(true);
        httpURLConnection.setDoOutput(true);

        // write query data, only suitable for x-www-form-urlencoded now
        if (!(body == null || body.trim().equals(""))) {
            OutputStream writer = httpURLConnection.getOutputStream();
            try {
                writer.write(body.getBytes(ENCODING));
            } finally {
                if (writer != null) {
                    writer.flush();
                    writer.close();
                }
            }
        }

        // response result
        int responseCode = httpURLConnection.getResponseCode();
        if (responseCode != 200 && responseCode != 201 && responseCode != 202) {
            throw new Exception(responseCode + ":" + inputStream2String(httpURLConnection.getErrorStream(), ENCODING));
        }

        return httpURLConnection;
    }

    /**
     * Convert the Map to String following x-www-form-urlencoded Format
     *
     * @param params
     * @param urlEncode
     * @return
     */
    public static String formatMapContent(Map<String, String> params, boolean urlEncode) {
        StringBuilder content = new StringBuilder();

        Iterator<String> stringIterator = params.keySet().iterator();
        while (stringIterator.hasNext()) {
            String key = stringIterator.next();
            String value = params.get(key);

            if (urlEncode) {
                try {
                    content.append(key + "=" + URLEncoder.encode(value, ENCODING) + "&");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                    LOG.error("There is an error for encoding: ", e);
                }
            } else {
                content.append(key + "=" + value + "&");
            }
        }

        if (content.length() == 0) {
            return "";
        }
        return content.substring(0, content.length() - 1);
    }

    /**
     * @param input
     * @param encoding
     * @return
     * @throws IOException
     */
    private static String inputStream2String(InputStream input, String encoding) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(input, encoding));
        StringBuilder result = new StringBuilder();
        String temp;
        while ((temp = reader.readLine()) != null) {
            result.append(temp);
        }

        return result.toString();
    }


    /**
     * Process the request with file
     *
     * @param url
     * @param method
     * @param headers
     * @param textMap
     * @param fileMap
     * @return
     */
    private static String proxyFormHttpRequest(
            String url, String method, Map<String, String> headers, Map<String, String> textMap, Map<String, String> fileMap) throws Exception {
        String result;
        HttpURLConnection httpConnection = null;

        try {
            httpConnection = createFormConnection(url, method, headers, textMap, fileMap);
            String encoding = ENCODING;
            if (httpConnection.getContentEncoding() != null) {
                encoding = httpConnection.getContentEncoding();
            }
            result = inputStream2String(httpConnection.getInputStream(), encoding);
            LOG.debug(MessageFormat.format("The result from URL {0} is: {1}", url, result));
        } catch (Exception e) {
            LOG.error(e.getMessage());
            throw e;
        } finally {
            if (httpConnection != null) {
                httpConnection.disconnect();
            }
        }
        return result;
    }

    private static HttpURLConnection createFormConnection(
            String url, String method, Map<String, String> headers, Map<String, String> textMap, Map<String, String> fileMap) throws Exception {
        URL Url = new URL(url);

        trustAllHttpsCertificates();


        HttpURLConnection httpURLConnection = (HttpURLConnection) Url.openConnection();
        // handler the connection
        httpURLConnection.setConnectTimeout(TIMEOUT);
        httpURLConnection.setRequestMethod(method);
        if (headers != null) {
            Iterator<String> iteratorHeader = headers.keySet().iterator();
            while (iteratorHeader.hasNext()) {
                String key = iteratorHeader.next();
                httpURLConnection.setRequestProperty(key, headers.get(key));
            }
        }

        httpURLConnection.setConnectTimeout(5000);
        httpURLConnection.setReadTimeout(30000);
        httpURLConnection.setDoOutput(true);
        httpURLConnection.setDoInput(true);
        httpURLConnection.setUseCaches(false);

        // boundary
        String BOUNDARY = "------WebKitFormBoundaryH9IH92tGZe3sHZLq";

        trustAllHttpsCertificates();
        httpURLConnection.setRequestProperty("Content-Type",
                "multipart/form-data; boundary=" + BOUNDARY);

        OutputStream writer = new DataOutputStream(httpURLConnection.getOutputStream());
        try {
            // text
            if (textMap != null) {
                StringBuffer strBuf = new StringBuffer();
                Iterator iter = textMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    strBuf.append("\r\n").append("--").append(BOUNDARY)
                            .append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\""
                            + inputName + "\"\r\n\r\n");
                    strBuf.append(inputValue);
                }
                writer.write(strBuf.toString().getBytes(ENCODING));
            }

            // file
            if (fileMap != null) {
                Iterator iter = fileMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    File file = new File(inputValue);
                    if (!file.exists() || !file.isFile()) {
                        LOG.error("Upload file is not exists or the path is not a file");
                        throw new Exception("Upload file is not exists or the path is not a file");
                    }
                    String filename = file.getName();

                    String contentType = "application/octet-stream";
//                  Security Scan Result:Validation.EncodingRequired: java.io.OutputStream.write(byte[]):void
//                    //get contentType from file type, default value is application/octet-stream
//                    String contentType = new MimetypesFileTypeMap().getContentType(file);
//                    //set contentType for image type
//                    if (!"".equals(contentType)) {
//                        if (filename.endsWith(".png")) {
//                            contentType = "image/png";
//                        } else if (filename.endsWith(".jpg") || filename.endsWith(".jpeg") || filename.endsWith(".jpe")) {
//                            contentType = "image/jpeg";
//                        } else if (filename.endsWith(".gif")) {
//                            contentType = "image/gif";
//                        } else if (filename.endsWith(".ico")) {
//                            contentType = "image/image/x-icon";
//                        }
//                    }
//                    if (contentType == null || "".equals(contentType)) {
//                        contentType = "application/octet-stream";
//                    }
                    StringBuffer strBuf = new StringBuffer();
                    strBuf.append("\r\n").append("--").append(BOUNDARY)
                            .append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\""
                            + inputName + "\"; filename=\"" + filename
                            + "\"\r\n");
                    strBuf.append("Content-Type:" + contentType + "\r\n\r\n");
                    writer.write(strBuf.toString().getBytes(ENCODING));
                    DataInputStream fileReader = new DataInputStream(
                            new FileInputStream(file));
                    try {
                        int bytes = 0;
                        byte[] bufferOut = new byte[1024];
                        while ((bytes = fileReader.read(bufferOut)) != -1) {
                            writer.write(bufferOut, 0, bytes);
                        }
                    } finally {
                        fileReader.close();
                    }
                }
            }
            byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes(ENCODING);
            writer.write(endData);
        } finally {
            if (writer != null) {
                writer.flush();
                writer.close();
            }
        }

        // response result
        int responseCode = httpURLConnection.getResponseCode();
        if (responseCode != 200 && responseCode != 201 && responseCode != 202) {
            throw new Exception(responseCode + ":" + inputStream2String(httpURLConnection.getErrorStream(), ENCODING));
        }

        return httpURLConnection;
    }

    /**
     * Config for HTTPS
     *
     * @throws Exception
     */
    private static void trustAllHttpsCertificates() throws Exception {
        HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
            public boolean verify(String str, SSLSession session) {
                return true;
            }
        });
        javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[1];
        javax.net.ssl.TrustManager tm = new miTM();
        trustAllCerts[0] = tm;
        javax.net.ssl.SSLContext sc = javax.net.ssl.SSLContext
                .getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        javax.net.ssl.HttpsURLConnection.setDefaultSSLSocketFactory(sc
                .getSocketFactory());
    }

    //====================================================================
    //=============================   TEST  ==============================
    //====================================================================
    public static void main(String[] args) {
        try {

            // the url to query info of telephone number by taobao
            String address = "https://tcc.taobao.com/cc/json/mobile_tel_segment.htm";

            Map<String, String> params = new HashMap<String, String>();
            params.put("tel", "188xxxxxxxx");

            String res = get(address, null, params, false);
//            String res = get(address, null, params, true);
            System.out.println(res);

        } catch (Exception e) {
            LOG.error(e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Config the Request certificate
     */
    static class miTM implements javax.net.ssl.TrustManager, javax.net.ssl.X509TrustManager {

        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
            return null;
        }

        public boolean isServerTrusted(
                java.security.cert.X509Certificate[] certs) {
            return true;
        }

        public boolean isClientTrusted(
                java.security.cert.X509Certificate[] certs) {
            return true;
        }

        public void checkServerTrusted(
                java.security.cert.X509Certificate[] certs, String authType)
                throws java.security.cert.CertificateException {
            return;
        }

        public void checkClientTrusted(
                java.security.cert.X509Certificate[] certs, String authType)
                throws java.security.cert.CertificateException {
            return;
        }
    }
}
