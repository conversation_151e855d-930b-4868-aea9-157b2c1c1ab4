
/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/
package com.ibm.zds.ext.api.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.LinkedList;
import java.util.List;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

/**
 * Class that provides XML utility methods to parse an xml file
 * 
 */
public class XMLUtil {

    private static final String CP1047 = "Cp1047";
    private static final String UTF8 = "UTF-8";
    private static String ENCODING = UTF8;

    public static String getAttrValue(Node n, String name) {

        String ret = null;
        NamedNodeMap nattr = n.getAttributes();
        Node attr = nattr.getNamedItem(name);

        if (attr != null) {

            ret = attr.getNodeValue();

        } else {
            // handle case in which <nodename and <namespace:nodename
            String ln = "";
            int ci = 0;
            String nln = "";
            for (int i = nattr.getLength() - 1; i > -1 && ret == null; --i) {

                attr = nattr.item(i);
                ln = attr.getNodeName();

                if (name.equals(ln)) {
                    // ignore namespace .. pretty safe.
                    ret = attr.getNodeValue();
                } else {

                    ci = ln.lastIndexOf(':');
                    if (-1 != ci && ci != ln.length() - 1) {

                        nln = ln.substring(ci + 1);
                        if (name.equals(nln)) {

                            ret = attr.getNodeValue();

                        }
                    }
                }
            }

        }
        return ret;

    }

    public static List<Element> getChildElementsByTagName(Element parent,
            String exp) {

        List<Element> ret = new LinkedList<Element>();
        Node cn = parent.getFirstChild();

        String ln = "";
        int cs = 0;
        String lln = "";

        // handle xml element <nodeName and <namespace:nodeName
        for (; cn != null; cn = cn.getNextSibling()) {

            if (cn.getNodeType() == Node.ELEMENT_NODE) {

                ln = ((Element) cn).getNodeName();

                if (exp.equals(ln)) {

                    ret.add((Element) cn);

                } else {

                    cs = ln.lastIndexOf(':');

                    if (cs != -1 && cs != ln.length() - 1) {

                        lln = ln.substring(cs + 1);

                        if (exp.equals(lln)) {

                            ret.add((Element) cn);
                        }
                    }
                }

            }

        }

        return ret;
    }

    public static Document getDOM(File f, String encoding) throws Exception {
        ENCODING = encoding;
        return getDOM(f);
    }

    /**
     * Utility method to parse an generic xml file
     * 
     * @param f
     *          File to parse
     * @return Document xml object
     * @throws Exception thrown when there is a parsing problem with f
     */
    public static Document getDOM(File f) throws Exception {

        String message = "";
        Document ret = null;
        // get the factory
        DocumentBuilderFactory dbf = null;
        dbf = DocumentBuilderFactory.newInstance();

        dbf.setValidating(false);
        try {
            dbf.setFeature("http://xml.org/sax/features/validation", false);
            dbf.setFeature(
                    "http://apache.org/xml/features/nonvalidating/load-dtd-grammar",
                    false);
            dbf.setFeature(
                    "http://apache.org/xml/features/nonvalidating/load-external-dtd",
                    false);

        } catch (ParserConfigurationException e1) {
            message = e1.getClass().getName()
                    + ": Exception while parsing file:" + f.getName() + "\n" + e1.getMessage();
            throw new Exception(message);
        }

        // Using factory get an instance of document builder
        InputSource is = null;
        DocumentBuilder db = null;
        FileInputStream read = null;
        try {
            db = dbf.newDocumentBuilder();
            // Forces the parser to use the application log handler
            db.setErrorHandler(null);
            read = new FileInputStream(f);
            is = new InputSource(read);
            is.setSystemId(f.getAbsolutePath());
            is.setEncoding(ENCODING);
            ret = db.parse(is);

        } catch (ParserConfigurationException e) {
            message = e.getClass().getName() + ": " + e.getMessage();
            throw new Exception(message);

        } catch (FileNotFoundException e) {
            message = e.getClass().getName() + ": " + e.getMessage();
            throw new Exception(message);

        } catch (SAXException e) {
            // Try other encoding
            message = e.getClass().getName() + ": " + e.getMessage();

            try {
                if (ENCODING.equals(UTF8)) {
                    ENCODING = CP1047;
                } else {
                    ENCODING = UTF8;
                }

                message = message + "\nPossible cause different encoding. Trying: " + ENCODING;

                db = dbf.newDocumentBuilder();
                // Forces the parser to use the application log handler
                db.setErrorHandler(null);
                read = new FileInputStream(f);
                is = new InputSource(read);
                is.setSystemId(f.getAbsolutePath());
                is.setEncoding(ENCODING);
                ret = db.parse(is);
            } catch (ParserConfigurationException e1) {
                message = message + "\n" + e1.getClass().getName() + ": " + e1.getMessage();
                throw new Exception(message);
            } catch (FileNotFoundException e1) {
                message = message + "\n" + e1.getClass().getName() + ": " + e1.getMessage();
                throw new Exception(message);
            } catch (SAXException e1) {
                message = message + "\n" + e1.getClass().getName() + ": " + e1.getMessage();
                throw new Exception(message);
            } catch (IOException e1) {
                message = message + "\n" + e1.getClass().getName() + ": " + e1.getMessage();
                throw new Exception(message);
            }

        } catch (IOException e) {
            message = e.getClass().getName() + ": " + e.getMessage();
            throw new Exception(message);
        }

        return ret;
    }

    public static Document getDOM(String fileName, InputStream read) throws Exception {

        String message = "";
        Document ret = null;
        // get the factory
        DocumentBuilderFactory dbf = null;
        dbf = DocumentBuilderFactory.newInstance();

        dbf.setValidating(false);
        try {
            dbf.setFeature("http://xml.org/sax/features/validation", false);
            dbf.setFeature(
                    "http://apache.org/xml/features/nonvalidating/load-dtd-grammar",
                    false);
            dbf.setFeature(
                    "http://apache.org/xml/features/nonvalidating/load-external-dtd",
                    false);

        } catch (ParserConfigurationException e1) {
            message = e1.getClass().getName()
                    + ": Exception while parsing file:" + fileName + "\n" + e1.getMessage();
            throw new Exception(message);
        }

        // Using factory get an instance of document builder
        InputSource is = null;
        DocumentBuilder db = null;
        try {
            db = dbf.newDocumentBuilder();
            // Forces the parser to use the application log handler
            db.setErrorHandler(null);
            is = new InputSource(read);
            is.setEncoding(ENCODING);
            ret = db.parse(is);

        } catch (ParserConfigurationException e) {
            message = e.getClass().getName() + ": " + e.getMessage();
            throw new Exception(message);

        } catch (FileNotFoundException e) {
            message = e.getClass().getName() + ": " + e.getMessage();
            throw new Exception(message);

        } catch (SAXException e) {
            // Try other encoding
            message = e.getClass().getName() + ": " + e.getMessage();

            try {
                if (ENCODING.equals(UTF8)) {
                    ENCODING = CP1047;
                } else {
                    ENCODING = UTF8;
                }

                message = message + "\nPossible cause different encoding. Trying: " + ENCODING;

                db = dbf.newDocumentBuilder();
                // Forces the parser to use the application log handler
                db.setErrorHandler(null);
                is = new InputSource(read);
                is.setEncoding(ENCODING);
                ret = db.parse(is);
            } catch (ParserConfigurationException e1) {
                message = message + "\n" + e1.getClass().getName() + ": " + e1.getMessage();
                throw new Exception(message);
            } catch (FileNotFoundException e1) {
                message = message + "\n" + e1.getClass().getName() + ": " + e1.getMessage();
                throw new Exception(message);
            } catch (SAXException e1) {
                message = message + "\n" + e1.getClass().getName() + ": " + e1.getMessage();
                throw new Exception(message);
            } catch (IOException e1) {
                message = message + "\n" + e1.getClass().getName() + ": " + e1.getMessage();
                throw new Exception(message);
            }

        } catch (IOException e) {
            message = e.getClass().getName() + ": " + e.getMessage();
            throw new Exception(message);
        }

        return ret;
    }
}
