/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

public class KeyUtil {

    private static int addLen = 3;
    private static int addPart = 1;
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    private static String lastDate = "";

    public static synchronized String genUniqueKey() {
        Random random = new Random();
        Integer number = random.nextInt(1000);
        return System.currentTimeMillis() + String.format("%03d", number);
    }

    /**
     * Get an unique ID generated by a millisecond timestamp and an additional counter
     * The length of the ID is default 20 = 17(SimpleDateFormat length) +3(addLen).
     */
    public static synchronized String getUniqueTimeStr() {
        String timeStr = sdf.format(new Date());

        if (KeyUtil.lastDate.equals(timeStr)) {
            addPart += 1;
        } else {
            addPart = 1;
            lastDate = timeStr;
        }

        for (int i = 0; i < addLen - (addPart + "").length(); i++) {
            timeStr += "0";
        }

        timeStr += addPart;
        return timeStr;
    }

    public static void main(String[] args) {
        String uniqueKey = genUniqueKey();
        System.out.println(uniqueKey);
        String uniqueTimeStr = getUniqueTimeStr();
        System.out.println(uniqueTimeStr);
    }
}
