package com.ibm.zds.ext.api.controller;

import com.ibm.zds.ext.api.api.QuartzApi;
import com.ibm.zds.ext.api.service.ProcessService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class QuartzController implements QuartzApi {

    private static final Logger LOG = LoggerFactory.getLogger(QuartzController.class);
    @Autowired
    ProcessService processService;
    @Autowired
    Environment env;

    @PostConstruct
    public void runScheduledJob() throws Exception {
        // createScheduledJob("222");
    }

    public ResponseEntity<String> createScheduledJob(@PathVariable String jobType, @PathVariable String jobName)
            throws Exception {
        LOG.info("Received POST request to create the scheduled job " + jobName + " of the jobType " + jobType);
        return processService.createScheduledJob(jobType, jobName);
    }

    public ResponseEntity<String> updateScheduledJob(@PathVariable String jobType, @PathVariable String jobName)
            throws Exception {
        LOG.info("Received POST request to create the scheduled job " + jobName + " of the jobType " + jobType);
        return processService.updateScheduledJob(jobType, jobName);
    }

    public ResponseEntity<String> pauseScheduledJob(@PathVariable String jobType, @PathVariable String jobName)
            throws Exception {
        LOG.info("Received POST request to pause the scheduled job " + jobName + " of the jobType " + jobType);
        return processService.pauseScheduledJob(jobType, jobName);
    }

    public ResponseEntity<String> resumeScheduledJob(@PathVariable String jobType, @PathVariable String jobName)
            throws Exception {
        LOG.info("Received POST request to resume the scheduled job " + jobName + " of the jobType " + jobType);
        return processService.resumeScheduledJob(jobType, jobName);
    }

    public ResponseEntity<String> runScheduledJobOnce(@PathVariable String jobType, @PathVariable String jobName)
            throws Exception {
        LOG.info("Received POST request to run the scheduled job " + jobName + " of the jobType " + jobType + " once");
        return processService.runScheduledJobOnce(jobType, jobName);
    }

    public ResponseEntity<String> deleteScheduledJob(@PathVariable String jobType, @PathVariable String jobName)
            throws Exception {
        LOG.info("Received POST request to delete the scheduled job " + jobName + " of the jobType " + jobType);
        return processService.deleteScheduledJob(jobType, jobName);
    }

}
