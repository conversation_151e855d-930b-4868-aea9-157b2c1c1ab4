/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RequestMapping("/zrdds/ext/api/v1")
@Tag(name = "Config", description = "This API focus on configuration")
public interface ConfigApi {

        @Operation(summary = "Get the config for jobName", description = "Get the config for jobName <br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @GetMapping(value = "/config/get/{jobType}/{jobName}")
        @ResponseBody
        ResponseEntity<String> getConfig(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate configs of what type of job to get. Currently we support servicenow.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, name = "jobName", description = "Indicate configs of which job to get.", required = true) @PathVariable String jobName)
                        throws Exception;

        @Operation(summary = "Initialize the config for jobName", description = "Initialize the config for jobName,  Content-Type in request header must be application/json<br>"
                        +
                        "return response body:<br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/config/init/{jobType}/{jobName}", consumes = { "application/json" })
        @ResponseBody
        ResponseEntity<String> initConfig(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate what type of job to initialize. Currently we support servicenow.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, description = "Indicate which job to initialize.", required = true) @PathVariable("jobName") String jobName,
                        @Parameter(in = ParameterIn.DEFAULT, description = "configs", required = true) @RequestBody @Schema(example = "{\"timeoutInsec\": \"10\",\"accessToken\": \"sampletoken\",\"scheme\": \"https\",\"server\": \"sample.service-now.com\",\"port\": \"443\",\"queryInterval\": \"600\",\"cronExpression\": \"10 * * * * ?\"}") Map<String, String> config)
                        throws Exception;

        @Operation(summary = "Update the config for jobName", description = "Update the config for jobName,  Content-Type in request header must be application/json<br>"
                        +
                        "return response body:<br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/config/update/{jobType}/{jobName}", consumes = { "application/json" })
        @ResponseBody
        ResponseEntity<String> updateConfig(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate what type of job to update. Currently we support servicenow.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, description = "Indicate which job to update.", required = true) @PathVariable("jobName") String jobName,
                        @Parameter(in = ParameterIn.DEFAULT, description = "configs", required = true) @RequestBody @Schema(example = "{\"timeoutInsec\": \"10\",\"accessToken\": \"sampletoken\",\"scheme\": \"https\",\"server\": \"sample.service-now.com\",\"port\": \"443\",\"queryInterval\": \"600\",\"cronExpression\": \"10 * * * * ?\"}") Map<String, String> config)
                        throws Exception;
}
