/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.oauth2.server.resource.OAuth2ResourceServerConfigurer;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.firewall.StrictHttpFirewall;
import org.springframework.security.web.header.writers.StaticHeadersWriter;

import java.util.Arrays;


@Configuration
@EnableWebSecurity
public class WebSecurityConfig {

    @Value("${keycloak.jwks.uri}")
    protected String keycloakJwksUri;

    @Value("${authentication.mode}")
    protected String authMode;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {  

        http.csrf().disable();
        http.headers()
            .contentTypeOptions()
            .and()
            .cacheControl()
            .and()
            .xssProtection()
            .and()
            .frameOptions()
            .and()
            .referrerPolicy()
            .and()
            .addHeaderWriter(
                new StaticHeadersWriter("Content-Security-Policy",
                    "default-src 'none'; " +
                        "script-src 'self'; " +
                        "connect-src 'self'; " +
                        "img-src 'self'; " +
                        "style-src 'self';" +
                        "base-uri 'self';" +
                        "frame-src 'self';" +
                        "frame-ancestors 'none';" +
                        "manifest-src 'none';" +
                        "form-action 'self'"
                ));

        if (authMode.toLowerCase().equals("keycloak")) {
            http
                    .authorizeHttpRequests(requests ->
                            requests
                                    .requestMatchers("/trace/users/**").permitAll()
                                    // swagger start
                                    .requestMatchers("/").permitAll()
                                    .requestMatchers("/swagger-ui/index.html").permitAll()
                                    .requestMatchers("/swagger-ui/**.js").permitAll()
                                    .requestMatchers("/swagger-ui/**.css").permitAll()
                                    .requestMatchers("/swagger-ui/favicon-**.png").permitAll()
                                    .requestMatchers("/api-docs/**").permitAll()
                                    // zrdds api
                                    .requestMatchers("/zrdds/api/**").hasAuthority("SCOPE_USER")
                                    .anyRequest().authenticated()
                    )
                    .oauth2ResourceServer(OAuth2ResourceServerConfigurer::jwt);
        }

        return http.build();
    }

    @Bean
    JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri(keycloakJwksUri).build();
    }

    @Bean
    public HttpFirewall configureFirewall() {
        StrictHttpFirewall strictHttpFirewall = new StrictHttpFirewall();
        strictHttpFirewall.setAllowedHttpMethods(Arrays.asList("GET", "POST"));
        return strictHttpFirewall;
    }
}