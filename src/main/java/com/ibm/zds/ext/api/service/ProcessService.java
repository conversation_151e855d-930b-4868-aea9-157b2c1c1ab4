/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.service;

import com.google.gson.*;
import com.ibm.zds.ext.api.db.QueryService;
import com.ibm.zds.ext.api.utils.EnvConfigUtils;
import com.ibm.zds.ext.api.utils.GsonUtils;
import com.ibm.zds.ext.api.utils.HttpUtils;
import com.ibm.zds.ext.database.entity.snow.ChangeRequest;
import com.ibm.zds.ext.database.entity.snow.Incident;
import com.ibm.zds.ext.database.entity.snow.Problem;
import com.ibm.zds.ext.database.entity.snow.Tag;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@Component
public class ProcessService {
    private static final Logger LOG = LoggerFactory.getLogger(ProcessService.class);
    private final String emptyString = "{}";
    private final String CiNotFoundName = "CI not found";
    @Autowired
    Environment env;
    Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();
    @Getter
    @Setter
    private JsonObject tickets;
    private CorrelationConventionService parser;
    private Map<String, String> headers;
    private Map<String, String> prioityConversionMap = new HashMap<>();
    private Map<String, String> stateConversionMap = new HashMap<>();
    @Autowired
    private EnvConfigUtils envConfigs;

    public ProcessService() {
        try {
            parser = new CorrelationConventionService(".");
            parser.readIdConventionRules();
        } catch (Exception e) {
            LOG.error("Get error when configing the correlationConvention and the reason is ", e);
        }
        this.headers = new HashMap<>();
        envConfigs = new EnvConfigUtils();
        this.headers.put("Accept", "application/json");
        this.headers.put("Content-Type", "application/json");
        prioityConversionMap.put("1", "1-Critical");
        prioityConversionMap.put("2", "2-High");
        prioityConversionMap.put("3", "3-Medium");
        prioityConversionMap.put("4", "4-Low");
        prioityConversionMap.put("5", "5-Planning");
        stateConversionMap.put("1", "New");
        stateConversionMap.put("2", "In Progress");
        stateConversionMap.put("3", "On Hold");
        stateConversionMap.put("6", "Resolved");
        stateConversionMap.put("7", "Closed");
        stateConversionMap.put("8", "Canceled");
        stateConversionMap.put("-5", "New");
        stateConversionMap.put("-4", "Assess");
        stateConversionMap.put("-2", "Scheduled");
        stateConversionMap.put("-3", "Authorize");
        stateConversionMap.put("-1", "Implement");
        stateConversionMap.put("0", "Review");
        stateConversionMap.put("101", "New");
        stateConversionMap.put("103", "Root Cause Analysis");
        stateConversionMap.put("107", "Closed");
        stateConversionMap.put("102", "Assess");
        stateConversionMap.put("106", "Resolved");
        stateConversionMap.put("104", "Fix in Progress");

    }

    public ResponseEntity<String> initConfig(String jobType, String jobName, Map<String, String> config)
            throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/config/init/" + jobType + "/" + jobName;
            String configString = gson.toJson(config);
            String response = HttpUtils.post(apiUrl, headers, null, configString, true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in initConfig method: ", e);
            return new ResponseEntity<String>(
                    "Failed to initialize the configs and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<String> updateConfig(String jobType, String jobName, Map<String, String> config)
            throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/config/update/" + jobType + "/" + jobName;
            String configString = gson.toJson(config);
            String response = HttpUtils.post(apiUrl, headers, null, configString, true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in updateConfig method: ", e);
            return new ResponseEntity<String>(
                    "Failed to update the configs and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<String> getConfig(String jobType, String jobName) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/config/get/" + jobType + "/" + jobName;
            String response = HttpUtils.get(apiUrl, headers, null, true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in getConfig method: ", e);
            return new ResponseEntity<String>("Failed to get the configs and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<String> createScheduledJob(String jobType, String jobName)
            throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/scheduledJob/create/" + jobType + "/" + jobName;
            String response = HttpUtils.post(apiUrl, headers, null, "", true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in createScheduledJob method: ", e);
            return new ResponseEntity<String>(
                    "Failed to create the scheduled job and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<String> updateScheduledJob(String jobType, String jobName)
            throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/scheduledJob/update/" + jobType + "/" + jobName;
            String response = HttpUtils.post(apiUrl, headers, null, "", true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in updateScheduledJob method: ", e);
            return new ResponseEntity<String>(
                    "Failed to update the scheduled job and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<String> pauseScheduledJob(String jobType, String jobName)
            throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/scheduledJob/pause/" + jobType + "/" + jobName;
            String response = HttpUtils.post(apiUrl, headers, null, "", true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in pauseScheduledJob method: ", e);
            return new ResponseEntity<String>(
                    "Failed to pause the scheduled job and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<String> resumeScheduledJob(String jobType, String jobName)
            throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/scheduledJob/resume/" + jobType + "/" + jobName;
            String response = HttpUtils.post(apiUrl, headers, null, "", true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in resumeScheduledJob method: ", e);
            return new ResponseEntity<String>(
                    "Failed to resume the scheduled job and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<String> runScheduledJobOnce(String jobType, String jobName)
            throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/scheduledJob/runOnce/" + jobType + "/" + jobName;
            String response = HttpUtils.post(apiUrl, headers, null, "", true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in runScheduledJobOnce method: ", e);
            return new ResponseEntity<String>(
                    "Failed to run the scheduled job once and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<String> deleteScheduledJob(String jobType, String jobName)
            throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            String coreApiToken = envConfigs.getCoreApiToken();
            headers.put("ApiToken", coreApiToken);
            headers.put("Content-Type", "application/json");
            String apiUrl = envConfigs.getCoreUrl() + "/ext/api/v1/scheduledJob/delete/" + jobType + "/" + jobName;
            String response = HttpUtils.post(apiUrl, headers, null, "", true);
            return new ResponseEntity<String>(response, HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in deleteScheduledJob method: ", e);
            return new ResponseEntity<String>(
                    "Failed to run the scheduled job once and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public JsonArray getTags(Boolean getAllTags) throws Exception {
        QueryService queryService = new QueryService();
        List<Tag> tagList = queryService.getAllTags();
        queryService.closeSession();
        if (getAllTags) {
            return GsonUtils.toJsonElement(tagList).getAsJsonArray();
        } else {
            JsonArray jsonArray = new JsonArray();
            jsonArray.add(getJsonObjectFromList(tagList));
            return jsonArray;
        }
    }

    public ResponseEntity<Object> apiToGetTags(Boolean getAllTags) throws Exception {
        try {
            JsonArray jsonArray = getTags(getAllTags);
            return new ResponseEntity<Object>(jsonArray.toString(), HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in apiToGetTags method: ", e);
            return new ResponseEntity<Object>(
                    "Failed to get the tags and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<Object> querySpecificTicketBySysid(String tableName, String sysId, String tag)
            throws Exception {
        try {
            JsonObject specificTicketJsonObject = getSpecificTicketBySysid(tableName, sysId, tag);
            return new ResponseEntity<Object>(specificTicketJsonObject.toString(), HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in querySpecificTicketBySysid method: ", e);
            return new ResponseEntity<Object>(
                    "Failed to query the specific ticket and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public JsonObject getSpecificTicketBySysid(String tableName, String sysId, String tag) throws Exception {
        if (sysId != null && !sysId.isEmpty()) {
            JsonObject jsonObject = new JsonObject();
            QueryService queryService = new QueryService();
            if (tableName.equals("incident")) {
                List<Incident> incidentList = queryService.getIncidentByTagAndSysID(tag, sysId);
                jsonObject = getJsonObjectFromList(incidentList);
            } else if (tableName.equals("problem")) {
                List<Problem> problemList = queryService.getProblemByTagAndSysID(tag, sysId);
                jsonObject = getJsonObjectFromList(problemList);
            } else if (tableName.equals("change")) {
                List<ChangeRequest> changeRequestList = queryService.getChangeRequestByTagAndSysID(tag, sysId);
                jsonObject = getJsonObjectFromList(changeRequestList);
            } else {
                // TO DO: exception process
                throw new Exception(
                        MessageFormat.format("The {0} type should be one of [incident,problem,change]", tableName));
            }
            queryService.closeSession();
            return jsonObject;
        } else {
            // TO DO: exception process
            throw new Exception("The sys_id is not provided or is empty. Please double check the sys_id.");
        }

    }

    private <T> JsonObject getJsonObjectFromList(List<T> list) {
        if (list.size() > 0) {
            return GsonUtils.toJsonElementWithNull(list.get(0)).getAsJsonObject();
        } else {
            return new JsonObject();
        }
    }

    public JsonArray getRelatedTicketsByCISysId(String ciItem, String tag) throws Exception {
        QueryService queryService = new QueryService();
        List<Map<String, String>> affectedCIList = queryService.getAffectedCIByTagAndCIItem(tag, ciItem);
        JsonArray array = GsonUtils.toJsonElementWithNull(affectedCIList).getAsJsonArray();
        queryService.closeSession();
        return array;
    }

    public JsonArray getRelatedTicketsByCICorrelationId(String ciCorrelationId, String tag) throws Exception {
        QueryService queryService = new QueryService();
        List<Map<String, String>> affectedCIList = queryService.getAffectedCIByTagAndCICorrelationID(tag,
                ciCorrelationId);
        JsonArray array = GsonUtils.toJsonElementWithNull(affectedCIList).getAsJsonArray();
        queryService.closeSession();
        return array;
    }

    public ResponseEntity<Object> queryTicketsWithHighLevelInformation(String ticketParas, String tag)
            throws Exception {
        try {
            JsonObject tickets = GsonUtils.fromJson(ticketParas, JsonObject.class);
            JsonArray ticketList = tickets.get("tickets").getAsJsonArray();
            List<String> ticketsWithHighLevelInfo = new ArrayList<String>();
            for (JsonElement ticket : ticketList) {
                JsonObject ticketInfoJson = gson.fromJson(ticket, JsonObject.class);
                String sysId = ticketInfoJson.get("sys_id").getAsString();
                String ticketType = ticketInfoJson.get("ticket_type").getAsString();
                JsonObject ticketJson = getSpecificTicketBySysid(ticketType, sysId, tag);
                ticketsWithHighLevelInfo.add(keepHighLevelInformation(ticketJson, ticketType));
            }
            return new ResponseEntity<Object>(ticketsWithHighLevelInfo.toString(), HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in queryTicketsWithHighLevelInformation method: ", e);
            return new ResponseEntity<Object>(
                    "Failed to query the specific tickets with high level information and the fail cause is "
                            + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public String keepHighLevelInformation(JsonObject ticketJson, String ticket_type) {
        if (ticketJson.isEmpty()) {
            return emptyString;
        }
        JsonObject ticketJsonWithHighLevelInfo = new JsonObject();
        ticketJsonWithHighLevelInfo.addProperty("ticket_type", ticket_type);
        ticketJsonWithHighLevelInfo.addProperty("sys_id", ticketJson.get("sys_id").getAsString());
        ticketJsonWithHighLevelInfo.addProperty("Number", ticketJson.get("number").getAsString());
        ticketJsonWithHighLevelInfo.addProperty("Description", ticketJson.get("description").getAsString());
        ticketJsonWithHighLevelInfo.addProperty("Priority",
                this.prioityConversionMap.get(ticketJson.get("priority").getAsString()));
        ticketJsonWithHighLevelInfo.addProperty("State",
                this.stateConversionMap.get(ticketJson.get("state").getAsString()));
        return gson.toJson(ticketJsonWithHighLevelInfo);
    }

    public JsonObject queryTicketsRelatedToSourceBySysId(String tag, String ciItem) throws Exception {
        String name = CiNotFoundName;
        JsonObject relatedTickets = new JsonObject();
        JsonArray jsonArray = getRelatedTicketsByCISysId(ciItem, tag);
        Integer incidentCount = 0;
        Integer problemCount = 0;
        Integer changeCount = 0;
        ArrayList<String> incidents = new ArrayList<String>();
        ArrayList<String> problems = new ArrayList<String>();
        ArrayList<String> changes = new ArrayList<String>();
        for (JsonElement element : jsonArray) {
            JsonObject ticket = element.getAsJsonObject();
            String ticketSysId = ticket.get("task").getAsString();
            String ticketType = ticket.get("task_type").getAsString();
            if (ticket.get("name") == null) {
                throw new Exception("It seems the data is not complete. The CI with sys_id of " + ciItem
                        + " cannot be found in the table.");
            }
            name = ticket.get("name").getAsString();
            switch (ticketType) {
                case "incident":
                    incidentCount = incidentCount + 1;
                    incidents.add(ticketSysId);
                    break;
                case "problem":
                    problemCount = problemCount + 1;
                    problems.add(ticketSysId);
                    break;
                case "change_request":
                    changeCount = changeCount + 1;
                    changes.add(ticketSysId);
                    break;
                default:
                    break;
            }
        }
        relatedTickets.addProperty("incidentCount", incidentCount.toString());
        relatedTickets.addProperty("incidentSysIds", String.join(",", incidents));
        relatedTickets.addProperty("problemCount", problemCount.toString());
        relatedTickets.addProperty("problemSysIds", String.join(",", problems));
        relatedTickets.addProperty("changeCount", changeCount.toString());
        relatedTickets.addProperty("changeSysIds", String.join(",", changes));
        relatedTickets.addProperty("name", name);
        return relatedTickets;
    }

    public JsonObject queryTicketsRelatedToSourceByCorrelationId(String tag, String ciCorrelationId) throws Exception {
        try {
            String name = CiNotFoundName;
            JsonObject relatedTickets = new JsonObject();
            JsonArray jsonArray = getRelatedTicketsByCICorrelationId(ciCorrelationId, tag);
            Integer incidentCount = 0;
            Integer problemCount = 0;
            Integer changeCount = 0;
            ArrayList<String> incidents = new ArrayList<String>();
            ArrayList<String> problems = new ArrayList<String>();
            ArrayList<String> changes = new ArrayList<String>();
            for (JsonElement element : jsonArray) {
                JsonObject ticket = element.getAsJsonObject();
                String ticketSysId = ticket.get("task").getAsString();
                String ticketType = ticket.get("task_type").getAsString();
                if (ticket.get("name") == null) {
                    throw new Exception(
                            "It seems the data is not complete. The CI with correlation_id of " + ciCorrelationId
                                    + " cannot be found in the table.");
                }
                name = ticket.get("name").getAsString();
                switch (ticketType) {
                    case "incident":
                        incidentCount = incidentCount + 1;
                        incidents.add(ticketSysId);
                        break;
                    case "problem":
                        problemCount = problemCount + 1;
                        problems.add(ticketSysId);
                        break;
                    case "change_request":
                        changeCount = changeCount + 1;
                        changes.add(ticketSysId);
                        break;
                    default:
                        break;
                }
            }
            relatedTickets.addProperty("incidentCount", incidentCount.toString());
            relatedTickets.addProperty("incidentSysIds", String.join(",", incidents));
            relatedTickets.addProperty("problemCount", problemCount.toString());
            relatedTickets.addProperty("problemSysIds", String.join(",", problems));
            relatedTickets.addProperty("changeCount", changeCount.toString());
            relatedTickets.addProperty("changeSysIds", String.join(",", changes));
            relatedTickets.addProperty("name", name);
            return relatedTickets;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public JsonObject queryTicketsRelatedToSourceByPayload(String tag, JsonObject namingRulePayload) throws Exception {
        try {
            String name = CiNotFoundName;
            JsonObject relatedTickets = new JsonObject();
            String correlationId = parser.getCorrelationId(namingRulePayload);
            JsonArray jsonArray = getRelatedTicketsByCICorrelationId(correlationId, tag);
            Integer incidentCount = 0;
            Integer problemCount = 0;
            Integer changeCount = 0;
            ArrayList<String> incidents = new ArrayList<String>();
            ArrayList<String> problems = new ArrayList<String>();
            ArrayList<String> changes = new ArrayList<String>();
            for (JsonElement element : jsonArray) {
                JsonObject ticket = element.getAsJsonObject();
                String ticketSysId = ticket.get("task").getAsString();
                String ticketType = ticket.get("task_type").getAsString();
                name = ticket.get("name").getAsString();
                switch (ticketType) {
                    case "incident":
                        incidentCount = incidentCount + 1;
                        incidents.add(ticketSysId);
                        break;
                    case "problem":
                        problemCount = problemCount + 1;
                        problems.add(ticketSysId);
                        break;
                    case "change_request":
                        changeCount = changeCount + 1;
                        changes.add(ticketSysId);
                        break;
                    default:
                        break;
                }
            }
            relatedTickets.addProperty("incidentCount", incidentCount.toString());
            relatedTickets.addProperty("incidentSysIds", String.join(",", incidents));
            relatedTickets.addProperty("problemCount", problemCount.toString());
            relatedTickets.addProperty("problemSysIds", String.join(",", problems));
            relatedTickets.addProperty("changeCount", changeCount.toString());
            relatedTickets.addProperty("changeSysIds", String.join(",", changes));
            relatedTickets.addProperty("name", name);
            relatedTickets.addProperty("correlationId", correlationId);
            return relatedTickets;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public ResponseEntity<Object> batchQueryTicketsRelatedToSourceBySysIds(String tag, String sysIdParas)
            throws Exception {
        try {
            JsonArray statisticsInfoArray = new JsonArray();
            JsonObject sysIdObject = GsonUtils.fromJson(sysIdParas, JsonObject.class);
            if (!sysIdObject.has("sysIds")) {
                return new ResponseEntity<Object>(
                        "Failed to query the related tickets because field \"sysIds\" is not provided.",
                        HttpStatus.BAD_GATEWAY);
            }
            JsonArray sysIdList = sysIdObject.get("sysIds").getAsJsonArray();
            for (JsonElement sysId : sysIdList) {
                String ciItem = sysId.getAsString();
                JsonObject statisticsInfo = queryTicketsRelatedToSourceBySysId(tag, ciItem);
                statisticsInfo.addProperty("sys_id", ciItem);
                statisticsInfoArray.add(statisticsInfo);
            }
            return new ResponseEntity<>(gson.toJson(statisticsInfoArray), HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in batchQueryTicketsRelatedToSourceBySysIds method: ", e);
            return new ResponseEntity<Object>(
                    "Failed to query the related tickets and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<Object> batchQueryTicketsRelatedToSourceByCorrelationIds(String tag,
            String correlationIdsParas)
            throws Exception {
        try {
            JsonArray statisticsInfoArray = new JsonArray();
            JsonObject correlationIdObject = GsonUtils.fromJson(correlationIdsParas, JsonObject.class);
            JsonArray correlationIdList = correlationIdObject.get("correlationIds").getAsJsonArray();
            for (JsonElement correlationId : correlationIdList) {
                String ciCorrelationId = correlationId.getAsString();
                JsonObject statisticsInfo = queryTicketsRelatedToSourceByCorrelationId(tag, ciCorrelationId);
                statisticsInfo.addProperty("correlation_id", ciCorrelationId);
                statisticsInfoArray.add(statisticsInfo);
            }
            return new ResponseEntity<>(gson.toJson(statisticsInfoArray), HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in batchQueryTicketsRelatedToSourceByCorrelationIds method: ", e);
            return new ResponseEntity<Object>(
                    "Failed to query the related tickets and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }

    public ResponseEntity<Object> batchQueryTicketsRelatedToSourceByNamingRulePayloads(String tag,
            String namingRulePayloads) throws Exception {
        try {
            JsonArray statisticsInfoArray = new JsonArray();
            JsonObject payloadObjects = GsonUtils.fromJson(namingRulePayloads, JsonObject.class);
            JsonArray payloadList = payloadObjects.get("payloads").getAsJsonArray();
            for (JsonElement payload : payloadList) {
                JsonObject payloadJsonObject = payload.getAsJsonObject();
                JsonObject statisticsInfo = queryTicketsRelatedToSourceByPayload(tag, payloadJsonObject);
                statisticsInfoArray.add(statisticsInfo);
            }
            return new ResponseEntity<>(gson.toJson(statisticsInfoArray), HttpStatus.OK);
        } catch (Exception e) {
            LOG.error("There is a error in batchQueryTicketsRelatedToSourceByNamingRulePayloads method: ", e);
            return new ResponseEntity<Object>(
                    "Failed to query the related tickets and the fail cause is " + e.getMessage(),
                    HttpStatus.BAD_GATEWAY);
        }
    }
}
