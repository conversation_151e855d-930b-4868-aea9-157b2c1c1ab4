/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/zrdds/ext/api/v1")
@Tag(name = "Scheduled Job", description = "This API focus on Scheduled job ")
public interface QuartzApi {
        @Operation(summary = "Create a scheduled job", description = "Create a scheduled job <br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/scheduledJob/create/{jobType}/{jobName}")
        @ResponseBody
        ResponseEntity<String> createScheduledJob(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate what type of job you want to create. Currently we support servicenow.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, name = "jobName", description = "Indicate the name of the job crteated.", required = true) @PathVariable String jobName)
                        throws Exception;

        @Operation(summary = "Update a scheduled job", description = "Update a scheduled job <br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/scheduledJob/update/{jobType}/{jobName}")
        @ResponseBody
        ResponseEntity<String> updateScheduledJob(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate what type of job to update. Currently we support servicenow.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, name = "jobName", description = "Indicate which job to update.", required = true) @PathVariable String jobName)
                        throws Exception;

        @Operation(summary = "Pause a scheduled job", description = "Pause a scheduled job <br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/scheduledJob/pause/{jobType}/{jobName}")
        @ResponseBody
        ResponseEntity<String> pauseScheduledJob(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate what type of job to pause.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, name = "jobName", description = "Indicate which job to pause.", required = true) @PathVariable String jobName)
                        throws Exception;

        @Operation(summary = "Resume a scheduled job", description = "Resume a scheduled job <br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/scheduledJob/resume/{jobType}/{jobName}")
        @ResponseBody
        ResponseEntity<String> resumeScheduledJob(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate what type of job to resume.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, name = "jobName", description = "Indicate which job to resume.", required = true) @PathVariable String jobName)
                        throws Exception;

        @Operation(summary = "Run a scheduled job once", description = "Run a scheduled job once <br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/scheduledJob/runOnce/{jobType}/{jobName}")
        @ResponseBody
        ResponseEntity<String> runScheduledJobOnce(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate what type of job to resume.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, name = "jobName", description = "Indicate which job to resume.", required = true) @PathVariable String jobName)
                        throws Exception;

        @Operation(summary = "Delete a scheduled job", description = "Delete a scheduled job<br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/scheduledJob/delete/{jobType}/{jobName}")
        @ResponseBody
        ResponseEntity<String> deleteScheduledJob(
                        @Parameter(in = ParameterIn.PATH, name = "jobType", description = "Indicate what type of job to resume.", required = true) @PathVariable String jobType,
                        @Parameter(in = ParameterIn.PATH, name = "jobName", description = "Indicate which job to resume.", required = true) @PathVariable String jobName)
                        throws Exception;
}
