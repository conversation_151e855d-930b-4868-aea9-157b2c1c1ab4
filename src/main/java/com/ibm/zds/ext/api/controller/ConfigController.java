/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.controller;

import com.ibm.zds.ext.api.api.ConfigApi;
import com.ibm.zds.ext.api.service.ProcessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class ConfigController implements ConfigApi {

    private static final Logger LOG = LoggerFactory.getLogger(ConfigController.class);

    @Autowired
    ProcessService processService;

    public ResponseEntity<String> getConfig(@PathVariable String jobType, @PathVariable String jobName)
            throws Exception {
        LOG.info("Received GET request to get the configs for job " + jobName);
        return processService.getConfig(jobType, jobName);
    }

    public ResponseEntity<String> initConfig(@PathVariable String jobType, @PathVariable String jobName,
            @RequestBody Map<String, String> config) throws Exception {
        LOG.info("Received POST request to initialize the configs for job " + jobName);
        return processService.initConfig(jobType, jobName, config);
    }

    public ResponseEntity<String> updateConfig(@PathVariable String jobType, @PathVariable String jobName,
            @RequestBody Map<String, String> config) throws Exception {
        LOG.info("Received POST request to update the configs for job " + jobName);
        return processService.updateConfig(jobType, jobName, config);
    }
}
