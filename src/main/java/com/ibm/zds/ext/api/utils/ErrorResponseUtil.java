/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.utils;


import com.ibm.zds.ext.api.vo.AbnormalInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public class ErrorResponseUtil {
    private static final Logger log = LoggerFactory.getLogger(ErrorResponseUtil.class);

    public static ResponseEntity<Object> getResponse(Exception e) {

        log.error(e.getMessage());
        log.error(e.getCause().getMessage());

        String msg  = "";
        if (e.getMessage() != null && !e.getMessage().isEmpty()) {
            msg = e.getMessage().split(":")[0];
        }

        AbnormalInfo abnormalInfo = new AbnormalInfo();
        abnormalInfo.setError(msg);

        /**
         * Error process
         */
        if (e instanceof Exception) {
            abnormalInfo.setMessage("Error");
            return new ResponseEntity<>(abnormalInfo, HttpStatus.SERVICE_UNAVAILABLE);}
        else {
            abnormalInfo.setMessage("INTERNAL SERVER ERROR");
            return new ResponseEntity<>(abnormalInfo, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
