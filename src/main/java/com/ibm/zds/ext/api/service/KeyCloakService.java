/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

 package com.ibm.zds.ext.api.service;

 import com.google.gson.reflect.TypeToken;
 import com.ibm.zds.ext.api.utils.GsonUtils;
 import com.ibm.zds.ext.api.utils.HttpUtils;
 import org.springframework.context.annotation.Configuration;
 import org.springframework.stereotype.Component;
 
 import java.util.HashMap;
 import java.util.Map;
 
 @Configuration
 @Component
 public class KeyCloakService {
 
     // @Value("${keycloak.token.uri}")
     // protected String keycloakTokenUri;
     protected String keycloakTokenUri = "http://localhost:8080/auth/realms/Test/protocol/openid-connect/token";
 
     public static void main(String[] args) {
         KeyCloakService kcService = new KeyCloakService();
 
         String clientId = "clientId";
         String clientSecret = "clientSecret";
 
         try {
             String accessToken = kcService.getAccessTokenByClientCredentials(clientId, clientSecret);
             System.out.println(accessToken);
             String authorizationStr = kcService.getAuthorizationByClientCredentials(clientId, clientSecret);
             System.out.println(authorizationStr);
             String scope = kcService.validateTokenByClientCredentials(clientId, clientSecret, accessToken);
             System.out.println(scope);
         } catch (Exception e) {
             System.out.println(e);
         }
     }
 
     public String getAccessTokenByClientCredentials(String clientId, String clientSecret) throws Exception {
 
         HashMap<String, String> body = new HashMap<>();
         body.put("client_id", clientId);
         body.put("client_secret", clientSecret);
         body.put("grant_type", "client_credentials");
 
         return executeGetAccessToken(body);
     }
 
     public String getAuthorizationByClientCredentials(String clientId, String clientSecret) throws Exception {
         String authorizationStr = "Bearer " + getAccessTokenByClientCredentials(clientId, clientSecret);
         return authorizationStr;
     }
 
     public String getAccessTokenByPassword(String clientId, String username, String password) throws Exception {
 
         HashMap<String, String> body = new HashMap<>();
         body.put("client_id", clientId);
         body.put("username", username);
         body.put("password", password);
         body.put("grant_type", "password");
 
         return executeGetAccessToken(body);
     }
 
     private String executeGetAccessToken(HashMap<String, String> body) throws Exception {
 
         String bodyStr = HttpUtils.formatMapContent(body, false);
         String res = HttpUtils.post(keycloakTokenUri, null, null, bodyStr, true);
 
         Map<String, String> m = GsonUtils.fromJson(res, new TypeToken<Map<String, String>>() {}.getType());
 
         System.out.println(m);
 
         return m.get("access_token");
     }
 
     public String validateTokenByClientCredentials(String clientId, String clientSecret, String accessToken)
             throws Exception {
 
         HashMap<String, String> body = new HashMap<>();
         body.put("client_id", clientId);
         body.put("client_secret", clientSecret);
         body.put("grant_type", "client_credentials");
         body.put("token", accessToken);
 
         return executeValidateToken(body);
     }
 
     public String validateTokenByPassword(String clientId, String username, String password, String accessToken)
             throws Exception {
 
         HashMap<String, String> body = new HashMap<>();
         body.put("client_id", clientId);
         body.put("username", username);
         body.put("password", password);
         body.put("grant_type", "password");
         body.put("token", accessToken);
 
         return executeValidateToken(body);
     }
 
     private String executeValidateToken(HashMap<String, String> body) throws Exception {
 
         String bodyStr = HttpUtils.formatMapContent(body, false);
         String res = HttpUtils.post(keycloakTokenUri + "/introspect", null, null, bodyStr, true);
 
         Map<String, String> m = GsonUtils.fromJson(res, new TypeToken<Map<String, String>>() {}.getType());
 
         System.out.println(m);
 
         return m.get("scope").toString();
     }
 }
 