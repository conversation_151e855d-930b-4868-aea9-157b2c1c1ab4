/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.db;

import com.ibm.zds.ext.api.utils.GsonUtils;
import com.ibm.zds.ext.database.DatabaseService;
import com.ibm.zds.ext.database.entity.snow.*;
import com.ibm.zds.ext.database.mapper.snow.SnowMapper;
import org.apache.ibatis.session.SqlSession;

import java.util.List;
import java.util.Map;

public class QueryService {

    private SqlSession session;
    private SnowMapper snowMapper;

    public QueryService() {
        this.session = DatabaseService.getDatabaseService().getSqlSession();
        this.snowMapper = this.session.getMapper(SnowMapper.class);
    }

    public static void main(String[] args) throws Exception {
        // String jsonStr =
        // "{\"users\":[],\"incidents\":[{\"caller\":\"71826bf03710200044e0bfc8bcbe5d3b\",\"short_description\":\"sample
        // short
        // description\",\"resolution_code\":\"\",\"rfc\":\"bb55a0cec35ac210e1851c377d013173\",\"caused_by_change\":\"\",\"problem\":\"6058984ec31ac210e1851c377d013149\",\"number\":\"INC0010114\",\"state\":\"1\",\"priority\":\"5\",\"impact\":\"3\",\"urgency\":\"3\",\"assignment_group\":\"72910dd8779050108a370870a81061a9\",\"assigned_to\":\"\",\"last_updated\":\"2024-05-22
        // 08:45:46\",\"description\":\"sample
        // description\",\"configuration_item\":\"\",\"work_notes\":\"\",\"sys_id\":\"39b694c2c31ac210e1851c377d013116\"},{\"caller\":\"2e826bf03710200044e0bfc8bcbe5de0\",\"short_description\":\"sample
        // caused by
        // incident\",\"resolution_code\":\"\",\"rfc\":\"\",\"caused_by_change\":\"bb55a0cec35ac210e1851c377d013173\",\"problem\":\"\",\"number\":\"INC0010119\",\"state\":\"1\",\"priority\":\"5\",\"impact\":\"3\",\"urgency\":\"3\",\"assignment_group\":\"c38f00f4530360100999ddeeff7b1298\",\"assigned_to\":\"\",\"last_updated\":\"2024-05-22
        // 08:46:07\",\"description\":\"sample caused by
        // incident\",\"configuration_item\":\"\",\"work_notes\":\"\",\"sys_id\":\"85b6e442c39ac210e1851c377d013159\"}]}";
        // JsonObject jsonObject = GsonUtils.parseString(jsonStr).getAsJsonObject();
        // QueryService persistService = new QueryService();
        // persistService.processData(jsonObject);
        QueryService persistService = new QueryService();
        persistService.test();
    }

    public void closeSession() {
        DatabaseService.getDatabaseService().closeSqlSession(this.session);
    }

    public List<Tag> getAllTags() {
        return this.snowMapper.findTagAll();
    }

    public List<Incident> getIncidentByTagAndSysID(String tag, String sysID) {
        return this.snowMapper.findIncidentByTagAndSysID(tag, sysID);
    }

    public List<Problem> getProblemByTagAndSysID(String tag, String sysID) {
        return this.snowMapper.findProblemByTagAndSysID(tag, sysID);
    }

    public List<ChangeRequest> getChangeRequestByTagAndSysID(String tag, String sysID) {
        return this.snowMapper.findChangeRequestByTagAndSysID(tag, sysID);
    }

    public List<Map<String, String>> getAffectedCIByTagAndCIItem(String tag, String ciItem) {
        return this.snowMapper.findAffectedCIByTagAndCIItem(tag, ciItem);
    }

    public List<Map<String, String>> getAffectedCIByTagAndCICorrelationID(String tag, String ciCorrelationID) {
        return this.snowMapper.findAffectedCIByTagAndCICorrelationID(tag, ciCorrelationID);
    }

    private void test() {
        List<ConfigurationItem> list = this.snowMapper.findConfigurationItemByTagAndSysID(null, null);
        System.out.println(GsonUtils.toJsonStringWithNull(list));
    }

}
