/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.configuration;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.stream.Collectors;

@Component
public class MethodFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(MethodFilter.class);

    @Autowired
    Environment env;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String allowListStr = env.getProperty("host.allowlist");
        if (allowListStr != null && !allowListStr.isEmpty()) {
            String[] allowlist = allowListStr.split(",");
            String serverName = request.getServerName();

            if (!Arrays.stream(allowlist).collect(Collectors.toList()).contains(serverName)) {
                response.sendError(HttpServletResponse.SC_PRECONDITION_FAILED, "Mismatch ServerName");
                return;
            }
        }

        // filter the method
        if (request.getMethod().equals("OPTIONS") || request.getMethod().equals("DELETE")) {
            response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
            return;
        }

        String uri = request.getRequestURI();
        // only check those api data requests
        if (uri.contains("/api/")) {

            String authMode = env.getProperty("authentication.mode");
            if (!authMode.toLowerCase().equals("local") && !authMode.toLowerCase().equals("keycloak")) {
                response.sendError(HttpServletResponse.SC_PRECONDITION_FAILED,
                        "Illegal 'authentication.mode', 'local' or 'keycloak'.");
                log.warn("get illegal authMode: " + authMode);
                return;
            }

            if (authMode.toLowerCase().equals("local")) {
                String tokenString = env.getProperty("api.token");
                if (tokenString.isEmpty() || tokenString.length() < 32) {
                    // check the api.token firstly, require at least 32 bits
                    response.sendError(HttpServletResponse.SC_PRECONDITION_FAILED,
                            "Illegal 'api.token', at least 32 bits.");
                    log.warn("The api.token is too short, at least 32 bits");
                    return;
                } else {
                    // check the ApiToken
                    String apiToken = request.getHeader("ApiToken");
                    if (apiToken == null || apiToken.isEmpty() || !apiToken.equals(tokenString)) {
                        response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "ApiToken mismatch locally");
                        log.warn("Get mismatched local ApiToken in request header:" + apiToken);
                        return;
                    }
                }
            }
        }

        filterChain.doFilter(request, response);
    }
}
