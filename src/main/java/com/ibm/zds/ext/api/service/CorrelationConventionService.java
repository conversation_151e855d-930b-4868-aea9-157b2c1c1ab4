/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/
package com.ibm.zds.ext.api.service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Comparator;

import org.apache.ibatis.io.Resources;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import com.google.gson.JsonObject;
import com.ibm.zds.ext.api.utils.XMLUtil;
import java.io.InputStream;

import lombok.Getter;
import lombok.Setter;

public class CorrelationConventionService {
    public class NamingRule {
        @Getter
        @Setter
        private String name;
        @Getter
        private Integer priority;
        @Getter
        @Setter
        private String format;
        @Getter
        @Setter
        private String suffix;
        @Getter
        @Setter
        private ArrayList<String> requiredFields;

        private NamingRule(String name, Integer priority) throws Exception {
            this.name = name;
            this.priority = priority;
            this.requiredFields = new ArrayList<String>();
        }
    }

    private static final String REQUIREDFIELD = "RequiredField";
    private static final String PRIORITY = "priority";
    private static final String POLICY = "policy";
    private static final String SOURCE = "source";
    private static final String NAME = "name";
    private static final String SUFFIX = "suffix";
    private static final String FORMAT = "CorrelationFormat";
    private static final String NAMINGRULE = "NamingRule";
    private static final String REQUIRED = "required";
    private static final String SOURCETYPE = "sourceType";
    private static final String CLASSTYPE = "classType";

    private HashMap<String, ArrayList<NamingRule>> namingRulesMap;
    private HashMap<String, String> mappings;

    private static CorrelationConventionService parser;

    /**
     * Private constructor for a singleton class
     * 
     * @throws Exception thrown when there is a parsing problem with NamingRule.xml
     */
    public CorrelationConventionService(String basedir) {
        namingRulesMap = new HashMap<String, ArrayList<NamingRule>>();
        mappings = new HashMap<String, String>();
    }

    public void readIdConventionRules() throws Exception {

        Node node = null;
        String policy;
        String source;
        source = "NamingRules.xml";
        InputStream namingRules = Resources.getResourceAsStream(source);

        Document domNR = XMLUtil.getDOM(source, namingRules);
        Element nRElement = domNR.getDocumentElement();

        List<Element> mappings = XMLUtil.getChildElementsByTagName(nRElement,
                "Mapping");

        for (Element map : mappings) {
            node = map;
            source = XMLUtil.getAttrValue(node, SOURCE);
            policy = XMLUtil.getAttrValue(node, POLICY);
            this.mappings.put(source, policy);
        }

        List<Element> policies = XMLUtil.getChildElementsByTagName(nRElement,
                "CorrelationPolicy");

        populateRules(policies);
    }

    private void populateRules(List<Element> policies) throws Exception {
        Node policyNode = null;
        Node namingRuleNode = null;
        Node fieldNode = null;
        Element policyElement = null;
        Element fieldElement = null;
        Element namingRuleElement = null;
        String policyName = null;
        String namingRuleName = null;
        Integer priority = null;
        String suffix = null;
        Node formatNode = null;
        String format = null;
        ArrayList<String> requiredFields = null;
        ArrayList<NamingRule> namingRules = null;
        List<Element> requiredFieldsList = null;
        List<Element> namingRuleList = null;
        for (Element policy : policies) {
            namingRules = new ArrayList<NamingRule>();
            policyNode = policy;
            if (policyNode != null && policyNode.getNodeType() == Node.ELEMENT_NODE) {
                policyElement = (Element) policyNode;
                policyName = policyElement.getAttribute(NAME);
                suffix = policyElement.getAttribute(SUFFIX);
                namingRuleList = XMLUtil.getChildElementsByTagName(policyElement,
                        NAMINGRULE);
                for (Element singleNamingRule : namingRuleList) {
                    namingRuleNode = (Element) singleNamingRule;
                    if (namingRuleNode != null && namingRuleNode.getNodeType() == Node.ELEMENT_NODE) {
                        namingRuleElement = (Element) namingRuleNode;
                        formatNode = XMLUtil.getChildElementsByTagName(namingRuleElement, FORMAT).get(0);
                        format = XMLUtil.getAttrValue(formatNode,
                                "value");
                        requiredFields = new ArrayList<String>();
                        requiredFieldsList = XMLUtil.getChildElementsByTagName(namingRuleElement, REQUIREDFIELD);
                        for (Element field : requiredFieldsList) {
                            fieldNode = (Element) field;
                            if (fieldNode != null && fieldNode.getNodeType() == Node.ELEMENT_NODE) {
                                fieldElement = field;
                                if (fieldElement.getAttribute(REQUIRED).equals("true")) {
                                    requiredFields.add(fieldElement.getAttribute(NAME));
                                }
                            }
                        }
                        namingRuleName = namingRuleElement.getAttribute(NAME);
                        priority = Integer.parseInt(namingRuleElement.getAttribute(PRIORITY));
                        NamingRule namingRule = new NamingRule(namingRuleName, priority);
                        namingRule.setFormat(format);
                        namingRule.setRequiredFields(requiredFields);
                        namingRule.setSuffix(suffix);
                        namingRules.add(namingRule);
                    }
                }
                namingRules.sort(Comparator.comparing(NamingRule::getPriority));
                namingRulesMap.put(policyName, namingRules);
            }
        }
        System.currentTimeMillis();
    }

    private String getPolicy(String sourceType, String classType) throws Exception {
        String source = sourceType + "." + classType;
        if (this.mappings.containsKey(source)) {
            return this.mappings.get(source);
        } else {
            throw new Exception(MessageFormat.format(
                    "The naming rule policy for sourceType {0} and classType {1} is not found", sourceType, classType));
        }
    }

    private String matchPolicy(String policyName, JsonObject payload) throws Exception {
        if (this.namingRulesMap.containsKey(policyName)) {
            String correlationId = matchNamingRules(payload, namingRulesMap.get(policyName));
            return correlationId;
        } else {
            throw new Exception(MessageFormat.format(
                    "The naming policy {0} has not been defined yet.", policyName));
        }
    }

    private String matchNamingRules(JsonObject payload, ArrayList<NamingRule> namingRuleList) throws Exception {
        Integer length = namingRuleList.size();
        for (int i = 0; i < length; i++) {
            NamingRule namingRule = namingRuleList.get(i);
            if (containsAllFields(payload, namingRule.getRequiredFields())) {
                return formatCorrelationId(payload,namingRule.getFormat(),namingRule.getSuffix());
            }
        }
        throw new Exception(
                "Cannot find a matched naming rule for the payload.");
    }

    private Boolean containsAllFields(JsonObject payload, ArrayList<String> fields) throws Exception {
        Boolean containsAll = true;
        for (String requiredField : fields) {
            if (!payload.has(requiredField)) {
                containsAll = false;
            }
        }
        return containsAll;
    }

    private String formatCorrelationId(JsonObject payload, String format, String suffix) throws Exception {
        String[] requiredFields = format.split("-");
        Integer length = requiredFields.length;
        // Convert the array to an ArrayList
        for (int i = 0; i < length; i++){
            String field = requiredFields[i];
            if (!field.equals(SUFFIX)){
                requiredFields[i] = payload.get(field).getAsString();
            }
            else{
                requiredFields[i] = suffix;
            }
        }
        return String.join("-", requiredFields);
    }

    public String getCorrelationId(JsonObject payload) throws Exception {
        if (payload.has(SOURCETYPE) && payload.has(CLASSTYPE)) {
            String sourceType = payload.get(SOURCETYPE).getAsString();
            String classType = payload.get(CLASSTYPE).getAsString();
            String policyName = getPolicy(sourceType, classType);
            return matchPolicy(policyName, payload);
        } else {
            throw new Exception("Field \"classType\" or \"sourceType\" not provided.");
        }
    }

    public static void main(String[] args) {
        try {
            parser = new CorrelationConventionService(".");
            parser.readIdConventionRules();
            JsonObject json = new JsonObject();
            json.addProperty("sourceType", "ZVS");
            json.addProperty("classType", "mqAliasQueue");
            json.addProperty("correlationId", "correlationId");
            json.addProperty("DLAId", "DLAId");
            json.addProperty("smfId", "smfId");
            json.addProperty("lparName", "lparName");
            json.addProperty("sysplexLabel", "sysplexLabel");
            json.addProperty("manufacturer", "IBM");
            json.addProperty("model", "model");
            json.addProperty("name", "name");
            json.addProperty("cicsRegionName", "cicsRegionName");
            json.addProperty("sysplexName", "sysplexName");
            json.addProperty("subsystemName", "subsystemName");
            json.addProperty("sharingGroupName", "sharingGroupName");
            json.addProperty("logicalPartitionHost", "logicalPartitionHost");
            json.addProperty("lparId", "lparId");
            json.addProperty("serialNumber", "serialNumber");
            json.addProperty("mqManager", "mqManager");
            System.out.println(parser.getCorrelationId(json));
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
    }
}
