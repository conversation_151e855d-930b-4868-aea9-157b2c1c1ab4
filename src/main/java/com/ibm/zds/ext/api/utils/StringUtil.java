/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.utils;

import java.util.Arrays;
import java.util.List;

public class StringUtil {

    public static String[] splitStringByComma(String src) {
        if (src == null || src.equals("")) {
            return new String[]{};
        } else {
            String[] sArr = src.split(",");
            List<String> sl = Arrays.asList(sArr);
            sl.forEach(item -> item.trim());
            sArr = sl.toArray(new String[sl.size()]);
            return sArr;
        }
    }

    public static String getClassTypeByDlaId(String dlaId) {
        String classType;
        if (dlaId.contains("CICS_SIT_Overrides")) {
            classType = "CICSSitOverrides";
        } else if (dlaId.contains("CICS_SIT")) {
            classType = "CICSSit";
        } else {
            String[] strings = dlaId.split("-");
            classType = strings[strings.length - 1];
        }

        // special for CICSProgram
        if (classType.equals("CICSProgram")) {
            classType = "Program";
        }

        return classType;
    }

    public static String getRelationFromClassName(String className) {
        String[] strings = className.split("\\.");
        String relationName = strings[strings.length - 1];
        // Initial to lowercase
        if (Character.isLowerCase(relationName.charAt(0))) {
            return relationName;
        } else {
            return (new StringBuilder()).append(Character.toLowerCase(relationName.charAt(0))).append(relationName.substring(1)).toString();
        }
    }

    public static String replaceClassTypeInDlaID(String dlaId, String classType) {
        String[] strings = dlaId.split("-");
        strings[strings.length - 1] = classType;
        String newDlaId = String.join("-", strings);
        return newDlaId;
    }

}
