/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.zds.ext.api.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/zrdds/ext/api/v1")
@Tag(name = "Query Tickets", description = "This API focus on query tickets from source")
public interface QueryApi {

        @Operation(summary = "Query a specific ticket", description = "Query a specific ticket <br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @GetMapping(value = "/query/ticket/{ticketType}/{sys_id}")
        @ResponseBody
        ResponseEntity<Object> querySpecificTicket(
                        @Parameter(in = ParameterIn.PATH, name = "ticketType", description = "ticket type like incidents", required = true) @PathVariable String ticketType,
                        @Parameter(in = ParameterIn.PATH, name = "sys_id", description = "ticket sys_id", required = true) @PathVariable String sys_id,
                        @Parameter(name = "tag", description = "indicate the tag of the data") @RequestParam(required = false) String tag)
                        throws Exception;

        @Operation(summary = "Get the tags for the data", description = "Get the tags for the data <br>" +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @GetMapping(value = "/query/tags")
        @ResponseBody
        ResponseEntity<Object> getTags(
                        @Parameter(description = "Determine whether to get all tags. If no, return the latest tag.") @RequestParam(value = "allTags", required = true) Boolean getAllTag)
                        throws Exception;

        @Operation(summary = "Query a list of specific tickets with high level information", description = "Query a list of specific tickets with high level information <br>"
                        +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/query/tickets")
        @ResponseBody
        ResponseEntity<Object> queryTicketsWithHighLevelInformation(
                        @Parameter(name = "tag", description = "indicate the tag of the data") @RequestParam(required = false) String tag,
                        @Parameter(in = ParameterIn.PATH, name = "tickets", description = "ticket parameters containing tickets information including sys_ids", required = true) @RequestBody String ticketParas)
                        throws Exception;

        @Operation(summary = "Query statistic info of SNOW tickets against a list of specific zOS resources by sysIds", description = "Query statistic info of SNOW tickets against a list of specific zOS resources by sys_ids <br>"
                        +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/query/source/ticket4SysIds")
        @ResponseBody
        ResponseEntity<Object> queryTicketsRelatedToSourceBySysIds(
                        @Parameter(name = "tag", description = "indicate the tag of the data") @RequestParam(required = false) String tag,
                        @Parameter(name = "sys_ids", description = "parameters containing source information including sysIds", required = true) @RequestBody String sysIdParas)
                        throws Exception;

        @Operation(summary = "Query statistic info of SNOW tickets against a list of specific zOS resources by correlation ids", description = "Query statistic info of SNOW tickets against a list of specific zOS resources by correlation ids <br>"
                        +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/query/source/ticket4CorrelationIds")
        @ResponseBody
        ResponseEntity<Object> queryTicketsRelatedToSourceByCorrelationIds(
                        @Parameter(name = "tag", description = "indicate the tag of the data") @RequestParam(required = false) String tag,
                        @Parameter(name = "corrleation_ids", description = "parameters containing source information including correlationIds", required = true) @RequestBody String correlationIdsParas)
                        throws Exception;

        @Operation(summary = "Query statistic info of SNOW tickets against a list of specific zOS resources by naming rule payloads", description = "Query statistic info of SNOW tickets against a list of specific zOS resources by naming rule payloads <br>"
                        +
                        "return response body: <br>" +
                        "statusCode: '0' -> OK, other -> ERROR <br>" +
                        "message: the message or error message <br>" +
                        "data: the result data from the process")
        @PostMapping(value = "/query/source/ticket4NamingRulePayloads")
        @ResponseBody
        ResponseEntity<Object> queryTicketsRelatedToSourceByNamingRulePayloads(
                        @Parameter(name = "tag", description = "indicate the tag of the data") @RequestParam(required = false) String tag,
                        @Parameter(name = "naming_rule_payloads", description = "naming rule payloads of the resource in servicenow", required = true) @RequestBody String namingRulePayloads)
                        throws Exception;

}
