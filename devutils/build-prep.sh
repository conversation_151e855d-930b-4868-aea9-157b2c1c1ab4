#!/bin/bash
BASEDIR=${PWD}
export ARCH="$( uname -m )"
if [ "${ARCH}" = "x86_64" ]
then
  export OPENJDK_ARCH="x64"
  export UBU_ARCH="amd64"
elif [ "${ARCH}" = "arm64" ]
then
  export OPENJDK_ARCH="aarch64"
  export UBU_ARCH=${ARCH}
else
  export OPENJDK_ARCH=${ARCH}
  export UBU_ARCH=${ARCH}
fi
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
RED=$(tput setaf 1)
GREEN=$(tput setaf 2)
YELLOW=$(tput setaf 3)
BLUE=$(tput setaf 4)
ENDCOLOR=$(tput sgr0)

sed() {
  if [ "$( uname -s )" == "Darwin" ]
  then
    which gsed > /dev/null 2>&1
    if [ $? == 0 ]
    then
      gsed "$@"
    else
      echo "WARNING: Running on MacOS, but no GNU sed found."
    fi
  else
    command sed "$@"
  fi
}

echo ""
set -a
if [ -f ${HOME}/.zoalocal ]
then
  . ${HOME}/.zoalocal
else
  printf "${YELLOW}WARNING: Development configuration file '.zoalocal' not found in directory '${HOME}'.${ENDCOLOR}\n"
fi
cat zoa_env.config.* > zoa_env.config
cat .zoa_factory.config.* > .zoa_factory.config
if [ -f ${BASEDIR}/${NEW_CONFIG} ]
then
  . ${BASEDIR}/${NEW_CONFIG}
else
  printf "${YELLOW}WARNING: Production configuration file '${NEW_CONFIG}' not found in directory '${BASEDIR}'.${ENDCOLOR}\n"
fi
if [ -f ${BASEDIR}/${IBM_CONFIG} ]
then
  . ${BASEDIR}/${IBM_CONFIG}
else
  printf "${YELLOW}WARNING: Production configuration file '${IBM_CONFIG}' not found in directory '${BASEDIR}'.${ENDCOLOR}\n"
fi
if [ -f ${BASEDIR}/.buildenv ]
then
  . ${BASEDIR}/.buildenv
else
  printf "${YELLOW}WARNING: Build configuration file '.buildenv' not found in directory '${BASEDIR}'.${ENDCOLOR}\n"
fi
set +a

echo ""
echo "Setting ARCH tags to '${ARCH}'..."
sed -i -e "s%-x86_64$%-${ARCH}%g" docker-compose-dev.yml
for DFILE in $( python3 ${BASEDIR}/devutils/read-dc-yaml.py -f docker-compose-dev.yml )
do
  sed -i -e "s%-x86_64$%-${ARCH}%g" ${DFILE}
  sed -i -e "s%-x86_64\ AS%-${ARCH}\ AS%g" ${DFILE}
  sed -i -e "s%/x86_64/%/${ARCH}/%g" ${DFILE}
done
echo ""
