#!/bin/bash
SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
export BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
ARCH="$( uname -m )"
cd ${BASEDIR}

sed() {
  if [ "$( uname -s )" == "Darwin" ]
  then
    which gsed > /dev/null 2>&1
    if [ $? == 0 ]
    then
      gsed "$@"
    else
      echo "WARNING: Running on MacOS, but no GNU sed found."
    fi
  else
    command sed "$@"
  fi
}

echo ""
echo "Setting ARCH tags back to 'x86_64'..."
sed -i -e "s%-${ARCH}$%-x86_64%g" docker-compose-dev.yml
for DFILE in $( python3 ${BASEDIR}/devutils/read-dc-yaml.py -f docker-compose-dev.yml )
do
  sed -i -e "s%-${ARCH}$%-x86_64%g" ${DFILE}
  sed -i -e "s%-${ARCH}\ AS%-x86_64\ AS%g" ${DFILE}
  sed -i -e "s%/${ARCH}/%/x86_64/%g" ${DFILE}
done
rm zoa_env.config .zoa_factory.config
echo ""
