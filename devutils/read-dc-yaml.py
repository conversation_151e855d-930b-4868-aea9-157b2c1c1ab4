#!/usr/bin/python

import os
import yaml
import json
import argparse


def parse_file(infile):
    with open(infile, 'r') as yamlfile:
        yaml_data = yaml.safe_load(yamlfile)

    servicecount = len(yaml_data['services'])
    for svc in yaml_data['services'].keys():
        print(yaml_data['services'][svc]['build']['context'] + '/' + yaml_data['services'][svc]['build']['dockerfile'])


def main():
    """Main"""
    parser = argparse.ArgumentParser(
        description="""
                Extract path to Dockerfiles from docker compose YAML file.
                    """,
        usage="""
    python3 read-dc-yaml.pyy --file <docker_compose_file>
                """)
    required_args = parser.add_argument_group('required arguments')
    required_args.add_argument('-f', "--file", help="Path to docker compose file to be parsed", required=True)
    args = parser.parse_args()

    yaml_file = args.file
    assert os.path.exists(yaml_file), 'Unable to find file ' + str(yaml_file) + '.'
    assert os.path.isfile(yaml_file), str(yaml_file) + ' is not a file.'

    parse_file(yaml_file)


if __name__ == "__main__":
    main()
