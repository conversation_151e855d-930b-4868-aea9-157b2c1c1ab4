## Settings for local ZOA work
# OCI_AGENT: controls which OCI runtime tool to use. Use of 'podman' is not supported at this time, but might be in the future
OCI_AGENT=docker
# OCI_VERSION: set to the version number (not the whole VRM) of the Docker release installed in your environment; e.g., '24' if you are running Docker 24.0.2-rd
OCI_VERSION=
# AFUSER: your W3 user ID
AFUSER=
# AFTOKEN: the Artifactory token set up for your W3 user ID
AFTOKEN=
# GHUSER: GitHub user ID for access to GHE repos from inside of build containers; identical to yuor W3 'short ID', e.g. 'vbsiegem'
GHUSER=
# GHTOKEN: base64-encoded GHE PAT (personal access token): Settings > Developer Settings > Personal access tokens > Tokens (classic)
GHTOKEN=
# IBMCLOUD_API_KEY: IBM Cloud API key; required to access ICCR for pulling (or pushing) images
IBMCLOUD_API_KEY=
# WORKDIR: full path to TEMPWORK directory
WORKDIR=
# OSSFORKS: full path to the directory containing all the 'fork' repos maintained in the ZLDAforks GHE organization
OSSFORKS=
# METRICML_ENABLED: change to true if you want to be able to run metric ML containers
METRICML_ENABLED=false
# METRICML_SOURCE: if METRICML_ENABLED=true, set to the absolute path of your local metric-ml-docker-MF repo; e.g., /Users/<USER>/IdeaProjects/IZOA/ZAA/metric-ml-docker-MF
METRICML_SOURCE=
# LOGMSGML_ENABLED: change to true if you want to be able to run log-based ML containers
LOGMSGML_ENABLED=false
# LOGMSGML_SOURCE: if LOGMSGML_ENABLED=true, set to the absolute path of your local izoa-zaware-docker-MF repo; e.g., /Users/<USER>/IdeaProjects/IZOA/ZAA/izoa-zaware-docker-MF
LOGMSGML_SOURCE=
# ZAACOMMON_ENABLED: change to true if you want to be able to run log-based ML containers
ZAACOMMON_ENABLED=false
# ZAACOMMON_SOURCE: if ZAACOMMON_ENABLED=true, set to the absolute path of your local izoa-zaware-docker-MF repo; e.g., /Users/<USER>/IdeaProjects/IZOA/ZAA/izoa-zaware-docker-MF
ZAACOMMON_SOURCE=
# ZTOPO_ENABLED: change to true if you want to be able to run ZRDDS-related containers
ZTOPO_ENABLED=false
# SNOW_ENABLED: change to true if you want to be able to run ServiceNow CMDB integration containers
SNOW_ENABLED=false
# ZDAP_ENABLED: change to true if you want to be able to run ZDAP containers
ZDAP_ENABLED=false
# ZLDA_SOURCE: if ZDAP_ENABLED=true; set to the absolute path of the directory in which all your ZLDA repos are located; e.g., /Users/<USER>/IdeaProjects/ZLDA
ZLDA_SOURCE=
###############################################################################
# DO NOT CHANGE BELOW THIS LINE UNLESS YOU KNOW WHAT YOU ARE DOING...         #
###############################################################################
if [ "${METRICML_ENABLED}" == "true" ]
then
  export METRICCOMPOSE="-f metricml-docker-compose.yml"
else
  METRICCOMPOSE=""
fi
if [ "${LOGMSGML_ENABLED}" == "true" ]
then
  export LOGCOMPOSE="-f logmsgml-docker-compose.yml"
else
  LOGCOMPOSE=""
fi
if [ "${ZAACOMMON_ENABLED}" == "true" ]
then
  export ZAACOMMONCOMPOSE="-f zaacommon-docker-compose.yml"
else
  ZAACOMMONCOMPOSE=""
fi
if [ "${ZDAP_ENABLED}" == "true" ]
then
  export ZDAPCOMPOSE="-f zdap-docker-compose.yml"
else
  ZDAPCOMPOSE=""
fi
if [ "${ZTOPO_ENABLED}" == "true" ]
then
  export ZTOPOCOMPOSE="-f zrdds-docker-compose.yml"
else
  ZTOPOCOMPOSE=""
fi
if [ "${SNOW_ENABLED}" == "true" ]
then
  export SNOWCOMPOSE="-f zoacommon-docker-compose-snow.yml"
else
  SNOWCOMPOSE=""
fi
