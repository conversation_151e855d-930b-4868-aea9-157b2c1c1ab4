#!/bin/bash
SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
export BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
cd ${BASEDIR}
ARCH="$( uname -m )"
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
NAMESPACE_COMMON=zoa
RED=$(tput setaf 1)
GREEN=$(tput setaf 2)
YELLOW=$(tput setaf 3)
BLUE=$(tput setaf 4)
ENDCOLOR=$(tput sgr0)

# START: Functions to be exported
sed() {
  if [ "$( uname -s )" == "Darwin" ]
  then
    which gsed > /dev/null 2>&1
    if [ $? == 0 ]
    then
      gsed "$@"
    else
      echo "WARNING: Running on MacOS, but no GNU sed found."
    fi
  else
    command sed "$@"
  fi
}
tar() {
  if [ "$( uname -s )" == "Darwin" ]
  then
    which gtar > /dev/null 2>&1
    if [ $? == 0 ]
    then
      gtar "$@"
    else
      echo "WARNING: Running on MacOS, but no GNU tar found."
    fi
  else
    command tar "$@"
  fi
}
zoacompose() {
  docker compose -f zoacore-docker-compose.yml -f zoacommon-docker-compose.yml ${LOGCOMPOSE} ${METRICCOMPOSE} ${ZAACOMMONCOMPOSE} ${ZDAPCOMPOSE} ${ZTOPOCOMPOSE} $@
}
zoabaseserial() {
  # Do two-step startup when all services are started at once to avoid timeouts / resource competition
  zoacompose up -d discovery
  zoacompose up -d gateway
  # Wait for gateway service to come up
  printf "%-50s %s" "Waiting for gateway service to come up" "..."
  COUNT=0
  while :
  do
    echo -n .
    ${OCI_AGENT} logs zoa-gateway | grep -q "Started PiGatewayApplication" 2>/dev/null
    if [ $? -eq 0 ]
    then
      echo -n " "
      echo "successful after ${COUNT} seconds."
      break
    elif [ ${COUNT} -ge 300 ]
    then
      echo -n " "
      echo "Gateway service still not ready after ${COUNT} seconds; giving up."
    else
      sleep 5
      COUNT=$(( ${COUNT} + 5 ))
    fi
  done
  zoacompose up -d auth
  # Check for authentication service
  printf "%-50s %s " "Waiting for authentication service to be ready" "..."
  zoasvccheck auth 8443
  # Check for datastore service
  zoacompose up -d datastore
  printf "%-50s %s " "Waiting for datastore service to be ready" "..."
  zoasvccheck datastore 9200
  zoacompose up -d kafkacontroller
  zoacompose up -d kafkabroker
  # Check for Kafka service
  printf "%-50s %s " "Waiting for Kafka broker service to be ready" "..."
  zoasvccheck kafkabroker 19092
  zoacompose up -d piserver
  # Check for PI server service
  printf "%-50s %s " "Waiting for Problem Insights service to be ready" "..."
  zoasvccheck piserver 9446
  zoagenrealm 
}
zoasvccheck() {
  HOST=${1}
  PORT=${2}
  if [ "${HOST}" == "auth" ]
  then
    SERVICE=${HOST}
  else
    SERVICE=gateway
  fi
  START=$( date +"%s" )
  ${OCI_AGENT} exec zoa-{SERVICE} bash -c "wait-for-it.sh --quiet --progress --strict --timeout=${SERVICE_TIMEOUT} ${HOST}:${PORT}"
  if [ $? -eq 0 ]
  then
    PORTCHECK=PASSED
  else
    PORTCHECK=FAILED
    echo "service still not ready after ${SERVICE_TIMEOUT} seconds; giving up."
  fi
  if [ "${HOST}" == "auth" ] && [ "${PORTCHECK}" == "PASSED" ]
  then
    APICHECK=$( check-kc-readiness )
    if [ "${APICHECK}" == "PASSED" ]
    then
      ALLCHECKS=PASSED
    else 
      echo "service response is invalid; giving up."
      ALLCHECKS=FAILED
    fi
  else
    ALLCHECKS=${PORTCHECK}
  fi
  if [ "${ALLCHECKS}" == "PASSED" ]
  then
    END=$( date +"%s" )
    echo "successful after $(( ${END} - ${START} )) seconds."
  else
    if [ "${NOEXIT}" == "NOEXIT" ]
    then
      return 1
    else
      exit 1
    fi
  fi
}
check-kc-readiness() { 
  ${OCI_AGENT} cp ${WORKDIR}/bin/utils/keycloak/checkKCreadiness.sh zoa-auth:/tmp/
  if [ "${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}x" == "truex" ]
  then
    KC_TEST_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN} 
    KC_TEST_ADMIN_PWD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD}
  else
    KC_TEST_ADMIN=${ZAIOPS_KEYCLOAK_ADMIN}
    KC_TEST_ADMIN_PWD=${ZAIOPS_KEYCLOAK_ADMIN_PASS}
  fi
  ${OCI_AGENT} exec zoa-auth bash -c "/tmp/checkKCreadiness.sh ${KC_TEST_ADMIN} ${KC_TEST_ADMIN_PWD} auth ${ZAIOPS_KC_CONTEXT_ROOT}"
}
zoagenrealm() {
  ${WORKDIR}/bin/utils/keycloak/updateKC.sh
  # On first run, updateKC.sh makes updates to .zoa_factory.config that are needed for the subsequent commands
  # Re-source .zoa_factory.config to pick up those changes
  set -a
  . ${WORKDIR}/.zoa_factory.config
  set +a
  if [ -f ${WORKDIR}/bin/utils/keycloak/configKC.sh ]
  then
    ${OCI_AGENT} stop zoa-auth && ${OCI_AGENT} rm zoa-auth
    ${WORKDIR}/bin/utils/keycloak/configKC.sh
    zoacompose up -d auth
    # Check for authentication service
    printf "%-50s %s " "Waiting for authentication service to be ready" "..."
    zoasvccheck auth 8443
  fi
  ${OCI_AGENT} cp ${WORKDIR}/bin/utils/keycloak/kc_postUp.sh zoa-auth:/realm
  if [ "${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}x" == "truex" ]
  then
    KC_TEST_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN}
    KC_TEST_ADMIN_PWD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD}
  else
    KC_TEST_ADMIN=${ZAIOPS_KEYCLOAK_ADMIN}
    KC_TEST_ADMIN_PWD=${ZAIOPS_KEYCLOAK_ADMIN_PASS}
  fi
  ${OCI_AGENT} exec zoa-auth bash -c "/realm/kc_postUp.sh ${KC_TEST_ADMIN} ${EXTERNAL_GATEWAY_HOST} ${ZAIOPS_KEYCLOAK_IP} ${IZOA_GATEWAY_PORT} ${ZAIOPS_KEYCLOAK_PORT} ${KC_TEST_ADMIN_PWD} ${ZAIOPS_ZOASVC_PASS} ${ZAIOPS_KC_CONTEXT_ROOT}"
  if [ -f ${WORKDIR}/bin/utils/keycloak/kc_postUp_zdap.sh ]
  then
    ${OCI_AGENT} cp ${WORKDIR}/bin/utils/keycloak/kc_postUp_zdap.sh zoa-auth:/realm
    ${OCI_AGENT} exec zoa-auth bash -c "/realm/kc_postUp_zdap.sh ${KC_TEST_ADMIN} ${KC_TEST_ADMIN_PWD} ${EXTERNAL_GATEWAY_HOST} ${ZAIOPS_KEYCLOAK_IP} ${IZOA_GATEWAY_PORT} ${ZAIOPS_KEYCLOAK_PORT} ${ZAIOPS_KC_CONTEXT_ROOT}"
  fi
}
zdapgetextensions() {
  . ${HOME}/.zoalocal
  mkdir -p ${WORKDIR}/extensions/datastore && rm -Rf ${WORKDIR}/extensions/datastore/*
  mkdir -p ${WORKDIR}/extensions/parser/pipeline && rm -Rf ${WORKDIR}/extensions/parser/pipeline/*
  wget --user=${AFUSER} --password=${AFTOKEN} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/zlda/components/v511-dev/develop/ZLDA-ELASTIC-5.1.1.dev.zip
  unzip ZLDA-ELASTIC-5.1.1.dev.zip -d ${WORKDIR}/extensions/datastore && rm -f ZLDA-ELASTIC-5.1.1.dev.zip
  wget --user=${AFUSER} --password=${AFTOKEN} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/zlda/components/v511-dev/develop/ZLDA-IngestionKit-curated-5.1.1.dev.zip
  wget --user=${AFUSER} --password=${AFTOKEN} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/zlda/components/v511-dev/develop/ZLDA-IngestionKit-raw-5.1.1.dev.zip
  unzip -j ZLDA-IngestionKit-curated-5.1.1.dev.zip -d ${WORKDIR}/extensions/parser/pipeline/curated_streams && rm -f ZLDA-IngestionKit-curated-5.1.1.dev.zip
  unzip -j ZLDA-IngestionKit-raw-5.1.1.dev.zip -d ${WORKDIR}/extensions/parser/pipeline/raw_streams && rm -f ZLDA-IngestionKit-raw-5.1.1.dev.zip
}
zdapload() {
  ${WORKDIR}/bin/utils/installOSextensions.sh dashboards import
  ${WORKDIR}/bin/utils/installLSextensions.sh
  ${OCI_AGENT} stop zdap-dashboards && ${OCI_AGENT} rm zdap-dashboards
  ${OCI_AGENT} stop zoa-datastore && ${OCI_AGENT} rm zoa-datastore
  zoacompose up -d datastore dashboards
}
zoadown() {
  for SERVICE in $( ${OCI_AGENT} ps -a | grep zoa- | awk '{ print $NF }' )
  do
    ${OCI_AGENT} stop ${SERVICE}
    ${OCI_AGENT} rm ${SERVICE}
  done
}
zdapdown() {
  for SERVICE in $( ${OCI_AGENT} ps -a | grep zdap- | awk '{ print $NF }' )
  do
    ${OCI_AGENT} stop ${SERVICE}
    ${OCI_AGENT} rm ${SERVICE}
  done
}
logmsgmldown() {
  for SERVICE in $( ${OCI_AGENT} ps -a | grep logmsgml- | awk '{ print $NF }' )
  do
    ${OCI_AGENT} stop ${SERVICE}
    ${OCI_AGENT} rm ${SERVICE}
  done
}
metricmldown() {
  for SERVICE in $( ${OCI_AGENT} ps -a | grep metricml- | awk '{ print $NF }' )
  do
    ${OCI_AGENT} stop ${SERVICE}
    ${OCI_AGENT} rm ${SERVICE}
  done
}
zaacommondown() {
  for SERVICE in $( ${OCI_AGENT} ps -a | grep zaa- | awk '{ print $NF }' )
  do
    ${OCI_AGENT} stop ${SERVICE}
    ${OCI_AGENT} rm ${SERVICE}
  done
}
zoagetlibraries() {
  echo ""
  if [ "${1}" == "DEBUG" ]
  then
    PICLI_DEBUG="-l FINEST"
  fi 
  if [ -z `zoacompose ps -q piserver` ] || [ -z `docker ps -q --no-trunc | grep $(zoacompose ps -q piserver)` ]
  then
    echo "PI server required but not running."
  else
    printf "%-50s %s " "Waiting for Problem Insights service to be ready" "..."
    waitforservice piserver 9446
    getadmincredentials
    echo "ADMINTMU=${ADMINTMU}" > /tmp/.tempvars
    echo "ADMINTMP=${ADMINTMP}" >> /tmp/.tempvars
    unset ADMINTMU ADMINTMP
    tar -C /tmp -cf - .tempvars | ${OCI_AGENT} cp - zoa-piserver:/tmp/
    rm -f /tmp/.tempvars
    ${OCI_AGENT} exec -u root zoa-piserver bash -c "chown 1000:1000 /tmp/.tempvars"
    ${OCI_AGENT} exec zoa-piserver bash -c "set -a ; . /tmp/.tempvars ; set +a ; /opt/ibm/piserver/bin/analysis.sh getLibraryList ${PICLI_DEBUG} ; rm -f /tmp/.tempvars"
  fi
  echo ""
}
zoadeletelibrary() {
  echo ""
  if [ "${1}" == "DEBUG" ]
  then
    PICLI_DEBUG="-l FINEST"  
  fi
  if [ -z `zoacompose ps -q piserver` ] || [ -z `docker ps -q --no-trunc | grep $(zoacompose ps -q piserver)` ]
  then
    echo "PI server required but not running."
  else
    printf "%-50s %s " "Waiting for Problem Insights service to be ready" "..."
    waitforservice piserver 9446
    echo -n "Specify the ID of the library to be deleted: "
    read -e LIB_ID
    echo -n "Specify the locale of the library to be deleted (default: en_US): "
    read -e LOCALE
    LOCALE=${LOCALE:-"en_US"}
    getadmincredentials
    echo "ADMINTMU=${ADMINTMU}" > /tmp/.tempvars
    echo "ADMINTMP=${ADMINTMP}" >> /tmp/.tempvars
    unset ADMINTMU ADMINTMP
    tar -C /tmp -cf - .tempvars | ${OCI_AGENT} cp - zoa-piserver:/tmp/
    rm -f /tmp/.tempvars
    ${OCI_AGENT} exec -u root zoa-piserver bash -c "chown 1000:1000 /tmp/.tempvars"
    ${OCI_AGENT} exec zoa-piserver bash -c "set -a ; . /tmp/.tempvars ; set +a  ; /opt/ibm/piserver/bin/analysis.sh deleteLibrary ${LIB_ID} ${LOCALE} -f ${PICLI_DEBUG} ; rm -f /tmp/.tempvars"
  fi
  echo ""
}
zoaimportlibrary() {
  echo ""
  if [ "${1}" == "DEBUG" ]
  then
    PICLI_DEBUG="-l FINEST"
  fi
  if [ -z `zoacompose ps -q piserver` ] || [ -z `docker ps -q --no-trunc | grep $(zoacompose ps -q piserver)` ]
  then
    echo "PI server required but not running."
  else
    printf "%-50s %s " "Waiting for Problem Insights service to be ready" "..."
    waitforservice piserver 9446
    echo -n "Specify the path to the message library file:  "
    read -e SOURCE
    if [ ! -f ${SOURCE} ]
    then
      echo "ERROR: ${SOURCE} is not a file or cannot be read."
      echo ""
      exit 1
    else
      getadmincredentials
      echo "ADMINTMU=${ADMINTMU}" > /tmp/.tempvars
      echo "ADMINTMP=${ADMINTMP}" >> /tmp/.tempvars
      unset ADMINTMU ADMINTMP
      tar -C /tmp -cf - .tempvars | ${OCI_AGENT} cp - zoa-piserver:/tmp/
      rm -f /tmp/.tempvars
      BASENAME=`basename ${SOURCE}`
      ${OCI_AGENT} exec zoa-piserver bash -c "mkdir -p /opt/ibm/staging/in"
      ${OCI_AGENT} cp ${SOURCE} zoa-piserver:/opt/ibm/staging/in
      ${OCI_AGENT} exec -u root zoa-piserver bash -c "chown 1000:1000 /tmp/.tempvars"
      ${OCI_AGENT} exec zoa-piserver bash -c "set -a ; . /tmp/.tempvars ; set +a  ; /opt/ibm/piserver/bin/analysis.sh importLibrary /opt/ibm/staging/in/${BASENAME} ${PICLI_DEBUG} ; rm -f /tmp/.tempvars"
    fi
  fi
  echo ""
}
zoaexportlibrary() {
  echo ""
  if [ "${1}" == "DEBUG" ]
  then    
    PICLI_DEBUG="-l FINEST"
  fi      
  if [ -z `zoacompose ps -q piserver` ] || [ -z `docker ps -q --no-trunc | grep $(zoacompose ps -q piserver)` ]
  then
    echo "PI server required but not running."
  else
    printf "%-50s %s " "Waiting for Problem Insights service to be ready" "..."
    waitforservice piserver 9446
    echo -n "Specify the ID of the library to be exported: "
    read -e LIB_ID
    echo -n "Specify the locale of the library to be exported (default: en_US): "
    read -e LOCALE
    LOCALE=${LOCALE:-"en_US"}
    echo -n "Specify the absolute path of the output file: "
    read -e TARGET
    DIRNAME=`dirname ${TARGET}`
    mkdir -p ${DIRNAME}
    if [ -w ${DIRNAME} ]
    then
      getadmincredentials
      echo "ADMINTMU=${ADMINTMU}" > /tmp/.tempvars
      echo "ADMINTMP=${ADMINTMP}" >> /tmp/.tempvars
      unset ADMINTMU ADMINTMP
      tar -C /tmp -cf - .tempvars | ${OCI_AGENT} cp - zoa-piserver:/tmp/
      rm -f /tmp/.tempvars
      ${OCI_AGENT} exec -u root zoa-piserver bash -c "chown 1000:1000 /tmp/.tempvars"
      ${OCI_AGENT}  exec zoa-piserver bash -c "set -a ; . /tmp/.tempvars ; set +a ; mkdir -p /opt/ibm/staging/out ; /opt/ibm/piserver/bin/analysis.sh exportLibrary ${LIB_ID} ${LOCALE} /opt/ibm/staging/out/${LIB_ID}.xml ${PICLI_DEBUG} ; rm -f /tmp/.tempvars"
      ${OCI_AGENT} cp zoa-piserver:/opt/ibm/staging/out/${LIB_ID}.xml ${TARGET}
      ${OCI_AGENT} exec zoa-piserver bash -c "rm -f /opt/ibm/staging/out/${LIB_ID}.xml"
    else
      echo "ERROR: Directory ${DIRNAME} is not writable."
      echo ""
      exit 1
    fi
  fi
  echo ""
}
zoaloadip() {
  echo ""
  if [ -z `zoacompose ps -q piserver` ] || [ -z `docker ps -q --no-trunc | grep $(zoacompose ps -q piserver)` ]
  then
    echo "PI server required but not running."
  else
    printf "%-50s %s " "Waiting for Problem Insights service to be ready" "..."
    waitforservice piserver 9446
    echo -n "Specify the path to the insight pack ZIP file:  "
    read -e SOURCE
    if [ ! -f ${SOURCE} ]
    then
      echo "ERROR: ${SOURCE} is not a file or cannot be read."
      exit 1
    elif [ $( echo ${SOURCE} | awk -F"." '{ print $NF }' ) != "zip" ]
    then
      echo "ERROR: ${SOURCE} does not appear to be an insight pack ZIP file."
      exit 1
    else
      ${OCI_AGENT} cp ${SOURCE} zoa-piserver:/opt/ibm/piserver/wlp/usr/servers/piFrameworkServer/insightpacks
      ${OCI_AGENT} exec -u root zoa-piserver bash -c "chown 1000:1000 /opt/ibm/piserver/wlp/usr/servers/piFrameworkServer/insightpacks/*.zip"
      zoaupdateips NIA
    fi
  fi
  echo ""
}
reloadips() {
  if [ "${1}" == "DEBUG" ]
  then 
    PICLI_DEBUG="-l FINEST"
  fi
  getadmincredentials
  echo "ADMINTMU=${ADMINTMU}" > /tmp/.tempvars
  echo "ADMINTMP=${ADMINTMP}" >> /tmp/.tempvars
  unset ADMINTMU ADMINTMP
  tar -C /tmp -cf - .tempvars | ${OCI_AGENT} cp - zoa-piserver:/tmp/
  rm -f /tmp/.tempvars
  ${OCI_AGENT} exec -u root zoa-piserver bash -c "chown 1000:1000 /tmp/.tempvars"
  # Get list of installed insight packs
  FULL_OUTPUT=`${OCI_AGENT} exec zoa-piserver bash -c "set -a ; . /tmp/.tempvars ; set +a ; /opt/ibm/piserver/bin/analysis.sh getInstalledPacks ${PICLI_DEBUG}" 2>/dev/null`
  echo "${FULL_OUTPUT}" | grep -q "Command successful"
  if [ $? -eq 0 ]
  then
    echo "INFO: Successfully obtained list of installed insight packs."
  else
    echo "ERROR: The following problem occurred while obtaining the list of installed insight packs:"
    echo "${FULL_OUTPUT}" | tail -2
    exit ${ERR} 
  fi
  OUTPUT=`echo "${FULL_OUTPUT}" | grep '::'`
  TO_INSTALL=`echo "${OUTPUT}" | awk -F '::' '{ print $1 }'`
  TO_REMOVE=`echo "${OUTPUT}" | awk -F '::' '{ print $2 }'`
  TO_ENABLE=`echo "${OUTPUT}" | grep enabled | awk -F '::' '{ print $2 }'`
  # Uninstall insight packs
  echo "Insight Pack uninstallation:"
  for PACK in `echo ${TO_REMOVE}`
  do
    RESULT=`${OCI_AGENT} exec zoa-piserver bash -c "set -a ; . /tmp/.tempvars ; set +a ; /opt/ibm/piserver/bin/analysis.sh uninstallInsightPack ${PACK} ${PICLI_DEBUG}" 2>/dev/null`
    checkipresult "${RESULT}" ${PACK}
  done
  # Re-install previously installed insight packs
  echo "Insight Pack reinstallation:"
  for PACK in `echo ${TO_INSTALL}`
  do
    RESULT=`${OCI_AGENT} exec zoa-piserver bash -c "set -a ; . /tmp/.tempvars ; set +a ; /opt/ibm/piserver/bin/analysis.sh installInsightPack ${PACK} ${PICLI_DEBUG}" 2>/dev/null`
    checkipresult "${RESULT}" ${PACK}
  done
  # Re-enable previously enabled insight packs
  echo "Insight Pack reenablement:"
  for PACK in `echo ${TO_ENABLE}`
  do
    RESULT=`${OCI_AGENT} exec zoa-piserver bash -c "set -a ; . /tmp/.tempvars ; set +a ; /opt/ibm/piserver/bin/analysis.sh enableInsightPack ${PACK} ${PICLI_DEBUG}" 2>/dev/null`
    checkipresult "${RESULT}" ${PACK}
  done
  ${OCI_AGENT} exec zoa-piserver bash -c "rm -f /tmp/.tempvars"
}
checkipresult() {
  RESULT="$1"
  OBJECT=$2
  echo "${RESULT}" | grep -q "Command successful"
  if [ $? -eq 0 ]
  then
    echo "  INFO: Object '"${PACK}"' was processed successfully."
  else
    echo "  ERROR: A problem occurred while processing object '"${PACK}"'."
    echo "         See the Problem Insights service log for details."
  fi
}
zoaupdateips() {
  echo ""
  NONINTERACTIVE=${1}
  if [ -z `zoacompose ps -q piserver` ] || [ -z `docker ps -q --no-trunc | grep $(zoacompose ps -q piserver)` ]
  then
    echo "PI server required but not running."
  else
    printf "%-50s %s " "Waiting for Problem Insights service to be ready" "..."
    waitforservice piserver 9446
    ${OCI_AGENT} cp ${WORKDIR}/bin/utils/piserver/customizeIPs.sh zoa-piserver:/opt/ibm/staging
    ${OCI_AGENT} exec zoa-piserver bash -c "/opt/ibm/staging/customizeIPs.sh ${EXTERNAL_GATEWAY_HOST} ${IZOA_GATEWAY_PORT}"
    if [ "${NONINTERACTIVE}x" == "x" ]
    then
      reloadips
    fi
  fi
  echo ""
}
waitforservice() {
  HOST=${1}
  PORT=${2}
  START=`date +"%s"`
  ${OCI_AGENT} exec zoa-gateway bash -c "wait-for-it.sh --quiet --progress --strict --timeout=${SERVICE_TIMEOUT} ${HOST}:${PORT}"
  if [ $? -eq 0 ]
  then
    END=`date +"%s"`
    echo "successful after $(( ${END} - ${START} )) seconds."
  else 
    echo "service still not ready after ${SERVICE_TIMEOUT} seconds; giving up."
    exit 1 
  fi
}
getadmincredentials() {
  if [ "${ADMINTMU}x" = "x" ]
  then
    echo ""
    # Get administrative user ID
    echo -n "Provide an administrative user ID for the Problem Insights server (default: piadmin):  "
    read -e ADMINTMU
    ADMINTMU=${ADMINTMU:-"piadmin"}
  fi
  if [ "${ADMINTMP}x" = "x"  ]
  then
    echo ""
    # Get administrative password
    echo "Provide the password for ${ADMINTMU}:"
    stty -echo
    while [[ -z "${ADMINTMP}" ]]; do
      read -e ADMINTMP
    done
    stty echo
  fi    
  echo ""
  echo ""
}
zoahelp() {
  printf "Available convenience commands:\n"
  printf "  - zoahelp                    this command listing\n"
  printf "  - zoacompose                 shorthand for 'docker compose -f <compose_file_1> -f <compose_file_2> ...'\n"
  printf "  - zoabaseserial              bring up ZOA base services one at a time to avoid contention during startup\n"
  printf "  - zoagenrealm                create IzoaKeycloak realm after starting auth (Keycloak) service\n"
  printf "  - zoagetlibraries            get list of message libraries installed in the PI server\n"
  printf "  - zoadeletelibrary           delete a single message library from the PI server\n"
  printf "  - zoaexportlibrary           export a single message library from the PI server\n"
  printf "  - zoaimportlibrary           import a single new message library into the PI server\n"
  printf "  - zoaloadip                  load a single insight pack into the PI server and activate it\n"
  printf "  - zoaupdateips               update all insight packs installed in the PI server after IP address or port change\n"
  printf "  - zdapgetextensions          download parser, datastore and dashboards extensions for ZDAP and unpack them into <WORKDIR>/extensions\n"
  printf "  - zdapload                   load dashboards, visualizations and searches as well as parser configuration into ZDAP\n"
  printf "  - zoadown                    stop and remove all ZOACOMMON containers\n"
  printf "  - logmsgmldown               stop and remove all LogMsgML containers\n"
  printf "  - metricmldown               stop and remove all MetricML containers\n"
  printf "  - zaacommondown              stop and remove all ZAACOMMON containers\n"
  printf "  - zdapdown                   stop and remove all ZDAP containers\n\n"
}
# END: Functions to be exported

help() {
  echo ""
  echo "Usage: "
  echo "  $0 prepare                 create working directory (makes it easier to exclude temporary artifacts from git control)"
  echo "  $0 gen-tls                 generate TLS artifacts and update zoa_env.config"
  echo "  $0 enable-metricml         copy artifacts from IZOA/metric-ml-docker-MF into working directory"
  echo "  $0 enable-logmsgml         copy artifacts from IZOA/izoa-zaware-docker-MF into working directory"
  echo "  $0 enable-zaacommon        copy artifacts from IZOA/zaa-common-docker-MF into working directory"
  echo "  $0 enable-zdap             copy artifacts from ZLDA/zlda-zdap-docker into working directory"
  echo "  $0 oci-shell               open a bash shell in thte working directory with required environment settings"
  echo "  $0 clean                   remove working directory"
  echo ""
}

enableFeature() {
  FEATURE=${1}
  if [ "${FEATURE}" == "METRICML" ]
  then
    DCSOURCE=${METRICML_SOURCE}
    DCFILE=metricml-docker-compose.yml
  elif [ "${FEATURE}" == "LOGMSGML" ]
  then
    DCSOURCE=${LOGMSGML_SOURCE}
    DCFILE=logmsgml-docker-compose.yml
  elif [ "${FEATURE}" == "ZAACOMMON" ]
  then
    DCSOURCE=${ZAACOMMON_SOURCE}
    DCFILE=zaacommon-docker-compose.yml
  elif [ "${FEATURE}" == "ZDAP" ]
  then
    DCSOURCE=${ZLDA_SOURCE}/zlda-zdap-docker
    DCFILE=zdap-docker-compose.yml
  fi
  if [ ! -f ${DCSOURCE}/${DCFILE} ]
  then
    printf "${RED}ERROR: Docker Compose file '${DCFILE}' not found in directory '${DCSOURCE}'.\n"
    printf "        Unable to proceed.${ENDCOLOR}\n\n"
    exit 1
  else
    cp ${DCSOURCE}/${DCFILE} ${WORKDIR}
    sed -i -e "s%-x86_64$%-${ARCH}%g" ${WORKDIR}/${DCFILE}
    cat ${DCSOURCE}/zoa_env.config >> ${WORKDIR}/zoa_env.config
    cat ${DCSOURCE}/.zoa_factory.config >> ${WORKDIR}/.zoa_factory.config
    sed -i -e "s%^${FEATURE}_ENABLED=.*$%${FEATURE}_ENABLED=true%g" ${HOME}/.zoalocal
    . ${HOME}/.zoalocal
    if [ "${FEATURE}" == "ZDAP" ]
    then
      shift 1
      cp ${DCSOURCE}/tools/install*.sh ${WORKDIR}/bin/utils
      cp ${DCSOURCE}/keycloak/tools/*.sh ${WORKDIR}/bin/utils/keycloak
      sed -i -e "s%ibm-zaiops%icr.io/zoa-oci%g" ${WORKDIR}/bin/utils/installLSextensions.sh
      sed -i -e "s%ibm-zaiops%icr.io/zoa-oci%g" ${WORKDIR}/bin/utils/installOSextensions.sh
      sed -i -e "s%ibm-zaiops%icr.io/zoa-oci%g" ${WORKDIR}/bin/utils/keycloak/configKC.sh
      # Create ZDAP client secret
      SEG1=$( openssl rand -hex 32 | tr -dc a-f0-9 | head -c${1:-8};echo; )
      SEG2=$( openssl rand -hex 32 | tr -dc a-f0-9 | head -c${1:-4};echo; )
      SEG3=$( openssl rand -hex 32 | tr -dc a-f0-9 | head -c${1:-4};echo; )
      SEG4=$( openssl rand -hex 32 | tr -dc a-f0-9 | head -c${1:-4};echo; )
      SEG5=$( openssl rand -hex 32 | tr -dc a-f0-9 | head -c${1:-12};echo; )
      ZDAP_CLIENT_SECRET=$( echo "${SEG1}-${SEG2}-${SEG3}-${SEG4}-${SEG5}" | base64 )
      sed -i -e "s%^ZDAP_CLIENT_SECRET=.*$%ZDAP_CLIENT_SECRET=${ZDAP_CLIENT_SECRET}%g" ${WORKDIR}/.zoa_factory.config
    fi
    echo ""
  fi
}

shellInfo() {
  printf "${BLUE}Entering bash shell for OCI operations.\n"
  printf "You can reference the directory from which you started by using the \${BASEDIR} variable.\n"
  printf "To return to the originating shell, run the 'exit' command or press Ctrl-D.\n\n"
  zoahelp
  printf "${YELLOW}Known issues:\n"
  printf "  - Lack of available memory may cause containers to fail, to loop, or otherwise to behave unexpectedly.\n"
  printf "    If you run your containers in a VM, make sure the VM has sufficient memory and CPUs assigned.\n"
  printf "    If you run your containers 'natively', make sure your system has sufficient memory available.\n"
  printf "${ENDCOLOR}\n"
}

cleanup() {
  echo "Removing ${WORKDIR}..."
  rm -Rf ${WORKDIR}
  echo ""
}

checkWorkDir() {
  if [ ! -d ${WORKDIR} ]
  then
    printf "${RED}ERROR: Cannot find working directory ('${WORKDIR}').\n"
    printf "       Unable to proceed.${ENDCOLOR}\n\n"
    exit 1
  fi
}

checkConfigFile() {
  if [ ! -f ${HOME}/.zoalocal ] && [ ! -h ${HOME}/.zoalocal ]
  then
    printf "${RED}ERROR: No '\${HOME}/.zoalocal' configuration file found. Unable to proceed.\n"
    printf "       Create a '\${HOME}/.zoalocal' configuration file based on the sample in\n"
    printf "       '${SCRIPTDIR}/samples/.zoalocal'\n"
    printf "       and try again.${ENDCOLOR}\n\n"
    exit 1
  else
    printf "${GREEN}INFO: Found '\${HOME}/.zoalocal' configuration file. Make sure that it is up to date and correct.${ENDCOLOR}\n"
    . ${HOME}/.zoalocal
  fi
}

sourceZoaConfig() {
  cd ${WORKDIR}
  echo ""
  set -a
  . ${WORKDIR}/${NEW_CONFIG}
  . ${WORKDIR}/${IBM_CONFIG}
  set +a
  if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
  then
    export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
  fi
  DAC=${DISCOVERY_AGENT_COUNT:-0}
  GRM=${ZAIOPS_GATEWAY_REQUESTS_PER_MIN:-80}
  export ZAIOPS_TOTAL_RATE_LIMIT=$( awk -v dac=${DAC} -v grm=${GRM} 'BEGIN { rounded = sprintf("%.0f", (dac+1)*grm*125/100); print rounded }' )
}

makeWorkDir() {
  mkdir -p ${WORKDIR}/bin/utils/datastore
  mkdir -p ${WORKDIR}/bin/utils/keycloak
  mkdir -p ${WORKDIR}/bin/utils/kafka
  mkdir -p ${WORKDIR}/bin/utils/piserver
  cat ${BASEDIR}/zoa_env.config.* > ${WORKDIR}/zoa_env.config
  cat ${BASEDIR}/.zoa_factory.config.* > ${WORKDIR}/.zoa_factory.config
  cp ${BASEDIR}/*docker-compose*.yml ${WORKDIR}/
  cp ${BASEDIR}/tools/common_functions.sh ${WORKDIR}/bin/utils/
  for FILE in $( find ${BASEDIR}/tools -type f -iname "*kc*" | grep -v k8s )
  do
    cp ${FILE} ${WORKDIR}/bin/utils/keycloak/
  done
  cp ${BASEDIR}/tools/zoa_create_perm_admin.sh ${WORKDIR}/bin/utils/keycloak/
  cp ${BASEDIR}/tools/customizeIPs.sh ${WORKDIR}/bin/utils/piserver/
  cp ${BASEDIR}/tools/kafkaPrune.sh ${WORKDIR}/bin/utils/kafka/
  sed -i -e "s%ibm-zaiops%icr.io/zoa-oci%g" ${WORKDIR}/bin/utils/keycloak/updateKC.sh
  cp ${BASEDIR}/tools/kc_postUp.sh ${WORKDIR}/bin/utils/keycloak
  sed -i -e "s%^OCI_AGENT=.*$%OCI_AGENT=${OCI_AGENT}%g" ${WORKDIR}/.zoa_factory.config
  sed -i -e "s%^OCI_VERSION=.*$%OCI_VERSION=${OCI_VERSION}%g" ${WORKDIR}/.zoa_factory.config
  for YML in $( ls -1 ${WORKDIR}/*.yml )
  do
    sed -i -e "s%-x86_64$%-${ARCH}%g" ${YML}
  done
  #${OCI_AGENT} volume create ibmzaiops_zaiops_keycloak
  ${OCI_AGENT} volume create zaiops_shared
  for FEATURE in LOGMSGML METRICML ZAACOMMON ZDAP
  do
    eval VALUE=\"\$${FEATURE}_ENABLED\"
    if [ "${VALUE}" == "true" ]
    then
      enableFeature ${FEATURE}
    fi
  done
}

dockerLogin() {
  . ${HOME}/.zoalocal
  ibmcloud login -r us-east
  ibmcloud cr login --client docker
}

getHostAndIp() {
  # Determine hostname of the system
  THISHOST=$( hostname -f )
  THISHOST_LOWER=$( hostname -f | tr [:upper:] [:lower:] )
  # MacOS use case
  if [ "$( uname -s )" == "Darwin" ]
  then
    THISIP=$( ipconfig getifaddr en0 )
  # Windows with WSL use case
  elif [ "$( uname -s )" == "Linux" ]
  then
    IPARRAY="$( host ${THISHOST_LOWER} | grep -vi IPv6 | awk '{ print $NF }' | grep -vE '(.*[a-zA-Z].*)' )"
    # Fallback network is 192.168.x
    NETPREFIX=192
    if echo "${IPARRAY}" | grep -q ^17
    then
      # Preferred network is 17x.x
      NETPREFIX=17
    else
      # Second-best is 9.x
      if echo "${IPARRAY}" | grep -q ^9
      then
        NETPREFIX=9
      fi
    fi
    for IP in $( echo "$IPARRAY" )
    do
      # Pick the IP address on the 17x network, which should be the WSL network
      if echo ${IP} | grep -q ^${NETPREFIX}
      then
        echo "Using IP address ${IP}."
        THISIP=${IP}
      else 
        echo "Discarding IP address ${IP}."
      fi
    done
    if [ "${THISIP}x" == "x" ]
    then
      printf "${RED}ERROR: Unable to determine suitable IP address.\n"
      printf "        Cannot proceed.${ENDCOLOR}\n\n"
      exit 1
    fi
  else
    echo "$(uname -s) not supported."
    exit 1
  fi
  sed -i -e "s%^ZAIOPS_KEYCLOAK_HOST=.*$%ZAIOPS_KEYCLOAK_HOST=${THISHOST_LOWER}%g" ${WORKDIR}/.zoa_factory.config
  sed -i -e "s%^EXTERNAL_GATEWAY_HOST=.*$%EXTERNAL_GATEWAY_HOST=${THISHOST_LOWER}%g" ${WORKDIR}/.zoa_factory.config
  sed -i -e "s%^ZAIOPS_KEYCLOAK_IP=.*$%ZAIOPS_KEYCLOAK_IP=${THISIP}%g" ${WORKDIR}/.zoa_factory.config
}

startUtil() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} run -d -t -u ${ZOA_UID}:0 --rm --name zaiops-util-${UTILTSTAMP} --entrypoint /bin/bash -v zaiops_shared:/shared:rw icr.io/zoa-oci/zoa-service-discovery:${TAG}-${ARCH} > /dev/null
}

stopUtil() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} stop zaiops-util-${UTILTSTAMP} > /dev/null
}

dumpTLSArtifacts() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} cp zaiops-util-${UTILTSTAMP}:/shared/config/zoasvc.tls ${WORKDIR} > /dev/null
}

tmpUnpackCerts() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "if [ -f /shared/config/zoasvc.tls ] ; then mkdir -p /shared/tls_tmp && rm -f /shared/tls_tmp/* ; cd /shared/tls_tmp ; cat /shared/config/zoasvc.tls | base64 -d | tar xz ; else echo \"${ERRORMSG}TLS artifacts not found.\" ; exit 1 ; fi"
}

tmpUnpackCertsNoError() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "if [ -f /shared/config/zoasvc.tls ] ; then mkdir -p /shared/tls_tmp && rm -f /shared/tls_tmp/* ; cd /shared/tls_tmp ; cat /shared/config/zoasvc.tls | base64 -d | tar xz ; fi"
}

tmpRepackCerts() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "if [ -f /shared/tls_tmp/zoasvc.ts ] && [ -s /shared/tls_tmp/zoasvc.ts ] ; then cd /shared/tls_tmp ; rm -f /shared/config/zoasvc.tls ; tar cz --exclude *.sh *.* | base64 > /shared/config/zoasvc.tls ; chmod 644 /shared/config/zoasvc.tls ; cd /tmp ; rm -Rf /shared/tls_tmp ; else echo \"${ERRORMSG}Truststore zoasvc.ts missing or empty\" ; exit 1 ; fi"
}

genTlsPwd() {
  # Ensure that keystore password starts with an alphabetic character
  ZOASVC_PASS=`openssl rand -base64 12 | tr -dc [a-zA-Z]`
  ZOASVC_PASS_B=$( echo "${ZOASVC_PASS}" | base64 )
}

writeTlsPwd() {
  sed -i -e "s%^ZAIOPS_ZOASVC_PASS=.*$%ZAIOPS_ZOASVC_PASS=${ZOASVC_PASS_B}%g" ${WORKDIR}/${NEW_CONFIG}
}

createZoaSecCert() {
  ORIGIN=$( pwd )
  RUNTIME=$( date +"%Y%m%d%H%M" )
  . ${WORKDIR}/zoa_env.config
  ZOASVC_PASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  echo "Generating new security administration artifacts..."
  mkdir -p ${WORKDIR}/tmp_${RUNTIME}/tls_tmp && cd ${WORKDIR}/tmp_${RUNTIME}/tls_tmp
  (
  cat <<- END
#!/bin/bash
OSSL_LEGACY=""
OSSL_VERSION=\$( openssl version 2>/dev/null | awk '{ print \$2 }' | tr -d ".a-zA-Z-" )
if [ \${OSSL_VERSION} -gt 200 ]
then  
  OSSL_LEGACY="-legacy"
fi  
echo ${ZOASVC_PASS} > .pw
echo ${ZOASVC_PASS} >> .pw
# Private key for root CA
openssl genpkey -out internalCA.key -algorithm EC \
    -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
    -pass file:.pw
# Certificate for root CA
openssl req -x509 -sha256 -days 3650 -key internalCA.key \
    -out internalCA.crt -subj "/CN=${THISHOST_LOWER}" \
    -passout file:.pw -passin file:.pw
# Private key for internal datastore security
openssl genpkey -out datastore.key -algorithm EC \
    -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
    -pass file:.pw
# Certificate signing request for datastore
openssl req -new -key datastore.key -subj "/CN=${THISHOST_LOWER}" \
  -passout file:.pw -passin file:.pw \
  -out datastore.csr
# Create extensions file
echo "authorityKeyIdentifier=keyid,issuer" > datastore.ext
echo "basicConstraints=CA:FALSE" >> datastore.ext
echo "subjectAltName = @alt_names" >> datastore.ext
echo "[alt_names]" >> datastore.ext
echo "DNS.1 = ${THISHOST_LOWER}" >> datastore.ext
echo "DNS.2 = localhost" >> datastore.ext
echo "DNS.3 = datastore" >> datastore.ext
echo "DNS.4 = dashboards" >> datastore.ext
echo "DNS.5 = host.docker.internal" >> datastore.ext
echo "IP.1 = ${THISIP}" >> datastore.ext
echo "IP.2 = 127.0.0.1" >> datastore.ext
# Sign datastore certificate
openssl x509 -req -CA internalCA.crt -CAkey internalCA.key \
  -in datastore.csr -out datastore.crt -days 3650 \
  -CAcreateserial -extfile datastore.ext \
  -passin file:.pw
# Import everything into a keystore
openssl pkcs12 -export \${OSSL_LEGACY} -out datastore.ks -name datastore \
  -inkey datastore.key -in datastore.crt \
  -passin file:.pw -passout file:.pw
# Truststore for client
# Remove any old certificates
keytool -delete -alias datastore -keystore datastore.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias zoa-services -keystore datastore.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias internal-root -keystore datastore.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-root -keystore datastore.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
# Import fresh certificates
keytool -import -alias datastore -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore datastore.ts -trustcacerts \
  -file datastore.crt
# Datastore truststore also needs zoasvc.crt
keytool -import -alias zoa-services -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore datastore.ts -trustcacerts \
  -file zoasvc.crt
# ...and the rootCA certificate
keytool -import -alias ca-root -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore datastore.ts -trustcacerts \
  -file rootCA.crt
# ...and the internalCA certificate
keytool -import -alias internal-root -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore datastore.ts -trustcacerts \
  -file internalCA.crt
# Private key for security admin
openssl genpkey -out secadmin.key.temp -algorithm EC \
  -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
  -pass file:.pw
# Certificate signing request for security admin
openssl req -new -key secadmin.key.temp \
  -subj "/L=internal/O=zoa/OU=zoa/CN=secadmin" \
  -passout file:.pw -passin file:.pw \
  -out secadmin.csr
# Sign certificate for security admin
openssl x509 -req -CA internalCA.crt -CAkey internalCA.key \
  -in secadmin.csr -out secadmin.crt -days 3650 \
  -CAcreateserial -passin file:.pw
# Convert security admin key to PKCS8 format
openssl pkcs8 -inform PEM -outform PEM \
  -in secadmin.key.temp -topk8 \
  -v1 PBE-SHA1-3DES -out secadmin.key \
  -passout file:.pw -passin file:.pw
# Remove temporary (non-PKCS8) key
rm -f secadmin.key.temp
# Remove password file
rm -f .pw
END
  ) > secproc.sh
  cd ${WORKDIR}/tmp_${RUNTIME}/
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tar cf sec_config.tar tls_tmp && ${OCI_AGENT} cp sec_config.tar zaiops-util-${UTILTSTAMP}:/shared/
  tmpUnpackCerts ${UTILTSTAMP}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "tar -C /shared -xf /shared/sec_config.tar ; chmod 755 /shared/tls_tmp/*.sh ; rm -f /shared/sec_config.tar ; cd /shared/tls_tmp ; ./secproc.sh"
  tmpRepackCerts ${UTILTSTAMP}
  dumpTLSArtifacts ${UTILTSTAMP}
  stopUtil ${UTILTSTAMP}
  # Clean up
  cd ${ORIGIN} && rm -Rf ${WORKDIR}/tmp_${RUNTIME}
}

createZoaSvcCsr() {
  . ${WORKDIR}/zoa_env.config
  ZOASVC_PASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  CHECKIP=${1}
  THISHOST_LOWER=${2}
  ORIGIN=$( pwd )
  RUNTIME=$( date +"%Y%m%d%H%M" )
  if [ "${CHECKIP}" == "true" ]
  then
    getHostAndIp
  fi
  # Check whether keystore already exists. If it doesn't, bail since we don't have the basics to create a CSR from
  if [ ! -f ${WORKDIR}/zoasvc.tls ]
  then
    logToStdout "${ERROMSG}No private key found to generate signing request with. Unable to proceed."
    exit 1
  else
    mkdir -p ${WORKDIR}/tmp_${RUNTIME}/tls_tmp && cd ${WORKDIR}/tmp_${RUNTIME}/tls_tmp
    (
    cat <<- END
#!/bin/bash
echo ${ZOASVC_PASS} > .pw
echo ${ZOASVC_PASS} >> .pw
# Create CSR configuration file
echo "[req]" > zoasvc.conf
echo "distinguished_name = req_distinguished_name" >> zoasvc.conf
echo "prompt = no" >> zoasvc.conf
echo "" >> zoasvc.conf
echo "[req_distinguished_name]" >> zoasvc.conf
if [ "${COUNTRY}x" != "x" ]
then
  echo "C = ${COUNTRY}" >> zoasvc.conf
fi
if [ "${STATE}x" != "x" ]
then
  echo "ST = ${STATE}" >> zoasvc.conf
fi
if [ "${LOCATION}x" != "x" ]
then
  echo "L = ${LOCATION}" >> zoasvc.conf
fi
if [ "${ORG_NAME}x" != "x" ]
then
  echo "O = ${ORG_NAME}" >> zoasvc.conf
fi
if [ "${ORG_UNIT}x" != "x" ]
then
  echo "OU = ${ORG_UNIT}" >> zoasvc.conf
fi
echo "CN = ${THISHOST_LOWER}" >> zoasvc.conf
# Create certificate signing request
rm -f zoasvc.csr
openssl req -new -key zoasvc.key -config zoasvc.conf \
  -passout file:.pw -passin file:.pw \
  -out zoasvc.csr
# Remove password file
rm -f .pw
mkdir -p /shared/config
tar cz --exclude *.sh *.* | base64 > /shared/config/zoasvc.tls
chmod 644 /shared/config/zoasvc.tls
END
    ) > csrproc.sh
    cd ${WORKDIR}/tmp_${RUNTIME}/
    UTILTSTAMP=$( date +"%s" )
    startUtil ${UTILTSTAMP}
    tmpUnpackCertsNoError ${UTILTSTAMP}
    tar cf csr_config.tar tls_tmp && ${OCI_AGENT} cp csr_config.tar zaiops-util-${UTILTSTAMP}:/shared/
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "rm -f /shared/config/zoasvc.tls ; tar -C /shared -xf /shared/csr_config.tar ; chmod 755 /shared/tls_tmp/*.sh ; rm -f /shared/csr_config.tar"
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "cd /shared/tls_tmp ; ./csrproc.sh ; cp ./zoasvc.csr /tmp/ ; cd /shared ; rm -Rf tls_tmp"
    dumpTLSArtifacts ${UTILTSTAMP}
    ${OCI_AGENT} cp zaiops-util-${UTILTSTAMP}:/tmp/zoasvc.csr ${WORKDIR} > /dev/null && logToStdout "${INFOMSG}Certificate signing request available at ${WORKDIR}/zoasvc.csr"
    stopUtil ${UTILTSTAMP}
    # Clean up
    cd ${ORIGIN} && rm -Rf ${WORKDIR}/tmp_${RUNTIME}
    echo ""
  fi
}

createZoaSvcCert() { 
  CMDOPT=${1}
  CHECKIP=true
  shift $#
  ORIGIN=$( pwd )
  RUNTIME=$( date +"%Y%m%d%H%M" )
  shift 2
  if [ "${CHECKIP}" == "true" ]
  then
    getHostAndIp
  fi
  genTlsPwd
  # Check whether keystore already exists. If true, use it.
  if [ ! -f ${WORKDIR}/zoasvc.tls ] || [ "${CMDOPT}" == "FORCEGEN" ]
  then
    echo "Generating new certificates..."
    mkdir -p ${WORKDIR}/tmp_${RUNTIME}/tls_tmp && cd ${WORKDIR}/tmp_${RUNTIME}/tls_tmp
    (
    cat <<- END
#!/bin/bash
CRYPT_ALGO=${CRYPT_ALGO:-EC}
OSSL_LEGACY=""
OSSL_VERSION=\$( openssl version 2>/dev/null | awk '{ print \$2 }' | tr -d ".a-zA-Z-" )
if [ \${OSSL_VERSION} -gt 200 ]
then  
  OSSL_LEGACY="-legacy"
fi  
echo ${ZOASVC_PASS} > .pw
echo ${ZOASVC_PASS} >> .pw
# Private key for root CA
rm -f rootCA.key
openssl genpkey -out rootCA.key -algorithm EC \
    -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
    -pass file:.pw
# Certificate for root CA
rm -f rootCA.crt
openssl req -x509 -sha256 -days 3650 -key rootCA.key \
    -out rootCA.crt -subj "/CN=${THISHOST_LOWER}" \
    -passout file:.pw -passin file:.pw
# Private key for application
rm -f zoasvc.key
if [ "\${CRYPT_ALGO}" == "RSA" ]
then
  openssl genpkey -out zoasvc.key -algorithm RSA \
      -aes256 -pass file:.pw
else
  if [ "\${CRYPT_ALGO}" != "EC" ]
  then
    echo "Invalid cryptographic algorithm '\${CRYPT_ALGO}' specified. Using Elliptic Curve instead."
  fi
  openssl genpkey -out zoasvc.key -algorithm EC \
      -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
      -pass file:.pw
fi
# Create CSR configuration file
echo "[req]" > zoasvc.conf
echo "distinguished_name = req_distinguished_name" >> zoasvc.conf
echo "prompt = no" >> zoasvc.conf
echo "" >> zoasvc.conf
echo "[req_distinguished_name]" >> zoasvc.conf
if [ "${COUNTRY}x" != "x" ]
then
  echo "C = ${COUNTRY}" >> zoasvc.conf
fi
if [ "${STATE}x" != "x" ]
then
  echo "ST = ${STATE}" >> zoasvc.conf
fi
if [ "${LOCATION}x" != "x" ]
then
  echo "L = ${LOCATION}" >> zoasvc.conf
fi
if [ "${ORG_NAME}x" != "x" ]
then
  echo "O = ${ORG_NAME}" >> zoasvc.conf
fi
if [ "${ORG_UNIT}x" != "x" ]
then
  echo "OU = ${ORG_UNIT}" >> zoasvc.conf
fi
echo "CN = ${THISHOST_LOWER}" >> zoasvc.conf
# Create certificate signing request
rm -f zoasvc.csr
openssl req -new -key zoasvc.key -config zoasvc.conf \
  -passout file:.pw -passin file:.pw \
  -out zoasvc.csr
# Create extensions configuration file for certificate signing
echo "authorityKeyIdentifier=keyid,issuer" > zoasvc.ext
echo "basicConstraints=CA:FALSE" >> zoasvc.ext
echo "subjectAltName = @alt_names" >> zoasvc.ext
echo "[alt_names]" >> zoasvc.ext
echo "DNS.1 = ${THISHOST_LOWER}" >> zoasvc.ext
echo "DNS.2 = localhost" >> zoasvc.ext
echo "DNS.3 = host.docker.internal" >> zoasvc.ext
echo "IP.1 = ${THISIP}" >> zoasvc.ext
echo "IP.2 = 127.0.0.1" >> zoasvc.ext
# Sign certificate
rm -f zoasvc.crt
openssl x509 -req -CA rootCA.crt -CAkey rootCA.key \
  -in zoasvc.csr -out zoasvc.crt -days 3650 \
  -CAcreateserial -extfile zoasvc.ext \
  -passin file:.pw
# Import everything into a keystore
rm -f zoasvc.ks
openssl pkcs12 -export \${OSSL_LEGACY} -out zoasvc.ks -name zoa-services \
  -inkey zoasvc.key -in zoasvc.crt \
  -passin file:.pw -passout file:.pw
# Truststore for client
# If truststores already exist, change truststore password:
if [ -f zoasvc.ts ]
then
  . .pw.ts
  keytool -storepasswd -keystore zoasvc.ts -new "\${NEWPASS}" \
    -storepass "\${OLDPASS}" -storetype pkcs12
fi
if [ -f datastore.ts ]
then
  . .pw.ts
  keytool -storepasswd -keystore datastore.ts -new "\${NEWPASS}" \
    -storepass "\${OLDPASS}" -storetype pkcs12
fi
rm -f .pw.ts
# Remove any old certificates
keytool -delete -alias zoa-services -keystore zoasvc.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-intermediate -keystore zoasvc.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-root -keystore zoasvc.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -import -alias zoa-services -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore zoasvc.ts -trustcacerts \
  -file zoasvc.crt
keytool -import -alias ca-root -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore zoasvc.ts -trustcacerts \
  -file rootCA.crt
# PEM file with all certificates for OpenSearch and OpenSearch Dashboards
openssl pkcs12 -in zoasvc.ts -out zoasvc_all.pem \
  -nokeys -passin file:.pw
# Remove password file
rm -f .pw
mkdir -p /shared/config
tar cz --exclude *.sh *.* | base64 > /shared/config/zoasvc.tls
chmod 644 /shared/config/zoasvc.tls
END
    ) > tlsproc.sh
    echo "OLDPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )" > .pw.ts
    echo "NEWPASS=${ZOASVC_PASS}" >> .pw.ts
    cd ${WORKDIR}/tmp_${RUNTIME}/
    UTILTSTAMP=$( date +"%s" )
    startUtil ${UTILTSTAMP}
    # Reuse existing artifacts only if ${WORKDIR}/zoasvc.tls is present.
    # If it is not present, then we have to assume that no valid password is present for whatever existing TLS artifacts are hanging around.
    # Attempting to re-use them would only cause errors.
    if [ -f ${WORKDIR}/zoasvc.tls ]
    then
      tmpUnpackCertsNoError ${UTILTSTAMP}
    else
      ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "mkdir -p /shared/tls_tmp && rm -f /shared/tls_tmp/*"
    fi
    tar cf tls_config.tar tls_tmp && ${OCI_AGENT} cp tls_config.tar zaiops-util-${UTILTSTAMP}:/shared/
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "rm -f /shared/config/zoasvc.tls ; tar -C /shared -xf /shared/tls_config.tar ; chmod 755 /shared/tls_tmp/*.sh ; rm -f /shared/tls_config.tar"
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "cd /shared/tls_tmp ; ./tlsproc.sh ; cd /shared ; rm -Rf tls_tmp"
    dumpTLSArtifacts ${UTILTSTAMP}
    stopUtil ${UTILTSTAMP}
    # Clean up
    cd ${ORIGIN} && rm -Rf ${WORKDIR}/tmp_${RUNTIME}
    writeTlsPwd
  elif [ -f ${WORKDIR}/zoasvc.tls ] && [ "${CMDOPT}" != "FORCEGEN" ]
  then
    logToStdout "${INFOMSG}TLS artifacts already exist, and 'force-generate' option was not specified.\n${SPACEMSG}Existing TLS artifacts will be reused."
    UTILTSTAMP=$( date +"%s" )
    startUtil ${UTILTSTAMP}
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "mkdir -p /shared/config"
    ${OCI_AGENT} cp ${WORKDIR}/zoasvc.tls zaiops-util-${UTILTSTAMP}:/shared/config/
    stopUtil ${UTILTSTAMP}
    echo ""
  fi
}

listTrustedCerts() {
  TSPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tmpUnpackCerts ${UTILTSTAMP}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "keytool -list -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' ; rm -Rf /shared/tls_tmp"
  echo ""
  stopUtil ${UTILTSTAMP}
}

deleteTrustedCert() {
  ALIAS=${1}
  NOSTART=${2}
  TSPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  if [ "${ALIAS}x" == "x" ]
  then
    logToStdout "${ERRORMSG}No alias provided."
    exit 1
  fi
  RC=0
  # If called from another function, the utility container is expected to be already running; otherwise, start it
  if [ "${NOSTART}x" == "x" ]
  then
    UTILTSTAMP=$( date +"%s" )
    startUtil ${UTILTSTAMP}
    tmpUnpackCerts ${UTILTSTAMP}
  fi
  ${OCI_AGENT} exec zaiops-util-${UTILTSTAMP} bash -c "keytool -list -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' -alias ${ALIAS}" > /dev/null
  if [ $? -eq 0 ]
  then
    # Certificate exists and can be deleted
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "keytool -delete -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' -alias ${ALIAS} ; echo '${TSPASS}' > /tmp/.pw ; openssl pkcs12 -in /shared/tls_tmp/zoasvc.ts -out /shared/tls_tmp/zoasvc_all.pem -nokeys -passin file:/tmp/.pw ; rm /tmp/.pw"
  else
    logToStdout "${ERRORMSG}Certificate with alias '${ALIAS}' does not exist in trust store."
    RC=1
  fi
  ${OCI_AGENT} exec zaiops-util-${UTILTSTAMP} bash -c "keytool -list -keystore /shared/tls_tmp/datastore.ts -storepass '${TSPASS}' -alias ${ALIAS}" > /dev/null
  if [ $? -eq 0 ]
  then
    # Certificate exists and can be deleted
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "keytool -delete -keystore /shared/tls_tmp/datastore.ts -storepass '${TSPASS}' -alias ${ALIAS}"
  fi
  # If called from another function, that function is expected to have further plans for the TLS artifacts; otherwise, repackage them
  if [ "${NOSTART}x" == "x" ]
  then
    tmpRepackCerts ${UTILTSTAMP}
    dumpTLSArtifacts ${UTILTSTAMP}
    stopUtil ${UTILTSTAMP}
    echo ""
  fi
  if [ ${RC} -ne 0 ]
  then
    exit 1
  fi
}

syncExternalCertStores() {
  TSPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tmpUnpackCerts ${UTILTSTAMP}
  echo ""
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "echo '${TSPASS}' > /tmp/.pw ; openssl pkcs12 -in /shared/tls_tmp/zoasvc.ts -out /shared/tls_tmp/zoasvc_all.pem -nokeys -passin file:/tmp/.pw ; rm /tmp/.pw"
  tmpRepackCerts ${UTILTSTAMP}
  dumpTLSArtifacts ${UTILTSTAMP}
  stopUtil ${UTILTSTAMP}
  echo ""
}

importCert() {
  TSPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  CERTPATH=${1}
  CERTFILE=$( basename ${CERTPATH} )
  ALIAS=${2}
  if [ "${CERTFILE}x" == "x" ]
  then
    logToStdout "${ERRORMSG}Can't extract certificate file name from path '${CERTPATH}'."
    exit 1
  elif [ "${ALIAS}x" == "x" ]
  then
    logToStdout "${ERRORMSG}No alias provided."
    exit 1
  fi
  unset RESPONSE
  RC=0
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tmpUnpackCerts ${UTILTSTAMP}
  echo ""
  ${OCI_AGENT} exec zaiops-util-${UTILTSTAMP} bash -c "keytool -list -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' -alias ${ALIAS}" > /dev/null
  if [ $? -eq 0 ]
  then
    echo "Alias ${ALIAS} already exists. Do you want to replace it with the certificate at ${CERTPATH}? (y/N)  "
    read -e RESPONSE
    RESPONSE=${RESPONSE:-"n"}
    if [ "${RESPONSE}" == "y" ] || [ "${RESPONSE}" == "Y" ]
    then
      deleteTrustedCert ${ALIAS} NOSTART
    elif [ "${RESPONSE}" == "n" ] || [ "${RESPONSE}" == "N" ]
    then
      RC=999
      echo ""
    else
      logToStdout "${ERRORMSG}Invalid response '${RESPONSE}'."
      RC=1
    fi
  fi
  if [ ${RC} -eq 0 ]
  then
    ${OCI_AGENT} cp ${CERTPATH} zaiops-util-${UTILTSTAMP}:/tmp
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "keytool -import -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' -alias ${ALIAS} -noprompt -trustcacerts -keypass '${TSPASS}' -file /tmp/${CERTFILE} ; keytool -import -keystore /shared/tls_tmp/datastore.ts -storepass '${TSPASS}' -alias ${ALIAS} -noprompt -trustcacerts -keypass '${TSPASS}' -file /tmp/${CERTFILE} ; echo '${TSPASS}' > /tmp/.pw ; openssl pkcs12 -in /shared/tls_tmp/zoasvc.ts -out /shared/tls_tmp/zoasvc_all.pem -nokeys -passin file:/tmp/.pw ; rm /tmp/${CERTFILE} /tmp/.pw"
  fi
  tmpRepackCerts ${UTILTSTAMP}
  dumpTLSArtifacts ${UTILTSTAMP}
  stopUtil ${UTILTSTAMP}
  echo ""
  if [ ${RC} -eq 1 ]
  then
    echo ""
  fi
}

##### MAIN
case "$1" in
  gen-tls )
    checkConfigFile
    sourceZoaConfig
    createZoaSvcCert FORCEGEN
    createZoaSecCert
    ;;
  prepare )
    checkConfigFile
    makeWorkDir
    dockerLogin
    ;;
  oci-shell )
    checkConfigFile
    checkWorkDir
    sourceZoaConfig
    shellInfo
    export WORKDIR
    export BASH_SILENCE_DEPRECATION_WARNING=1
    export -f zoacompose zoabaseserial zoagenrealm zoasvccheck zdapgetextensions zdapload zoadown zdapdown \
      logmsgmldown metricmldown zaacommondown zoagetlibraries zoadeletelibrary zoaexportlibrary zoaimportlibrary \
      zoaloadip zoaupdateips waitforservice getadmincredentials reloadips checkipresult zoahelp sed tar
    bash --rcfile <(echo 'PS1="\h: \W [OCI] > "' ; cat ~/.bashrc)
    ;;
  enable-metricml )
    checkConfigFile
    enableFeature METRICML
    ;;
  enable-logmsgml )
    checkConfigFile
    enableFeature LOGMSGML
    ;;
  enable-zaacommon )
    checkConfigFile
    enableFeature ZAACOMMON
    ;;
  enable-zdap )
    checkConfigFile
    enableFeature ZDAP
    ;;
  clean )
    checkConfigFile
    cleanup
    ;;
  * )
    help
    ;;
esac

