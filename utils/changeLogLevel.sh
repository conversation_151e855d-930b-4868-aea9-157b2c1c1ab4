#!/bin/bash

package=$1
level="$2"
file="${3:-log4j2.xml}"

if [[ "$level" == "get" ]]; then
  if [[ $file == *"yml"* ]]; then
    sed -n '/^logging:/,/'$package':/ s/.*'$package': \([^"]*\).*/\1/p' $file
  else
    sed -n 's/.*"'"$package"'" level="\([^"]*\).*/\1/p' $file
  fi
else
  if [[ $file == *"yml"* ]]; then
    sed -i -E '/^logging:/,/'$package':/ s/('$package':).*/\1 '${level^^}'/' $file
  else
    sed -i -E  's/("'"$package"'" level=)([a-zA-Z0-9"-${}:_])+/\1"'"$level"'"/g' $file
  fi
fi
