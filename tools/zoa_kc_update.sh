#!/bin/bash
########################################################## {COPYRIGHT-TOP} ###
## Licensed Materials - Property of IBM
## 5698-LDA (C) Copyright IBM Corp. 2023, 2025
## All rights reserved.
## US Government Users Restricted Rights - Use, duplication or
## disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
########################################################## {COPYRIGHT-END} ###

INFOMSG="\x1b[32;20mINFO    >> "
ERRORMSG="\x1b[31;20mERROR   >> "
WARNMSG="\x1b[33;20mWARNING >> "
SPACEMSG="           "
ENDMSG="\x1b[0m"

SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
OLDCLIENT=keep
KEYCLOAK_ADMIN=$1
ZAIOPS_KEYCLOAK_ADMIN_PASS=$2
REALM_ADMIN=$3
REALM_PWD=$4
TRUSTSTORE_PASS=$5
ZAIOPS_KEYCLOAK_HOST=$6
KEYCLOAK_PORT=$7
GATEWAY_PORT=$8
KEYCLOAK_ADMIN_PASSWORD=$( echo "${ZAIOPS_KEYCLOAK_ADMIN_PASS}" | base64 -d )
REALM_PWD_C=$( echo "${REALM_PWD}" | base64 -d )
TRUSTSTORE_PASS_C=$( echo "${TRUSTSTORE_PASS}" | base64 -d )
KEYCLOAK_HOST_LOWER=$( echo ${ZAIOPS_KEYCLOAK_HOST} | tr '[:upper:]' '[:lower:]' )
TRUSTSTORE=/ssl/zoasvc.ts.p12
SERVER=http://auth:8080
KC_CONTEXT_ROOT=$9
not_just_zdap=${10}

declare -A userFedIds

logToStdout() {
  MSG=${1}
  echo -e "${MSG}${ENDMSG}"
}

terminateCliSessions() {
  # Clear any active admin CLI sessions
  ADMIN_CLI_ID=$( /opt/keycloak/bin/kcadm.sh get clients -r master --fields id,clientId --format csv --noquotes  | grep admin-cli | cut -f 1 -d "," )
  SESSION_IDS=$( /opt/keycloak/bin/kcadm.sh get clients/${ADMIN_CLI_ID}/user-sessions -r master --fields id --format csv --noquotes )
  for SESSION_ID in ${SESSION_IDS}
  do
    /opt/keycloak/bin/kcadm.sh delete sessions/${SESSION_ID} -r master
  done
}

create_client_protocol_mappers() {  # - only for zaa
  # Add protocol mapper to new client
  PROTOMAPPER=$(/opt/keycloak/bin/kcadm.sh create clients/${NEWCLIENTTEST}/protocol-mappers/models -r IzoaKeycloak \
             -s name=groups \
             -s protocol=openid-connect \
             -s protocolMapper=oidc-usermodel-realm-role-mapper \
             -s 'config."id.token.claim"=true' \
             -s 'config."access.token.claim"=true' \
             -s 'config."claim.name"=groups' \
             -s 'config."userinfo.token.claim"=true' \
             -s 'config."multivalued"=true' \
             -s 'config."jsonType.label"=String'   2>&1)
  if [ $? -eq 0 ]
  then
    logToStdout "${INFOMSG}group protocol mapper successfully created."
  else
    logToStdout "${ERRORMSG}Failure occurred when attempting to create group protocol mapper. Details: ${PROTOMAPPER}"
  fi

  PROTOMAPPER=$(/opt/keycloak/bin/kcadm.sh create clients/${NEWCLIENTTEST}/protocol-mappers/models -r IzoaKeycloak \
             -s name=username \
             -s protocol=openid-connect \
             -s protocolMapper=oidc-usermodel-property-mapper \
             -s 'config."user.attribute"=username' \
             -s 'config."id.token.claim"=true' \
             -s 'config."claim.name"=preferred_username' \
             -s 'config."access.token.claim"=true' \
             -s 'config."userinfo.token.claim"=true' \
             -s 'config."multivalued"=true' \
             -s 'config."jsonType.label"=String'   2>&1)
  if [ $? -eq 0 ]
  then
    logToStdout "${INFOMSG}username protocol mapper successfully created."
  else
    logToStdout "${ERRORMSG}Failure occurred when attempting to create username protocol mapper. Details: ${PROTOMAPPER}"
  fi

  PROTOMAPPER=$(/opt/keycloak/bin/kcadm.sh create clients/${NEWCLIENTTEST}/protocol-mappers/models -r IzoaKeycloak \
             -s name=zoa-client-audience \
             -s protocol=openid-connect \
             -s protocolMapper=oidc-audience-mapper \
             -s 'config."included.client.audience"=zoa-client' \
             -s 'config."id.token.claim"=true' \
             -s 'config."access.token.claim"=true' \
             -s 'config."userinfo.token.claim"=true'  2>&1)
  if [ $? -eq 0 ]
  then
    logToStdout "${INFOMSG}zoa-client-audience protocol mapper successfully created."
  else
    logToStdout "${ERRORMSG}Failure occurred when attempting to create zoa-client-audience protocol mapper. Details: ${PROTOMAPPER}"
  fi
}

create_zoa-client() { # - only for zaa
  NEWCLIENTTEST=$(/opt/keycloak/bin/kcadm.sh create clients -r IzoaKeycloak -i \
           -s clientId=zoa-client \
           -s name="Z Operational Analytics" \
           -s baseUrl=https://${KEYCLOAK_HOST_LOWER}:${GATEWAY_PORT}/piFramework \
           -s alwaysDisplayInConsole=true \
           -s 'authenticationFlowBindingOverrides.browser=' \
           -s redirectUris=[\"*\"] \
           -s webOrigins=[\"*\"] \
           -s publicClient=true \
           -s directAccessGrantsEnabled=true \
           -s 'attributes."backchannel.logout.session.required"=true')

  if [ $? -eq 0 ]
  then
    logToStdout "${INFOMSG}zoa-client successfully created."

    # Add protocol mapper to new client
    create_client_protocol_mappers
  else
    logToStdout "${ERRORMSG}Unable to create zoa-client.  Unable to continue."
    exit 1
  fi
}

migrate_old-client() {  # - only for zaa
  # 'login-app' implementation exists, migrate it to 'zoa-client' implementation.
  /opt/keycloak/bin/kcadm.sh update clients/${OLDCLIENTTEST} -r IzoaKeycloak -s clientId=zoa-client
  /opt/keycloak/bin/kcadm.sh update clients/${OLDCLIENTTEST} -r IzoaKeycloak -s name="Z Operational Analytics"
  /opt/keycloak/bin/kcadm.sh update clients/${OLDCLIENTTEST} -r IzoaKeycloak -s baseUrl=https://${KEYCLOAK_HOST_LOWER}:${GATEWAY_PORT}/piFramework
  /opt/keycloak/bin/kcadm.sh update clients/${OLDCLIENTTEST} -r IzoaKeycloak -s alwaysDisplayInConsole=true
  # Make sure that there is no override for the browser authentication flow
  /opt/keycloak/bin/kcadm.sh update clients/${OLDCLIENTTEST} -r IzoaKeycloak -s 'authenticationFlowBindingOverrides.browser='

  # Check for protocol mapper; create it if not present
  PROTOMAPPER=$(/opt/keycloak/bin/kcadm.sh create clients/${OLDCLIENTTEST}/protocol-mappers/models -r IzoaKeycloak \
         -s name=zoa-client-audience \
         -s protocol=openid-connect \
         -s protocolMapper=oidc-audience-mapper \
         -s 'config."included.client.audience"=zoa-client' \
         -s 'config."id.token.claim"=true' \
         -s 'config."access.token.claim"=true'  2>&1)
  if [ $? -eq 0 ]
  then
    logToStdout "${INFOMSG}zoa-client-audience protocol mapper successfully created."
  elif [ "${PROTOMAPPER}" = "Protocol mapper exists with same name" ]
  then
    logToStdout "${INFOMSG}zoa-client-audience protocol mapper already exists."
  else
    logToStdout "${ERRORMSG}Failure occurred when attempting to create zoa-client-audience protocol mapper. Details: ${PROTOMAPPER}"
  fi

  # Remove legacy protocol mapper
  LAA_ID=$(/opt/keycloak/bin/kcadm.sh get clients/${OLDCLIENTTEST}/protocol-mappers/models -r IzoaKeycloak --fields id,name --format csv --noquotes | grep login-app-audience | cut -f 1 -d ",")
  if [ "${LAA_ID}x" == "x" ]
  then
    logToStdout "${INFOMSG}Legacy protocol mapper 'login-app-audience' not found."
  else
    logToStdout "${INFOMSG}Deleting legacy protocol mapper 'login-app-audience'."
    /opt/keycloak/bin/kcadm.sh delete clients/${OLDCLIENTTEST}/protocol-mappers/models/${LAA_ID} -r IzoaKeycloak
  fi

  NEWCLIENTTEST=OLDCLIENTTEST
}

create_custom_browser_flow() {
  # Make copy of 'browser' authentication flow - authentication/flows/browser used by zdap also during the zdap specific install steps
  /opt/keycloak/bin/kcadm.sh create authentication/flows/browser/copy -r IzoaKeycloak -s newName="ZOA browser flow with user session limit"
  CBF=$(/opt/keycloak/bin/kcadm.sh get authentication/flows -r IzoaKeycloak --format csv --fields id,alias --noquotes | grep -i "ZOA browser flow with user session limit" | cut -f 1 -d ",")
  # Add user session counter
  CID=$(/opt/keycloak/bin/kcadm.sh create authentication/flows/ZOA%20browser%20flow%20with%20user%20session%20limit%20forms/executions/execution -r IzoaKeycloak -i -s provider=user-session-limits)
  # Update requirement for user session counter execution
  /opt/keycloak/bin/kcadm.sh update authentication/flows/ZOA%20browser%20flow%20with%20user%20session%20limit%20forms/executions -r IzoaKeycloak  -b '{ "id" : "'${CID}'" , "requirement" : "REQUIRED" }'
  # Configure user session limit
  /opt/keycloak/bin/kcadm.sh create authentication/executions/${CID}/config -r IzoaKeycloak -s alias="ZOA browser session limit" -s 'config."behavior"="Deny new session"' -s 'config."errorMessage"="User session limit exceeded. Max. concurrent sessions per user: 3 per authentication client, 5 total."' -s 'config."userClientLimit"=3' -s 'config."userRealmLimit"=5'
  
  if [ "${not_just_zdap}x" != "x" ]
  then
    # Connection to zoa-client - only for zaa -- this should be moved to a separate routine
    /opt/keycloak/bin/kcadm.sh update clients/${NEWCLIENTTEST} -r IzoaKeycloak -b '{ "authenticationFlowBindingOverrides" : { "browser" : "'${CBF}'" } }'
  fi
}

create_email_2fa_flow() {
  # Make copy of 'browser' authentication flow - not directly used by zdap, but doesn't hurt to include it.=
  /opt/keycloak/bin/kcadm.sh create authentication/flows/ZOA%20browser%20flow%20with%20user%20session%20limit/copy -r IzoaKeycloak -s newName="ZOA Browser Email 2FA"
  # Get ID for conditional OTP execution
  CID=$(/opt/keycloak/bin/kcadm.sh get authentication/flows/ZOA%20Browser%20Email%202FA/executions -r IzoaKeycloak  --fields id,displayName --format csv --noquotes | grep -i "Conditional OTP" | cut -f 1 -d ",")
  # Update requirement for conditional OTP execution
  /opt/keycloak/bin/kcadm.sh update authentication/flows/ZOA%20Browser%20Email%202FA/executions -r IzoaKeycloak  -b '{ "id" : "'${CID}'" , "requirement" : "REQUIRED" }'
  # Get ID for 'OTP Form' execution
  CID=$(/opt/keycloak/bin/kcadm.sh get authentication/flows/ZOA%20Browser%20Email%202FA/executions -r IzoaKeycloak  --fields id,displayName --format csv --noquotes | grep "OTP Form" | cut -f 1 -d ",")
  # Disable 'OTP Form' execution
  /opt/keycloak/bin/kcadm.sh update authentication/flows/ZOA%20Browser%20Email%202FA/executions -r IzoaKeycloak  -b '{ "id" : "'${CID}'" , "requirement" : "DISABLED" }'
  # Create 'Email OTP' execution
  CID=$(/opt/keycloak/bin/kcadm.sh create authentication/flows/ZOA%20Browser%20Email%202FA%20ZOA%20browser%20flow%20with%20user%20session%20limit%20Browser%20-%20Conditional%20OTP/executions/execution -r IzoaKeycloak -i -s provider=zoa-email-authenticator)
  # Update requirement for 'Email OTP' execution
  /opt/keycloak/bin/kcadm.sh update authentication/flows/ZOA%20Browser%20Email%202FA%20ZOA%20browser%20flow%20with%20user%20session%20limit%20Browser%20-%20Conditional%20OTP/executions -r IzoaKeycloak  -b '{ "id" : "'${CID}'" , "requirement" : "REQUIRED" }'
  # Disable and re-enable 'Username Password Form' execution in an attempt to prevent it from ending up below the 'Email OTP' execution
  CID=$( /opt/keycloak/bin/kcadm.sh get authentication/flows/ZOA%20Browser%20Email%202FA/executions -r IzoaKeycloak  --fields id,displayName --format csv --noquotes | grep "Username Password Form" | cut -f 1 -d "," )
  /opt/keycloak/bin/kcadm.sh update authentication/flows/ZOA%20Browser%20Email%202FA/executions -r IzoaKeycloak  -b '{ "id" : "'${CID}'" , "requirement" : "DISABLED" }'
  /opt/keycloak/bin/kcadm.sh update authentication/flows/ZOA%20Browser%20Email%202FA/executions -r IzoaKeycloak  -b '{ "id" : "'${CID}'" , "requirement" : "REQUIRED" }'
}

create_decrypt_password_flow() { # used by zaa only, should the authentication/flows be creation just in case for zdap?
  # Decrypt Password authentication flow
  DPF=$(/opt/keycloak/bin/kcadm.sh create authentication/flows -r IzoaKeycloak -i -s alias="Decrypt Password" -s description="Decrypt AES encrypted password" -s providerId="basic-flow" -s topLevel=true)

  CID=$(/opt/keycloak/bin/kcadm.sh create authentication/flows/Decrypt%20Password/executions/execution -r IzoaKeycloak -i -s provider="password-encryption")
  if [ "${CID}x" != "x" ]
  then
    # Update requirement for 'Decrypt Password forms' execution
    /opt/keycloak/bin/kcadm.sh update authentication/flows/Decrypt%20Password/executions -r IzoaKeycloak  -b '{ "id" : "'${CID}'" , "requirement" : "REQUIRED" }'
  fi

  if [ "${not_just_zdap}x" != "x" ]
  then
    # Connection to zoa-client - only for zaa -- should be moved to the separate routine
    /opt/keycloak/bin/kcadm.sh update clients/${NEWCLIENTTEST} -r IzoaKeycloak -b '{ "authenticationFlowBindingOverrides" : { "direct_grant" : "'${DPF}'" } }'
  fi
}

create_user_auth_client_scope() {  # - used by zaa only
  # USER client scope
  UACS=$(/opt/keycloak/bin/kcadm.sh create client-scopes -r IzoaKeycloak -i \
                         -s name="USER" \
                         -s description="User authorization" \
                         -s protocol="openid-connect" \
                         -s 'attributes."display.on.consent.screen"=true' \
                         -s 'attributes."include.in.token.scope"=true')

  if [ "${not_just_zdap}x" != "x" ]
  then
    # Connection to zoa-client
    /opt/keycloak/bin/kcadm.sh update clients/${NEWCLIENTTEST}/default-client-scopes/${UACS} -r IzoaKeycloak
  fi
}

disable_federations() {
  # Check if there are any enabled federations.  If so, disable them.
  userFeds=$( /opt/keycloak/bin/kcadm.sh get components -r IzoaKeycloak -q type="org.keycloak.storage.UserStorageProvider" --fields id,name,providerId --format csv --noquotes )
  for i in ${userFeds}
  do
    userFedId=$( echo "${i}" | cut -f 1 -d "," )
    userFedName=$( echo "${i}" | cut -f 2 -d "," )
    userFedProvider=$( echo "${i}" | cut -f 3 -d "," )
    if [ "${OSTYPE}" == "OS/390" ]
    then
      # We are under z/OS UNIX System Services; Keycloak returns JSON output as garbage ... and field positions in CSV output are not guaranteed
      #   need to attempt to get information via grep
      /opt/keycloak/bin/kcadm.sh get components/${userFedId} -r IzoaKeycloak --format csv | grep -q \"true\"
      if [ $? == 0 ]
      then
        fedEnabled=true
      else
        fedEnabled=false
      fi
    else
      fedEnabled=$( /opt/keycloak/bin/kcadm.sh get components/${userFedId} -r IzoaKeycloak 2>/dev/null | grep \"enabled\" | head -1 | cut -f 4 -d "\"" )
    fi
    if [ "${fedEnabled}" == "true" ]
    then
      logToStdout "${INFOMSG}Disabling the ${userFedName} user federation"
      userFedIds[${userFedName}]=${userFedId}
      /opt/keycloak/bin/kcadm.sh update components/${userFedId} -r IzoaKeycloak -s 'config.enabled=["false"]'
    fi
  done
}

enable_federations() {
  for key in "${!userFedIds[@]}"
  do
    logToStdout "${INFOMSG}Enabling ${key} user federation"
    /opt/keycloak/bin/kcadm.sh update components/${userFedIds[$key]} -r IzoaKeycloak -s 'config.enabled=["true"]'
  done
}

# Main process
# Process IzoaKeycloak user realm
#/opt/keycloak/bin/kcadm.sh config truststore --trustpass "${TRUSTSTORE_PASS_C}" ${TRUSTSTORE}
/opt/keycloak/bin/kcadm.sh config credentials --server ${SERVER}/${KC_CONTEXT_ROOT} --realm master --user ${KEYCLOAK_ADMIN} --password ${KEYCLOAK_ADMIN_PASSWORD}
if [ $? -eq 0 ]
then
  # Check whether IzoaKeycloak realm exists
  REALMTEST=$(/opt/keycloak/bin/kcadm.sh get realms/IzoaKeycloak --fields realm --format csv --noquotes 2>/dev/null)
  if [ "${REALMTEST}x" == "x" ]
  then
    # IzoaKeycloak realm does not exist, create it
    logToStdout "${INFOMSG}IzoaKeycloak realm does not exist. It will be created."
    REALMTEST=$(/opt/keycloak/bin/kcadm.sh create realms -s realm=IzoaKeycloak -s enabled=true -s id=IzoaKeycloak -s displayName="Z Operational Analytics" -i)
    if [ $? -eq 0 ]
    then
      logToStdout "${INFOMSG}Realm IzoaKeycloak successfully created."

      # Create client - only for zaa
      if [ "${not_just_zdap}x" != "x" ]
      then
        logToStdout "${INFOMSG}Creating 'zoa-client'."
        create_zoa-client
      fi
    else
      logToStdout "${ERRORMSG}Unable to create IzoaKeycloak realm.  Unable to continue."
      exit 1
    fi
  else
    logToStdout "${INFOMSG}IzoaKeycloak realm already exists."
    if [ "${not_just_zdap}x" != "x" ]
    then
      # Check whether login-app exists - only for zaa
      OLDCLIENTTEST=$(/opt/keycloak/bin/kcadm.sh get clients -r IzoaKeycloak -q clientId=login-app --fields id --format csv --noquotes)
      # Check whether zoa-client already exists - only for zaa
      NEWCLIENTTEST=$(/opt/keycloak/bin/kcadm.sh get clients -r IzoaKeycloak -q clientId=zoa-client --fields id --format csv --noquotes)
      if [ "${OLDCLIENTTEST}x" == "x" ] && [ "${NEWCLIENTTEST}x" != "x" ]  # - only for zaa
      then
        # New client exists, old one doesn't; looks like this realm has already been migrated
        logToStdout "${INFOMSG}'IzoaKeycloak' realm already migrated to new 'zoa-client' implementation."
        # Make sure that there is no override for the browser authentication flow
        /opt/keycloak/bin/kcadm.sh update clients/${NEWCLIENTTEST} -r IzoaKeycloak -s 'authenticationFlowBindingOverrides.browser='
      fi
    fi
  fi

  # Set themes needed for login and email 2FA
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s loginTheme=email-code-theme
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s emailTheme=email-code-theme

  # Permit authentication requests through gateway
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s browserSecurityHeaders."contentSecurityPolicy"="frame-src 'self'; frame-ancestors https://${KEYCLOAK_HOST_LOWER}:${GATEWAY_PORT} https://${KEYCLOAK_HOST_LOWER}:${KEYCLOAK_PORT}; object-src 'none'"
  # ...also needed for 'master' realm so that admin console can be displayed
  /opt/keycloak/bin/kcadm.sh update realms/master -s browserSecurityHeaders."contentSecurityPolicy"="frame-src 'self'; frame-ancestors https://${KEYCLOAK_HOST_LOWER}:${GATEWAY_PORT} https://${KEYCLOAK_HOST_LOWER}:${KEYCLOAK_PORT}; object-src 'none'"
  
  if [ "${not_just_zdap}x" != "x" ]
  then
    if [ "${OLDCLIENTTEST}x" == "x" ] && [ "${NEWCLIENTTEST}x" == "x" ]  # - only for zaa
    then
      # Something is wrong: neither old nor new ZOA client is present
      # logToStdout "${ERRORMSG}Neither 'login-app' nor 'zoa-client' can be found. 'IzoaKeycloak' realm is not completely configured."
      #exit 1
      # Create client (can happen if zdap was installed first) - only for zaa
      logToStdout "${INFOMSG}Creating 'zoa-client'."
      create_zoa-client

    elif [ "${OLDCLIENTTEST}x" != "x" ] && [ "${NEWCLIENTTEST}x" == "x" ]  # - only for zaa
    then
      # Old client exists, new one doesn't; let's rename old to new
      logToStdout "${INFOMSG}'login-app' implementation exists and will be migrated to 'zoa-client' implementation."
      migrate_old-client

    elif [ "${OLDCLIENTTEST}x" != "x" ] && [ "${NEWCLIENTTEST}x" != "x" ]  # - only for zaa
    then
      # Both old and new client exist; this is unexpected, but does not necessarily constitute a problem
      logToStdout "${WARNMSG}Both 'login-app' and 'zoa-client' were found.\n \
        ${SPACEMSG}This indicates a non-standard modification of the 'IzoaKeycloak' realm."
    fi
  fi

  # Check whether basic browser authentication flow with user session limit already exists
  SESLIMTEST=$(/opt/keycloak/bin/kcadm.sh get authentication/flows -r IzoaKeycloak --fields id,alias --format csv --noquotes | grep "ZOA browser flow with user session limit" | cut -f 1 -d ",")
  if [ "${SESLIMTEST}x" == "x" ]
  then
    # Basic user session limit flow does not yet exist; create it
    logToStdout "${INFOMSG}Browser authentication flow with user session limit not yet defined. It will be created."
    create_custom_browser_flow
  else
    # logToStdout "${INFOMSG}Browser authentication flow with user session limit already defined."
    # When upgrading from Keycloak 21 to 26, some custom authentication flows seem to not work any longer; try deleting and re-creating them
    logToStdout "${INFOMSG}Browser authentication flow with user session limit was already defined but will be re-created."
    CIDS=$( /opt/keycloak/bin/kcadm.sh get clients -r IzoaKeycloak --format csv --noquotes | grep ${SESLIMTEST} | cut -f 1 -d ',' )
    for CID in ${CIDS}
    do
      /opt/keycloak/bin/kcadm.sh update clients/${CID} -r IzoaKeycloak -s 'authenticationFlowBindingOverrides.browser='
    done
    /opt/keycloak/bin/kcadm.sh delete authentication/flows/${SESLIMTEST} -r IzoaKeycloak
    create_custom_browser_flow
  fi

  # Check whether e-mail 2FA authentication flow already exists
  EMAIL2FATEST=$(/opt/keycloak/bin/kcadm.sh get authentication/flows -r IzoaKeycloak --fields id,alias --format csv --noquotes | grep "ZOA Browser Email 2FA" | cut -f 1 -d ",")
  if [ "${EMAIL2FATEST}x" == "x" ]
  then
    # e-mail 2FA flow does not yet exist; create it
    logToStdout "${INFOMSG}'E-mail 2FA' authentication flow not yet defined. It will be created."
    create_email_2fa_flow
  else
    # When upgrading from Keycloak 21 to 26, some custom authentication flows seem to not work any longer; try deleting and re-creating them
    logToStdout "${INFOMSG}'E-mail 2FA' authentication flow was already defined but will be re-created."
    CIDS=$( /opt/keycloak/bin/kcadm.sh get clients -r IzoaKeycloak --format csv --noquotes | grep ${EMAIL2FATEST} | cut -f 1 -d ',' )
    for CID in ${CIDS}
    do
      /opt/keycloak/bin/kcadm.sh update clients/${CID} -r IzoaKeycloak -s 'authenticationFlowBindingOverrides.browser='
    done
    /opt/keycloak/bin/kcadm.sh delete authentication/flows/${EMAIL2FATEST} -r IzoaKeycloak
    create_email_2fa_flow
  fi

  # Check whether Decrypt Password authentication flow already exists
  DECRYPTPWTEST=$(/opt/keycloak/bin/kcadm.sh get authentication/flows -r IzoaKeycloak --fields id,alias --format csv --noquotes | grep "Decrypt Password" | cut -f 1 -d ",")
  if [ "${DECRYPTPWTEST}x" == "x" ]
  then
    # Decrypt Password flow does not yet exist; create it
    logToStdout "${INFOMSG}'Decrypt Password' authentication flow not yet defined. It will be created."
    create_decrypt_password_flow
  else
    # logToStdout "${INFOMSG}'Decrypt Password' authentication flow already defined."
    # When upgrading from Keycloak 21 to 26, some custom authentication flows seem to not work any longer; try deleting and re-creating them
    logToStdout "${INFOMSG}'Decrypt Password' authentication flow was already defined but will be re-created."
    CIDS=$( /opt/keycloak/bin/kcadm.sh get clients -r IzoaKeycloak --format csv --noquotes | grep ${DECRYPTPWTEST} | cut -f 1 -d ',' )
    for CID in ${CIDS}
    do
      /opt/keycloak/bin/kcadm.sh update clients/${CID} -r IzoaKeycloak -s 'authenticationFlowBindingOverrides.direct_grant='
    done
    /opt/keycloak/bin/kcadm.sh delete authentication/flows/${DECRYPTPWTEST} -r IzoaKeycloak
    create_decrypt_password_flow
  fi

  # Check whether USER authorization client scope already exists
  USERAUTHTEST=$(/opt/keycloak/bin/kcadm.sh get client-scopes -r IzoaKeycloak --fields id,name --format csv --noquotes | grep "USER" | cut -f 1 -d ",")
  if [ "${USERAUTHTEST}x" == "x" ]
  then
    # USER authorization client scope does not yet exist; create it
    logToStdout "${INFOMSG}'USER' client scope not yet defined. It will be created."
    create_user_auth_client_scope
  else
    logToStdout "${INFOMSG}'USER' client scope already defined."
  fi

  # Update brute force detection and other realm settings
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s bruteForceProtected=true
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s permanentLockout=true
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s failureFactor=3
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s quickLoginCheckMilliSeconds=1000
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s minimumQuickLoginWaitSeconds=60
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s accessTokenLifespan=180
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s accessTokenLifespanForImplicitFlow=1800
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s ssoSessionIdleTimeout=7200
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s rememberMe=true
  /opt/keycloak/bin/kcadm.sh update realms/IzoaKeycloak -s resetPasswordAllowed=true

  if [ "${not_just_zdap}x" != "x" ]
  then
    # Create roles for PI framework - only for zaa
    for ROLE in user admin
    do
      RROLEDESC="ZOA ${ROLE} permissions"
      CROLEDESC="To authorize ZOA ${ROLE} permissions"
      CID=$(/opt/keycloak/bin/kcadm.sh get clients -r IzoaKeycloak -q clientId=zoa-client --fields id --format csv --noquotes)
      CRID=$(/opt/keycloak/bin/kcadm.sh get-roles -r IzoaKeycloak --cclientid zoa-client --fields id,name --format csv --noquotes | grep ${ROLE} | cut -f 1 -d ",")
      RRID=$(/opt/keycloak/bin/kcadm.sh get roles -r IzoaKeycloak --fields id,name --format csv --noquotes | grep ${ROLE} | cut -f 1 -d ",")
      if [ "${CRID}x" == "x" ]
      then
        # Client role does not exist; create it
        logToStdout "${INFOMSG}Creating role '${ROLE}' in client 'zoa-client'..."
        /opt/keycloak/bin/kcadm.sh create clients/${CID}/roles -r IzoaKeycloak -s name=${ROLE} -s "description=${CROLEDESC}"
      fi
      if [ "${RRID}x" == "x" ]
      then
        # Realm role does not exist; create it
        logToStdout "${INFOMSG}Creating role '${ROLE}' in realm 'IzoaKeycloak'..."
        /opt/keycloak/bin/kcadm.sh create roles -r IzoaKeycloak -s name=${ROLE} -s "description=${RROLEDESC}" -s containerId=IzoaKeycloak
        # Make it a composite role with matching client role for zoa-client
        /opt/keycloak/bin/kcadm.sh add-roles -r IzoaKeycloak --rname ${ROLE} --cclientid zoa-client --rolename ${ROLE}
      fi
    done
  fi

  # Set up default roles
  /opt/keycloak/bin/kcadm.sh add-roles --rname default-roles-izoakeycloak --rolename user -r IzoaKeycloak

  # Temporarily disable any user federations so we can create some stand-alone ids
  disable_federations

  # Create realm administrator ID
  ID=$( /opt/keycloak/bin/kcadm.sh get users -r IzoaKeycloak -q username=${REALM_ADMIN} --fields id --format csv --noquotes )
  if [ "${ID}x" != "x" ]
  then
    /opt/keycloak/bin/kcadm.sh delete users/${ID}  -r IzoaKeycloak
  fi
  logToStdout "${INFOMSG}Creating user ${REALM_ADMIN}..."
  ID=$( /opt/keycloak/bin/kcadm.sh create users -s username=${REALM_ADMIN} -s enabled=true -r IzoaKeycloak -i 2>&1)
  if [ $? != 0 ]
  then
    logToStdout "${ERRORMSG}Unable to create '${REALM_ADMIN}' in realm 'IzoaKeycloak'"
  fi

  /opt/keycloak/bin/kcadm.sh set-password -r IzoaKeycloak --username ${REALM_ADMIN} --new-password ${REALM_PWD_C}
  /opt/keycloak/bin/kcadm.sh update users/${ID} -r IzoaKeycloak -s "email=${REALM_ADMIN}@zoa.biz" -s "firstName=${REALM_ADMIN}" -s "lastName=built-in"
  for ROLE in $( /opt/keycloak/bin/kcadm.sh get-roles -r IzoaKeycloak --cclientid realm-management --fields name --format csv --noquotes )
  do
    /opt/keycloak/bin/kcadm.sh remove-roles --uusername ${REALM_ADMIN} --cclientid realm-management --rolename ${ROLE} -r IzoaKeycloak
    /opt/keycloak/bin/kcadm.sh add-roles --uusername ${REALM_ADMIN} --cclientid realm-management --rolename ${ROLE} -r IzoaKeycloak
  done

  if [ "${not_just_zdap}x" != "x" ]
  then
    # Create out-of-the-box user IDs for PI framework; if there are old ones, delete and re-create them - only for zaa 
    for USER in piadmin piuser
    do
      ID=$(/opt/keycloak/bin/kcadm.sh get users -r IzoaKeycloak -q username=${USER} --fields id --format csv --noquotes)
      if [ "${ID}x" != "x" ]
      then
        /opt/keycloak/bin/kcadm.sh delete users/${ID}  -r IzoaKeycloak
      fi
      logToStdout "${INFOMSG}Creating user ${USER}..."
      ID=$(/opt/keycloak/bin/kcadm.sh create users -s username=${USER} -s enabled=true -r IzoaKeycloak -i 2>&1)
      /opt/keycloak/bin/kcadm.sh set-password -r IzoaKeycloak --username ${USER} --new-password 'changeme'
      /opt/keycloak/bin/kcadm.sh update users/${ID} -r IzoaKeycloak -s "email=${USER}@zoa.biz" -s "firstName=${USER}" -s "lastName=built-in"
      if [ "${USER}" == "piadmin" ]
      then
        /opt/keycloak/bin/kcadm.sh remove-roles -r IzoaKeycloak --uusername ${USER} --rolename user
        /opt/keycloak/bin/kcadm.sh add-roles -r IzoaKeycloak --uusername ${USER} --rolename admin
      fi
    done
  fi

  # Re-enable any previously disabled user federations
  enable_federations
fi

terminateCliSessions > /dev/null 2>&1
