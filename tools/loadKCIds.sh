#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2023
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            loadKCIds.sh
#
# Description:     IBM Z Data Analytics platform bulk load ids
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Script used to initialize RACF ids for IBM Z Data Analytics
#                  Analytics platform
#
# Syntax:          loadKCIds.sh
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

SCRIPTDIR="$( cd "$( dirname "$0" )/../.." && pwd )"
SCRIPTNAME="$( basename "$0" )"
BASEDIR=`dirname ${SCRIPTDIR}`
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
DELETE_NETWORK=false

. ${SCRIPTDIR}/utils/common_functions.sh

usage() {
  echo ""
  echo "Usage:"
  echo "  loadKCIds.sh [force]     to bulk load user IDs into Keycloak"
  echo ""
}

checkService() {
  SERVICE_NAME=${1}
  STATUS=`${OCI_AGENT} inspect --format '{{ index .State.Status }}' ${SERVICE_NAME} 2>/dev/null`
  RC=$?
  if [ ${RC} -ne 0 ] || ( [ ${RC} -eq 0 ] && [ "${STATUS}x" == "x" ] )
  then
    echo "Container ${SERVICE_NAME} does not exist."
    SERVICE_STATUS=DOWN
  elif [ "${STATUS}" = "running" ]
  then
    echo "Container ${SERVICE_NAME} is running."
    SERVICE_STATUS=UP
  else
    echo "Container ${SERVICE_NAME} is not in a steady state."
    SERVICE_STATUS=UNSTABLE
  fi
}

ARG=${1}
echo ""
echo "Provide the full path to a file containing user IDs you wish to load and their associated authorization groups:"
while [[ -z "${ZAIOPS_RACF_LDAP_IDS}" ]]
do
  read -e ZAIOPS_RACF_LDAP_IDS
done

if [ -s ${ZAIOPS_RACF_LDAP_IDS} ] && [ -r ${ZAIOPS_RACF_LDAP_IDS} ]
then
  set -a
  . ${BASEDIR}/${NEW_CONFIG}
  . ${BASEDIR}/${IBM_CONFIG}
  set +a
  if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
  then
    export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
  fi
  if [ "${OCI_AGENT}" = "docker" ]
  then
    if [ ${OCI_VERSION} -ge 20 ]
    then
      COMMON_CONTAINER_PREFIX=zoa-
    else
      COMMON_CONTAINER_PREFIX=""
    fi
  elif [ "${OCI_AGENT}" = "podman" ]
  then
    COMMON_CONTAINER_PREFIX=""
  fi

  # Check Keycloak status; bring it up if needed
  checkService ${COMMON_CONTAINER_PREFIX}auth
  if [ "${SERVICE_STATUS}" = "DOWN" ]
  then
    cd ${BASEDIR}

    # Bring up Keycloak in standard mode
    echo "Starting authentication service in standard mode..."
    ${SCRIPTDIR}/${OCI_AGENT}ManageZoa.sh up auth
    cd ${BASEDIR}
    printf "%-50s %s " "Waiting for authentication service to be ready" "..."
    ${SCRIPTDIR}/${OCI_AGENT}ManageZoa.sh wait-for-service auth 8443
    if [ $? -eq 1 ]
    then
      shutdownKeycloak standard
      exit 1
    fi
  elif [ "${SERVICE_STATUS}" = "UNSTABLE" ]
  then
    logToStdout "${WARNMSG}Service ${COMMON_CONTAINER_PREFIX}auth is not ready. Unable to proceed."
    exit 1
  fi

  # Move files into the container
  RACF_LDAP_IDS_BASE=`basename ${ZAIOPS_RACF_LDAP_IDS}`
  RACF_LDAP_IDS_DIR=`dirname ${ZAIOPS_RACF_LDAP_IDS}`
  chmod 755 ${SCRIPTDIR}/utils/keycloak/*.sh
  tar -C ${SCRIPTDIR}/utils/keycloak --numeric-owner --owner=0 --group=0 -cf - zoa_load_ids.sh | ${OCI_AGENT} cp - ${COMMON_CONTAINER_PREFIX}auth:/realm/
  tar -C ${RACF_LDAP_IDS_DIR} --numeric-owner --owner=0 --group=0 -cf - ${RACF_LDAP_IDS_BASE} | ${OCI_AGENT} cp - ${COMMON_CONTAINER_PREFIX}auth:/realm/

  # Execute command in container
  ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}auth bash -c "/realm/zoa_load_ids.sh ${ZAIOPS_KEYCLOAK_ADMIN} ${ZAIOPS_KEYCLOAK_ADMIN_PASS} ${ZAIOPS_ZOASVC_PASS} ${ZAIOPS_KEYCLOAK_HOST} ${ZAIOPS_KEYCLOAK_PORT} ${ZAIOPS_KC_CONTEXT_ROOT} /realm/${RACF_LDAP_IDS_BASE} ${ARG}"

  echo "Cleaning up container"
  ${OCI_AGENT} exec -u root ${COMMON_CONTAINER_PREFIX}auth bash -c "cd /realm && rm -f zoa_load_ids.sh && rm -f ${RACF_LDAP_IDS_BASE}"

  # If the service was down before, shut it down now
  if [ "${KC_SERVICE_STATUS}" = "DOWN" ]
  then
    shutdownKeycloak standard
  fi

  echo "Done"
else
  echo "${ZAIOPS_RACF_LDAP_IDS} is not found."
  exit 2
fi
