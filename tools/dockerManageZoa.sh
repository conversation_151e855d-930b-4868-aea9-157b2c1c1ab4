#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2022, 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            dockerManageZoa.sh
#
# Description:     IBM Z AIOps Common Services management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         <PERSON><PERSON><PERSON> used to manage IBM Z AIOps Docker-based services
#
# Syntax:          dockerManageZoa.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------
SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
cd ${BASEDIR}
RUNDATE=`date +"%Y-%m-%d_%H.%M.%S"`
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
PRODUCT_INVENTORY=${BASEDIR}/product.inventory
PICLI_DEBUG=""
ARCH=`uname -m`
which host > /dev/null 2>&1
if [ ! $? = 0 ]
then
  hashost=false
else
  hashost=true
fi
NAMESPACE_COMMON=zoa
COMMON_CONTAINER_PREFIX=${NAMESPACE_COMMON}-
SCRIPT_PREFIX=docker

umask 0022

set -a
. ${NEW_CONFIG}
. ${IBM_CONFIG}
if [ -f ${PRODUCT_INVENTORY} ]
then
  . ${PRODUCT_INVENTORY}
fi
set +a
if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
then
  export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
fi
KEY_PASS=$( echo "${ZAIOPS_ZOASVC_PASS}" | base64 -d )
API_PASS=$( echo "${API_PWD}" | base64 -d )
API_USER=$( echo "${API_USR}" | base64 -d )
# Calculate effective gateway rate limit
DAC=${DISCOVERY_AGENT_COUNT:-0}
GRM=${ZAIOPS_GATEWAY_REQUESTS_PER_MIN:-80}
export ZAIOPS_TOTAL_RATE_LIMIT=$( awk -v dac=${DAC} -v grm=${GRM} 'BEGIN { rounded = sprintf("%.0f", (dac+1)*grm*125/100); print rounded }' )
# Set service and command lists
COMMON_SVC_LIST='@(gateway|auth|discovery|kafkacontroller|kafkabroker)'
COMMON_CMD_LIST='@(up|down|logs|ps|help|start|stop|restart|purge|gather|inspect-images|kafka-topics|kafka-console-consumer|kafka-consumer-groups|kafka-prune|kafka-debug-logging|config-certificates|move-data|backup-data|restore-data|seed-ldap|get-log-level|set-log-level|check-kc-readiness|wait-for-service|update-keycloak-password)'

. ${SCRIPTDIR}/utils/common_functions.sh
if [ "${ARCH}" != "s390x" ] && [ "${ARCH}" != "x86_64" ]
then
  logToStdout "${ERRORMSG}Unsupported architecture ${ARCH}. Unable to proceed."
  exit 1
fi
for FEATURE in ${INSTALLED_FEATURES}
do
  if [ -f ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh ]
  then
    . ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh
  fi
done

CMD_LIST_TEMP="${COMMON_CMD_LIST}"
for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
do
  eval TEMPVAR=\"\$${UFEATURE}_CMD_LIST\"
  CMD_LIST_TEMP+="|${TEMPVAR}"
done
CMD_LIST='@('$( echo ${CMD_LIST_TEMP} |tr -d '()@' )')'

SVC_LIST_TEMP="${COMMON_SVC_LIST}"
for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
do
  eval TEMPVAR=\"\$${UFEATURE}_SVC_LIST\"
  SVC_LIST_TEMP+="|${TEMPVAR}"
done
SVC_LIST='@('$( echo ${SVC_LIST_TEMP} |tr -d '()@' )')'
SVC_LIST_TRIMMED=$( echo ${SVC_LIST} | tr -d '@()' )

# We always need the common services as a minimum; others are optional and may not be present
DOCKER_COMPOSE="docker compose --env-file ${NEW_CONFIG} -f zoacore-docker-compose.yml"
for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
do
  eval TEMPVAR=\"\$${UFEATURE}_DC\"
  DOCKER_COMPOSE+=" ${TEMPVAR}"
done

up() {
  DO_KEYCLOAK_POSTCONFIG=false
  if [ $# != 0 ]
  then
    echo "$@" | grep -q auth
    if [ $? -eq 0 ]
    then
      DO_KEYCLOAK_POSTCONFIG=true
    fi
    serviceCheck $@
    ${DOCKER_COMPOSE} up -d $@
  else
    DO_KEYCLOAK_POSTCONFIG=true
    # Do two-step startup when all services are started at once to avoid timeouts / resource competition
    ${DOCKER_COMPOSE} up -d discovery
    ${DOCKER_COMPOSE} up -d gateway
    # Wait for gateway service to come up
    printf "%-50s %s" "Waiting for gateway service to come up" "..."
    COUNT=0
    while :
    do
      echo -n .
      ${OCI_AGENT} logs zoa-gateway | grep -q "Started PiGatewayApplication" 2>/dev/null
      if [ $? -eq 0 ]
      then
        echo -n " "
        echo "successful after ${COUNT} seconds."
        break
      elif [ ${COUNT} -ge ${SERVICE_TIMEOUT} ]
      then
        echo -n " "
        echo "Gateway service still not ready after ${COUNT} seconds; giving up."
        logToStdout "${ERRORMSG}Service status:"
        echo "--------------------------------------------------------------------------------"
        ${SCRIPTDIR}/dockerManageZoa.sh ps
        echo "--------------------------------------------------------------------------------"
        logToStdout "${ERROR_MSG}Last ${TS_LOG_LINES} lines of service log output:"
        echo "--------------------------------------------------------------------------------"
        ${OCI_AGENT} logs zoa-gateway | tail -n ${TS_LOG_LINES}
        echo "--------------------------------------------------------------------------------"
        exit 1
      else
        sleep 5
        COUNT=$(( ${COUNT} + 5 ))
      fi
    done
    ${DOCKER_COMPOSE} up -d auth
    # Check for authentication service
    printf "%-50s %s " "Waiting for authentication service to be ready" "..."
    wait-for-service auth 8443
    ${DOCKER_COMPOSE} up -d kafkacontroller
    ${DOCKER_COMPOSE} up -d kafkabroker
    # Check for Kafka service
    printf "%-50s %s " "Waiting for Kafka broker service to be ready" "..."
    wait-for-service kafkabroker 19092
    # Bring up the rest of the services
    for FEATURE in ${INSTALLED_FEATURES}
    do
      case ${FEATURE} in
        "zoacommon")
          ${DOCKER_COMPOSE} up -d datastore
          ;;
        "zdap")
          ${DOCKER_COMPOSE} up -d parser
          ${DOCKER_COMPOSE} up -d dashboards
          ;;
        *)
          ${DOCKER_COMPOSE} up -d
          ;;
      esac
    done
  fi
  # Handle post-startup actions as needed
  # A. Keycloak updates in case of port changes
  if [ "${DO_KEYCLOAK_POSTCONFIG}" == "true" ]
  then
    if (isContainerRunning auth)
    then
      printf "%-50s %s " "Waiting for authentication service to be ready" "..."
      wait-for-service auth 8443
      chmod 755 ${SCRIPTDIR}/utils/keycloak/*.sh
      tar -C ${SCRIPTDIR}/utils/keycloak --numeric-owner --owner=0 --group=0 -cf - kc_postUp.sh | ${OCI_AGENT} cp - ${NAMESPACE_COMMON}-auth:/realm/
      if [ "${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}x" == "truex" ]
      then
        KC_TEST_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN}
        KC_TEST_ADMIN_PWD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD}
      else
        KC_TEST_ADMIN=${ZAIOPS_KEYCLOAK_ADMIN}
        KC_TEST_ADMIN_PWD=${ZAIOPS_KEYCLOAK_ADMIN_PASS}
      fi
      ${OCI_AGENT} exec ${NAMESPACE_COMMON}-auth bash -c "/realm/kc_postUp.sh ${KC_TEST_ADMIN} ${EXTERNAL_GATEWAY_HOST} ${ZAIOPS_KEYCLOAK_IP} ${IZOA_GATEWAY_PORT} ${ZAIOPS_KEYCLOAK_PORT} ${KC_TEST_ADMIN_PWD} ${ZAIOPS_ZOASVC_PASS} ${ZAIOPS_KC_CONTEXT_ROOT}"
    fi
  fi
  # Allow features to run "post-up" functions to handle feature-specific needs
  for FEATURE in ${INSTALLED_FEATURES}
  do
    declare -F postUp_${FEATURE} >/dev/null && postUp_${FEATURE} $@
  done
}

down() {
  if [ $# != 0 ]
  then
    logToStdout "${WARNMSG}'down' command does not support specific services. Remove any parameters after 'down'."
    exit
  fi
  ${DOCKER_COMPOSE} down
}
logs() {
  mkdir -p logs
  if [ $# -eq 0 ]
  then
    ${DOCKER_COMPOSE} logs --no-color 2>&1 | tee -a logs/all-services.stdout
  elif [ $# -eq 1 ]
  then
    if [ $1 = "-f" ]
    then
      ${DOCKER_COMPOSE} logs $1 --no-color 2>&1 | tee -a logs/all-services.stdout
    else
      serviceCheck $1
      ${DOCKER_COMPOSE} logs --no-color $1 2>&1 | tee -a logs/${1}.stdout
    fi
  elif [ $# -eq 2 ] && [ $1 = "-f" ]
  then
    serviceCheck $2
    ${DOCKER_COMPOSE} logs $1 --no-color $2 2>&1 | tee -a logs/${2}.stdout
  elif [ $# -gt 1 ]
  then
    logToStdout "${ERRORMSG}Cannot specify multiple services to log. Please specify one or none."
    exit
  fi
}
ps() {
  serviceCheck $@
  ${DOCKER_COMPOSE} ps $@
}
start() {
  serviceCheck $@
  ${DOCKER_COMPOSE} start $@
}
stop() {
  serviceCheck $@
  ${DOCKER_COMPOSE} stop $@
}
restart() {
  serviceCheck $@
  ${DOCKER_COMPOSE} restart $@
}
serviceCheck(){
  shopt -s extglob
  for service in "$@"
  do
    case "$service" in
    ${SVC_LIST} ) : ;;
    *)
    logToStdout "${ERRORMSG}No such service: $service"
    echo "List of available services:"
    OLDIFS=${IFS}
    IFS='|'
    for ENTRY in ${SVC_LIST_TRIMMED}
    do
      if [ "${ENTRY}x" != "x" ]
      then
        echo "  ${ENTRY}"
      fi
    done
    IFS=${OLDIFS}
    exit 1
    ;;
    esac
  done
  shopt -u extglob
}

gather(){
  mkdir -p logs/support
  OLDIFS=${IFS}
  IFS='|'
  echo "Collecting service logs..."
  for ENTRY in ${SVC_LIST_TRIMMED}
  do
    if [ "${ENTRY}x" != "x" ]
    then
      IFS=${OLDIFS}
      ${DOCKER_COMPOSE} logs --no-color ${ENTRY} >> logs/${ENTRY}.stdout 2>&1
    fi
  done
  IFS=${OLDIFS}

  echo "Collecting Kafka information..."
  kafka-topics --list > logs/support/kafka-topics.log
  kafka-consumer-groups --list > logs/support/kafka-consumer-groups.log
  for GROUP in `kafka-consumer-groups --list`
  do
    kafka-consumer-groups --describe --group ${GROUP} >> logs/support/kafka-consumer-groups.log
  done

  echo "Collecting Docker image information..."
  inspect-images > logs/support/inspect-images.log
  echo "Collecting environment information..."
  free > logs/support/free.log
  df -k > logs/support/df.log
  echo "Docker environment information" > logs/support/dockerInfo.log
  echo "----------------------------------------" >> logs/support/dockerInfo.log
  echo "'docker info' output:" >> logs/support/dockerInfo.log
  ${OCI_AGENT} info >> logs/support/dockerInfo.log
  echo "----------------------------------------" >> logs/support/dockerInfo.log
  echo "docker file system space report:" >> logs/support/dockerInfo.log
  df -kh `${OCI_AGENT} info -f '{{ .DockerRootDir }}'` >> logs/support/dockerInfo.log
  echo "----------------------------------------" >> logs/support/dockerInfo.log
  echo "'docker version' output:" >> logs/support/dockerInfo.log
  ${OCI_AGENT} version >> logs/support/dockerInfo.log
  echo "----------------------------------------" >> logs/support/dockerInfo.log
  echo "'docker compose version' output:" >> logs/support/dockerInfo.log
  docker compose version >> logs/support/dockerInfo.log
  cat /etc/*-release > logs/support/linux.log
  uname -a > logs/support/uname.log
  ulimit -a > logs/support/ulimit.log
  ps > logs/support/ps.log

  tar czf support_${RUNDATE}.tar.gz logs *docker-compose*.yml ${NEW_CONFIG} ${IBM_CONFIG}
  echo ""
  echo "Must-gather information collected in ${BASEDIR}/support_${RUNDATE}.tar.gz"
  echo ""
}

purge(){
  if [ "${1}" == "SILENT" ]
  then
    PROCEED="Y"
  fi
  if [ "${PROCEED}" != "Y" ]
  then
    echo ""
    echo "This action will shut down and remove all running IBM Z AIOps containers"
    echo "     and delete the data volumes associated with them."
    echo ""
    echo "Are you sure you want to continue? (y/N)"
    read -e PROCEED
    PROCEED=${PROCEED:-"n"}   # accept no input as "NO"
  fi
  if [ "${PROCEED}" = "Y" ] || [ "${PROCEED}" = "y" ]
  then
    logToStdout "${INFOMSG}Shutting down and removing running IBM Z AIOps containers..."
    down
    echo ""
    logToStdout "${INFOMSG}Purging IBM Z AIOps data volumes..."
    for VOL in `docker volume ls -q`
    do
      if [[ ${VOL} =~ ${COMPOSE_PROJECT_NAME}.* ]]
      then
        docker volume rm ${VOL}
      fi
    done
    docker volume rm zaiops_shared
  else
    logToStdout "${INFOMSG}Not purging IBM Z AIOps data volumes as requested."
  fi
  echo ""
}

isContainerRunning(){
  serviceCheck $1
  if [ -z `${DOCKER_COMPOSE} ps -q $1` ] || [ -z `docker ps -q --no-trunc | grep $(${DOCKER_COMPOSE} ps -q $1)` ]; then
    echo "Container $1 is not running."
    false
  else
    true
  fi
}

kafka-topics(){
  if (isContainerRunning kafkabroker)
  then
    docker exec ${NAMESPACE_COMMON}-kafkabroker /opt/kafka/bin/kafka-topics.sh --bootstrap-server kafkabroker:19092 $@
  fi
}

kafka-console-consumer(){
  if (isContainerRunning kafkabroker)
  then
    docker exec ${NAMESPACE_COMMON}-kafkabroker /opt/kafka/bin/kafka-console-consumer.sh --bootstrap-server kafkabroker:19092 $@
  fi
}

kafka-consumer-groups(){
  if (isContainerRunning kafkabroker)
  then
    docker exec ${NAMESPACE_COMMON}-kafkabroker /opt/kafka/bin/kafka-consumer-groups.sh --bootstrap-server kafkabroker:19092 $@
  fi
}

kafka-prune(){
  PRODUCT=$1
  if [ "${PRODUCT}x" = "x" ]
  then
    echo "Missing parameter: Either ZAA or ZLDA or ZDiscovery must be specified."
    exit 1
  fi
  if (isContainerRunning kafkabroker)
  then
    ${SCRIPTDIR}/utils/kafka/kafkaPrune.sh -p $@
  fi
}

seed-ldap(){
  ${SCRIPTDIR}/utils/keycloak/loadKCIds.sh ${1}
}

update-keycloak-password(){
  ${SCRIPTDIR}/utils/keycloak/updateKCpassword.sh ${1}
}

set-log-level(){
  SLL_ENABLED="gateway"
  for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
  do
    eval TEMPVAR=\"\$${UFEATURE}_SETLOGLEVEL\"
    SLL_ENABLED+=" ${TEMPVAR}"
  done
  echo ""
  echo "Services supported for this operation are: ${SLL_ENABLED}"
  echo -n "Specify a service:  "
  read -e service
  serviceToContainer $service
  echo ""

  echo "Log levels supported for this operation are: error, warn, info, debug, trace"
  echo -n "Specify a log level:  "
  read -e logLevel
  if [ -z "$logLevel" -a "$logLevel" == "" ]; then
          echo "Specify the desired log level."
          exit 1
  fi
  levelCheck $logLevel

  if [[ "${#package[@]}" -gt 1 ]]; then
    echo ""
    selectPackage
  fi

  docker exec $containerName $path $package $logLevel $xml

  echo ""
}

serviceToContainer(){
  if [ -z "$1" -a "$1" == "" ]; then
          echo "Specify the service to work with."
          exit 1
  fi
  serviceCheck $1
  path="/usr/local/bin/changeLogLevel.sh"
  if [[ "$1" == "kafkacontroller" ]]; then
      containerName="${NAMESPACE_COMMON}-kafkacontroller"
  elif [[ "$1" == "kafkabroker" ]]; then
      containerName="${NAMESPACE_COMMON}-kafkabroker"
  elif [[ "$1" == "gateway" ]]; then
      containerName="${NAMESPACE_COMMON}-gateway"
      package="com.ibm.zsystem.zmanaged.piGateway"
  elif [[ "$1" == "auth" ]]; then
      containerName="${NAMESPACE_COMMON}-auth"
  elif [[ "$1" == "discovery" ]]; then
      containerName="${NAMESPACE_COMMON}-service-discovery"
  else
    for FEATURE in ${INSTALLED_FEATURES}
    do
      if [ -f ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh ]
      then
        . ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh serviceToContainer $1
      fi
    done
  fi
}

# If no command provided, show help; otherwise, run command with arguments
if [ $# -eq 0 ]
then
  help
  exit 0
else
  shopt -s extglob
  case "$1" in
  ${CMD_LIST} ) : ;;
  *)
    logToStdout "${ERRORMSG}Unrecognized command: $1"
    help
    exit 1
    ;;
  esac
  shopt -u extglob
  "$@"
fi
