#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2024
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            loadKCIds_k8s.sh
#
# Description:     IBM Z Data Analytics platform bulk load ids
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Script used to initialize RACF ids for IBM Z Data Analytics
#                  Analytics platform
#
# Syntax:          loadKCIds_k8s.sh
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

SCRIPTDIR="$( cd "$( dirname "$0" )/../.." && pwd )"
SCRIPTNAME="$( basename "$0" )"
BASEDIR=$( dirname ${SCRIPTDIR} )
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
DELETE_NETWORK=false

. ${SCRIPTDIR}/utils/common_functions_k8s.sh

if [ "${SCRIPTNAME}" == "loadKCIds_k8s.sh" ]
then
  OCI_AGENT=kubectl
  DISPLAY_NAME=Kubernetes
elif [ "${SCRIPTNAME}" == "loadKCIds_oc.sh" ]
then
  OCI_AGENT=oc
  DISPLAY_NAME="OpenShift Container Platform"
else
  logToStdout "${ERRORMSG}Invalid invocation: ${SCRIPTNAME}. Unable to proceed."
  exit 1
fi

usage() {
  echo ""
  echo "Usage:"
  echo "  loadKCIds_k8s.sh [force]     to bulk load user IDs into Keycloak"
  echo ""
}

checkService() {
  SERVICE_NAME=${1}
  RESPONSE=$( ${OCI_AGENT} get pods --namespace ${NAMESPACE} | grep ${SERVICE_NAME} ; echo _${PIPESTATUS[*]} )
  TRIMMED_RESPONSE=$( echo ${RESPONSE} | tr -d '\n' | tr ' ' '^' )
  IFS='_' read -r -a STATUSARRAY <<< "${TRIMMED_RESPONSE}"
  CLEANSTATUS=${STATUSARRAY[0]}
  IFS='^' read -r -a RCS <<< "${STATUSARRAY[1]}"
  if [ ${RCS[1]} -ne 0 ]
  then
    echo "No pods for Deployment ${SERVICE_NAME} exist."
    SERVICE_STATUS=DOWN
  else
    DETAIL=$( echo "${CLEANSTATUS}" | awk -F'^' '{ print $3 }' )
    echo "${DETAIL}" | grep -q Running
    if [ $? -eq 0 ]
    then
      echo "Deployment ${SERVICE_NAME} has running pods."
      SERVICE_STATUS=UP
    else
      echo "${DETAIL}" | grep -q Terminating
      if [ $? -eq 0 ]
      then
        echo "Deployment ${SERVICE_NAME} has terminating pods."
        SERVICE_STATUS=DOWN
      else
        echo "Deployment ${SERVICE_NAME} is in an undetermined state."
        SERVICE_STATUS=UNSTABLE
      fi
    fi
  fi
}

ARG=${1}
echo ""
echo "Provide the full path to a file containing user IDs you wish to load and their associated authorization groups:"
while [[ -z "${ZAIOPS_RACF_LDAP_IDS}" ]]
do
  read -e ZAIOPS_RACF_LDAP_IDS
done

if [ -s ${ZAIOPS_RACF_LDAP_IDS} ] && [ -r ${ZAIOPS_RACF_LDAP_IDS} ]
then
  set -a
  . ${BASEDIR}/${NEW_CONFIG}
  . ${BASEDIR}/${IBM_CONFIG}
  set +a
  if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
  then
    export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
  fi
  if [ "${SUBDOMAIN}x" == "x" ]
  then
    AUTH_HOST=auth
  else
    AUTH_HOST=auth.${SUBDOMAIN}
  fi
  COMMON_CONTAINER_PREFIX=zoa-

  # Check Keycloak status; bring it up if needed
  checkService ${COMMON_CONTAINER_PREFIX}auth
  if [ "${SERVICE_STATUS}" = "DOWN" ]
  then
    cd ${BASEDIR}

    # Bring up Keycloak in standard mode
    echo "Starting authentication service in standard mode..."
    eval "echo \"$(<${BASEDIR}/k8s-config/auth.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
    waitForPod ${COMMON_CONTAINER_PREFIX}auth
    if [ "${PODREADY}" == "TRUE" ]
    then
      cd ${BASEDIR}
      printf "%-50s %s " "Waiting for authentication service to be ready" "..."
      ${SCRIPTDIR}/k8sManageZoa.sh wait-for-service auth.zoa 8443
      if [ $? -eq 1 ]
      then
        shutdownKeycloak standard
        exit 1
      fi
    fi
  elif [ "${SERVICE_STATUS}" = "UNSTABLE" ]
  then
    logToStdout "${WARNMSG}Service ${COMMON_CONTAINER_PREFIX}auth is not ready. Unable to proceed."
    exit 1
  fi

  # Move files into the container
  RACF_LDAP_IDS_BASE=$( basename ${ZAIOPS_RACF_LDAP_IDS} )
  RACF_LDAP_IDS_DIR=$( dirname ${ZAIOPS_RACF_LDAP_IDS} )
  chmod 755 ${SCRIPTDIR}/utils/keycloak/*.sh
  POD=$( ${OCI_AGENT} get pods --namespace ${NAMESPACE} | grep ${COMMON_CONTAINER_PREFIX}auth | grep Running | awk '{ print $1 }' )
  dimDisplay
  ${OCI_AGENT} cp --namespace ${NAMESPACE} ${SCRIPTDIR}/utils/keycloak/zoa_load_ids.sh ${POD}:/realm/
  ${OCI_AGENT} cp --namespace ${NAMESPACE} ${RACF_LDAP_IDS_DIR}/${RACF_LDAP_IDS_BASE} ${POD}:/realm/
  resetDisplay

  # Execute command in container
  ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}auth -- bash -c "/realm/zoa_load_ids.sh ${ZAIOPS_KEYCLOAK_ADMIN} ${ZAIOPS_KEYCLOAK_ADMIN_PASS} ${ZAIOPS_ZOASVC_PASS} ${ZAIOPS_KEYCLOAK_HOST} ${ZAIOPS_KEYCLOAK_PORT} ${ZAIOPS_KC_CONTEXT_ROOT} /realm/${RACF_LDAP_IDS_BASE} ${ARG}"

  echo "Cleaning up container"
  ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}auth -- bash -c "cd /realm && rm -f zoa_load_ids.sh && rm -f ${RACF_LDAP_IDS_BASE}"

  # If the service was down before, shut it down now
  if [ "${KC_SERVICE_STATUS}" = "DOWN" ]
  then
    shutdownKeycloak standard
  fi

  echo "Done"
else
  echo "${ZAIOPS_RACF_LDAP_IDS} is not found."
  exit 2
fi
