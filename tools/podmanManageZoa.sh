#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2022, 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            podmanManageZoa.sh
#
# Description:     IBM Z AIOps Common Services management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         <PERSON><PERSON><PERSON> used to manage IBM Z AIOps Podman-based services
#
# Syntax:          podmanManageZoa.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------
SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
cd ${BASEDIR}
RUNDATE=$( date +"%Y-%m-%d_%H.%M.%S" )
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
PRODUCT_INVENTORY=${BASEDIR}/product.inventory
PICLI_DEBUG=""
ARCH=$( uname -m )
which host > /dev/null 2>&1
if [ ! $? = 0 ]
then
  hashost=false 
else
  hashost=true
fi
NAMESPACE_COMMON=zoa
SCRIPT_PREFIX=podman
# Set default/fallback logging driver
LOGDRIVER="json-file"
# Set default logging mode; for Docker only
LOGMODE="non-blocking"

umask 0022

set -a
. ${NEW_CONFIG}
. ${IBM_CONFIG}
if [ -f ${PRODUCT_INVENTORY} ]
then
  . ${PRODUCT_INVENTORY}
fi
set +a
if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
then
  export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
fi
if [ "${OCI_AGENT}" = "docker" ]
then
  DISPLAY_NAME=Docker
  if [ ${OCI_VERSION} -ge 20 ]
  then
    DNS_TYPE=modern
  else
    DNS_TYPE=legacy
  fi
  if [ "${LOGGING_DRIVER}" = "json-file" ] || [ "${LOGGING_DRIVER}" = "local" ] || [ "${LOGGING_DRIVER}" = "journald" ]
  then
    LOGDRIVER=${LOGGING_DRIVER}
  fi
  if [ "${LOGGING_MODE}" = "non-blocking" ] || [ "${LOGGING_MODE}" = "blocking" ]
  then
    LOGMODE=${LOGGING_MODE}
  fi
  LOGOPTS=(--log-driver ${LOGDRIVER} --log-opt mode=${LOGMODE})
elif [ "${OCI_AGENT}" = "podman" ]
then
  DISPLAY_NAME=Podman
  DNS_TYPE=legacy
  if [ "${LOGGING_DRIVER}" = "json-file" ] || [ "${LOGGING_DRIVER}" = "k8s-file" ] || [ "${LOGGING_DRIVER}" = "journald" ]
  then
    LOGDRIVER=${LOGGING_DRIVER}
  fi
  LOGOPTS=(--log-driver ${LOGDRIVER})
fi

if [ "${DNS_TYPE}" == "legacy" ]
then
  COMMON_CONTAINER_PREFIX=""
elif [ "${DNS_TYPE}" == "modern" ]
then
  COMMON_CONTAINER_PREFIX=${NAMESPACE_COMMON}-
fi

KEY_PASS=$( echo "${ZAIOPS_ZOASVC_PASS}" | base64 -d )
API_PASS=$( echo "${API_PWD}" | base64 -d )
API_USER=$( echo "${API_USR}" | base64 -d )
COMMON_SVC_LIST='@(gateway|auth|discovery|kafkacontroller|kafkabroker)'
COMMON_CMD_LIST='@(up|down|logs|ps|help|start|stop|restart|purge|gather|inspect-images|kafka-topics|kafka-console-consumer|kafka-consumer-groups|kafka-prune|kafka-debug-logging|config-certificates|move-data|backup-data|restore-data|seed-ldap|get-log-level|set-log-level|check-kc-readiness|wait-for-service)'

. ${SCRIPTDIR}/utils/common_functions.sh
if [ "${ARCH}" != "s390x" ] && [ "${ARCH}" != "x86_64" ]
then
  logToStdout "${ERRORMSG}Unsupported architecture ${ARCH}. Unable to proceed."
  exit 1
fi
for FEATURE in ${INSTALLED_FEATURES}
do
  if [ -f ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh ]
  then
    . ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh
  fi
done

CMD_LIST_TEMP="${COMMON_CMD_LIST}"
for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
do
  eval TEMPVAR=\"\$${UFEATURE}_CMD_LIST\"
  CMD_LIST_TEMP+="|${TEMPVAR}"
done
CMD_LIST='@('$( echo ${CMD_LIST_TEMP} |tr -d '()@' )')'

SVC_LIST_TEMP="${COMMON_SVC_LIST}"
for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
do
  eval TEMPVAR=\"\$${UFEATURE}_SVC_LIST\"
  SVC_LIST_TEMP+="|${TEMPVAR}"
done
SVC_LIST='@('$( echo ${SVC_LIST_TEMP} |tr -d '()@' )')'
SVC_LIST_TRIMMED=$( echo ${SVC_LIST} | tr -d '@()' )
SVC_LIST_SPACES=$( echo ${SVC_LIST_TRIMMED} | tr '|' ' ' )

gatewayUp() {
  if(isContainerRunning gateway)
  then
    echo "Container 'gateway' is already running."
  else
    echo "Will start 'gateway' service."
    # Calculate effective gateway rate limit
    DAC=${DISCOVERY_AGENT_COUNT:-0}
    GRM=${ZAIOPS_GATEWAY_REQUESTS_PER_MIN:-80}
    ZAIOPS_TOTAL_RATE_LIMIT=$( awk -v dac=${DAC} -v grm=${GRM} 'BEGIN { rounded = sprintf("%.0f", (dac+1)*grm*125/100); print rounded }' )
    # Start gateway container
    ${OCI_AGENT} run --restart=always \
      --name ${COMMON_CONTAINER_PREFIX}gateway -d \
      --security-opt no-new-privileges \
      ${LOGOPTS[@]} \
      -e EXTERNAL_GATEWAY_HOST=${EXTERNAL_GATEWAY_HOST} \
      -e IZOA_GATEWAY_PORT=${IZOA_GATEWAY_PORT} \
      -e KC_INTERNAL_HOST=${KC_INTERNAL_HOST} \
      -e PI_FRAMEWORK_ROUTE=${PI_FRAMEWORK_ROUTE} \
      -e RATELIMIT_LIMIT=${ZAIOPS_TOTAL_RATE_LIMIT} \
      -e SERVER_HTTP2_ENABLED=${ENABLE_HTTP2} \
      -e SSL_DEBUG=${SSL_DEBUG} \
      -e ZAIOPS_GW_PASS=${ZAIOPS_ZOASVC_PASS} \
      -e ZAIOPS_KC_CONTEXT_ROOT=${ZAIOPS_KC_CONTEXT_ROOT} \
      -e ZAIOPS_KC_REALM=${ZAIOPS_KC_REALM} \
      -e ZRDDS_API_HOST=${ZRDDS_API_HOST:-zrddsapi} \
      -e ZRDDS_CORE_HOST=${ZRDDS_CORE_HOST:-zrddsapi} \
      -e TOPOLOGY_UI=${TOPOLOGY_UI:-zrddsui} \
      -e ZRDDS_KB_HOST=${ZRDDS_KB_HOST:-kafkabridge} \
      -u ${ZOA_UID}:0 \
      -v zaiops_shared:/shared:ro \
      -p ${IZOA_GATEWAY_PORT}:8085 \
      -h gateway --network ${COMPOSE_PROJECT_NAME}_zaiops \
      ibm-zaiops/${NAMESPACE_COMMON}-gateway:${TAG}-${ARCH} > /dev/null
    sleep 5
  fi
}

authUp() {
  if(isContainerRunning auth)
  then
    echo "Container 'auth' is already running."
  else
    echo "Will start 'auth' service."
    ${OCI_AGENT} run --restart=always \
      --name ${COMMON_CONTAINER_PREFIX}auth -d \
      --security-opt no-new-privileges \
      ${LOGOPTS[@]} \
      -e EXTERNAL_GATEWAY_HOST=${EXTERNAL_GATEWAY_HOST} \
      -e HOSTNAME_STRICT=${HOSTNAME_STRICT} \
      -e IZOA_GATEWAY_PORT=${IZOA_GATEWAY_PORT} \
      -e KC_FORCE_EXTERNAL_HOSTNAME=${KC_FORCE_EXTERNAL_HOSTNAME} \
      -e KC_TRUSTSTORE_PATHS=${KC_TRUSTSTORE_PATHS} \
      -e SSL_DEBUG=${SSL_DEBUG} \
      -e ZAIOPS_KC_BOOTSTRAP_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN} \
      -e ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED} \
      -e ZAIOPS_KC_BOOTSTRAP_PASSWORD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD} \
      -e ZAIOPS_KC_CACHE_MODE=${ZAIOPS_KC_CACHE_MODE} \
      -e ZAIOPS_KC_CONTEXT_ROOT=${ZAIOPS_KC_CONTEXT_ROOT} \
      -e ZAIOPS_KC_DB=${ZAIOPS_KC_DB} \
      -e ZAIOPS_KC_DB_PWD=${ZAIOPS_KC_DB_PWD} \
      -e ZAIOPS_KC_DB_URL=${ZAIOPS_KC_DB_URL} \
      -e ZAIOPS_KC_DB_USER=${ZAIOPS_KC_DB_USER} \
      -e ZAIOPS_KC_HTTPS_PORT=${ZAIOPS_KC_HTTPS_PORT} \
      -e ZAIOPS_KC_KEYSTORE_FILE=${ZAIOPS_KC_KEYSTORE_FILE} \
      -e ZAIOPS_KC_KEYSTORE_TYPE=${ZAIOPS_KC_KEYSTORE_TYPE} \
      -e ZAIOPS_KC_PASS=${ZAIOPS_ZOASVC_PASS} \
      -e ZAIOPS_KC_TRUSTSTORE_FILE=${ZAIOPS_KC_TRUSTSTORE_FILE} \
      -e ZAIOPS_KC_TRUSTSTORE_TYPE=${ZAIOPS_KC_TRUSTSTORE_TYPE} \
      -e ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED=${ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED:-false} \
      -e ZAIOPS_KEYCLOAK_IP=${ZAIOPS_KEYCLOAK_IP} \
      -e ZAIOPS_KEYCLOAK_LOGLEVEL=${ZAIOPS_KEYCLOAK_LOGLEVEL:-INFO} \
      -e ZAIOPS_KEYCLOAK_METRICAPI_ENABLED=${ZAIOPS_KEYCLOAK_METRICAPI_ENABLED:-false} \
      -e ZAIOPS_KEYCLOAK_PORT=${ZAIOPS_KEYCLOAK_PORT} \
      -e ZAIOPS_PROXY_HEADERS=${ZAIOPS_PROXY_HEADERS} \
      -e ZAIOPS_TLS_VERSION=${ZAIOPS_TLS_VERSION} \
      -u ${ZOA_UID}:0 \
      -v ${COMPOSE_PROJECT_NAME}_zaiops_keycloak:/opt/keycloak/data/h2/ \
      -v zaiops_shared:/shared:ro \
      -h auth --network ${COMPOSE_PROJECT_NAME}_zaiops \
      ibm-zaiops/${NAMESPACE_COMMON}-auth:${TAG}-${ARCH} > /dev/null
    sleep 5
  fi
}

discoveryUp() {
  if(isContainerRunning discovery)
  then
    echo "Container 'discovery' is already running."
  else
    echo "Will start 'discovery' service."
    ${OCI_AGENT} run --restart=always \
      --name ${COMMON_CONTAINER_PREFIX}discovery -d \
      --security-opt no-new-privileges \
      ${LOGOPTS[@]} \
      -e JAVA_OPTS=-DEUREKA_URI=http://localhost:8761/eureka \
      -u ${ZOA_UID}:0 \
      -h discovery --network ${COMPOSE_PROJECT_NAME}_zaiops \
      ibm-zaiops/${NAMESPACE_COMMON}-service-discovery:${TAG}-${ARCH} > /dev/null
    sleep 5
  fi
}

kafkacontrollerUp() {
  if(isContainerRunning kafkacontroller)
  then
    echo "Container 'kafkacontroller' is already running."
  else
    echo "Will start 'kafkacontroller' service."
    ${OCI_AGENT} run --restart=always \
      --name ${COMMON_CONTAINER_PREFIX}kafkacontroller -d \
      --security-opt no-new-privileges \
      ${LOGOPTS[@]} \
      -e KAFKA_NODE_ROLE=controller \
      -e ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS} \
      -e SSL_DEBUG=${SSL_DEBUG} \
      -e KAFKA_HEAP_OPTS="-Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g" \
      -e KAFKA_OPTS="-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache" \
      -u ${ZOA_UID}:0 \
      -v ${COMPOSE_PROJECT_NAME}_zaiops_kafkabroker:/opt/kafka/data \
      -v zaiops_shared:/shared:ro \
      -h kafkacontroller --network ${COMPOSE_PROJECT_NAME}_zaiops \
      ibm-zaiops/${NAMESPACE_COMMON}-kafkabroker:${TAG}-${ARCH} > /dev/null
    sleep 5
  fi
}

kafkabrokerUp() {
  if(isContainerRunning kafkabroker)
  then
    echo "Container 'kafkabroker' is already running."
  else
    echo "Will start 'kafkabroker' service."
    ${OCI_AGENT} run --restart=always \
      --name ${COMMON_CONTAINER_PREFIX}kafkabroker -d \
      --security-opt no-new-privileges \
      ${LOGOPTS[@]} \
      -e KAFKA_NODE_ROLE=broker \
      -e ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST} \
      -e ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT} \
      -e ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT} \
      -e ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS} \
      -e ZAIOPS_KAFKA_RETENTION_HOURS=${ZAIOPS_KAFKA_RETENTION_HOURS} \
      -e SSL_DEBUG=${SSL_DEBUG} \
      -e KAFKA_HEAP_OPTS="-Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g" \
      -e KAFKA_OPTS="-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache" \
      -u ${ZOA_UID}:0 \
      -v ${COMPOSE_PROJECT_NAME}_zaiops_kafkabroker:/opt/kafka/data \
      -v zaiops_shared:/shared:ro \
      -p ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}:9092 \
      -p ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}:9093 \
      -h kafkabroker --network ${COMPOSE_PROJECT_NAME}_zaiops \
      ibm-zaiops/${NAMESPACE_COMMON}-kafkabroker:${TAG}-${ARCH} > /dev/null
    sleep 5
  fi
}

startupRemainder() {
  just_zdap=$( echo "$INSTALLED_FEATURES" | sed "s/zdap//g; s/zoacommon//g" | tr -d '[:space:]' )
  if [ "${just_zdap}x" == "x" ]
  then
    SVC_LIST_SPACES=$( echo "$SVC_LIST_SPACES" | sed "s/piserver//g" )
  fi
  for SVC in ${SVC_LIST_SPACES}
  do
    # Strip dashes off service names
    SVC=$( echo ${SVC} | tr -d "-" )
    ${SVC}Up &
  done
  wait
  for SVC in ${SVC_LIST_SPACES}
  do
    serviceToContainer ${SVC}
  done
}

up() {
  DO_KEYCLOAK_POSTCONFIG=false
  # Check whether network exists; if not, create it
  ${OCI_AGENT} network ls | awk '{ print $2 }' | grep -q ^${COMPOSE_PROJECT_NAME}_zaiops$
  if [ $? -ne 0 ]
  then
    ${OCI_AGENT} network create ${COMPOSE_PROJECT_NAME}_zaiops
  fi
  if [ $# == 0 ]
  then # bring up all services
    DO_KEYCLOAK_POSTCONFIG=true
    # Do two-step startup when all services are started at once to avoid timeouts / resource competition
    discoveryUp
    gatewayUp
    # Wait for gateway service to come up
    printf "%-50s %s" "Waiting for gateway service to come up" "..."
    COUNT=0
    while :
    do
      echo -n .
      ${OCI_AGENT} logs ${COMMON_CONTAINER_PREFIX}gateway | grep -q "Started PiGatewayApplication" 2>/dev/null
      if [ $? -eq 0 ]
      then
        echo -n " "
        echo "successful after ${COUNT} seconds."
        break
      elif [ ${COUNT} -ge ${SERVICE_TIMEOUT} ]
      then
        echo -n " "
        echo "Gateway service still not ready after ${COUNT} seconds; giving up."
        logToStdout "${ERRORMSG}Service status:"
        echo "--------------------------------------------------------------------------------"
        ${SCRIPTDIR}/podmanManageZoa.sh ps
        echo "--------------------------------------------------------------------------------"
        logToStdout "${ERRORMSG}Last ${TS_LOG_LINES} lines of service log output:"
        echo "--------------------------------------------------------------------------------"
        ${OCI_AGENT} logs ${COMMON_CONTAINER_PREFIX}gateway | tail -n ${TS_LOG_LINES}
        echo "--------------------------------------------------------------------------------"
        exit 1
      else
        sleep 5
        COUNT=$(( ${COUNT} + 5 ))
      fi
    done
    authUp
    # Check for authentication service
    printf "%-50s %s " "Waiting for authentication service to be ready" "..."
    wait-for-service auth 8443
    kafkacontrollerUp
    kafkabrokerUp
    # Check for Kafka service
    printf "%-50s %s " "Waiting for Kafka broker service to be ready" "..."
    wait-for-service kafkabroker 19092
    # Bring up the rest of the services
    startupRemainder
  else # bring up requested services
    echo "$@" | grep -q auth
    if [ $? -eq 0 ]
    then
      DO_KEYCLOAK_POSTCONFIG=true
    fi
    serviceCheck $@
    for SVC in $@
    do
      # Strip dashes off service names
      SVC=$( echo ${SVC} | tr -d "-" )
      ${SVC}Up &
    done
    wait
    for SVC in $@
    do
      serviceToContainer ${SVC}
    done
  fi
  # Handle post-startup actions as needed
  # A. Keycloak updates in case of port changes
  if [ "${DO_KEYCLOAK_POSTCONFIG}" == "true" ]
  then
    if(isContainerRunning auth)
    then
      printf "%-50s %s " "Waiting for authentication service to be ready" "..."
      wait-for-service auth 8443
      chmod 755 ${SCRIPTDIR}/utils/keycloak/*.sh
      tar -C ${SCRIPTDIR}/utils/keycloak --numeric-owner --owner=0 --group=0 -cf - kc_postUp.sh | ${OCI_AGENT} cp - ${COMMON_CONTAINER_PREFIX}auth:/realm/
      if [ "${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}x" == "truex" ]
      then
        KC_TEST_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN}
        KC_TEST_ADMIN_PWD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD}
      else
        KC_TEST_ADMIN=${ZAIOPS_KEYCLOAK_ADMIN}
        KC_TEST_ADMIN_PWD=${ZAIOPS_KEYCLOAK_ADMIN_PASS}
      fi
      ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}auth bash -c "/realm/kc_postUp.sh ${KC_TEST_ADMIN} ${EXTERNAL_GATEWAY_HOST} ${ZAIOPS_KEYCLOAK_IP} ${IZOA_GATEWAY_PORT} ${ZAIOPS_KEYCLOAK_PORT} ${KC_TEST_ADMIN_PWD} ${ZAIOPS_ZOASVC_PASS} ${ZAIOPS_KC_CONTEXT_ROOT}"
    fi
  fi
  # Allow features to run "post-up" functions to handle feature-specific needs
  for FEATURE in ${INSTALLED_FEATURES}
  do
    declare -F postUp_${FEATURE} >/dev/null && postUp_${FEATURE} $@
  done
}

down() {
  if [ $# -eq 0 ]
  then # bring down all services
    echo "Stopping services..."
    for SVC in ${SVC_LIST_SPACES}
    do
      serviceToContainer ${SVC}
      if [ "${OCI_AGENT}" == "podman" ]
      then
        ${OCI_AGENT} stop -i -t 60 ${containerName} &
      else
        ${OCI_AGENT} stop -t 60 ${containerName} &
      fi
    done
    wait
    echo "Removing services..."
    for SVC in ${SVC_LIST_SPACES}
    do
      serviceToContainer ${SVC}
      if [ "${OCI_AGENT}" == "podman" ]
      then
        ${OCI_AGENT} rm -i ${containerName} &
      else
        ${OCI_AGENT} rm ${containerName} &
      fi
    done
    wait
  else # bring down requested services
    serviceCheck $@
    echo "Stopping services..."
    for SVC in $@
    do
      serviceToContainer ${SVC}
      ${OCI_AGENT} stop -t 60 ${containerName} &
    done
    wait
    echo "Removing services..."
    for SVC in $@
    do
      serviceToContainer ${SVC}
      ${OCI_AGENT} rm ${containerName} &
    done
    wait
  fi
}

logs() {
  mkdir -p logs
  if [ $# -eq 0 ]
  then
    for SVC in ${SVC_LIST_SPACES}
    do
      serviceToContainer ${SVC}
      ${OCI_AGENT} logs ${containerName} 2>&1 | tee -a logs/all-services.stdout
    done
  elif [ $# -eq 1 ]
  then
    if [ $1 = "-f" ]
    then
      for SVC in ${SVC_LIST_SPACES}
      do
        serviceToContainer ${SVC}
        ALL_CONTAINERS="${ALL_CONTAINERS} ${containerName}"
      done
      ${OCI_AGENT} logs -f ${ALL_IMAGES} 2>&1 | tee -a logs/all-services.stdout
    else
      serviceCheck $1
      serviceToContainer $1
      ${OCI_AGENT} logs ${containerName} 2>&1 | tee -a logs/${1}.stdout
    fi
  elif [ $# -eq 2 ] && [ $1 = "-f" ]
  then
    serviceCheck $2
    serviceToContainer $2
    ${OCI_AGENT} logs -f ${containerName} 2>&1 | tee -a logs/${2}.stdout
  elif [ $# -gt 1 ]
  then
    logToStdout "${ERRORMSG}Cannot specify multiple services to log. Please specify one or none."
    exit
  fi
}

ps() {
  if [ $# -eq 0 ]
  then
    ${OCI_AGENT} ps -a
  else
    serviceCheck $@
    ${OCI_AGENT} ps | grep "CONTAINER ID"
    for SVC in $@
    do
      serviceToContainer ${SVC}
      ${OCI_AGENT} ps | grep ${containerName}
    done
  fi
}

start() {
  if [ $# -eq 0 ]
  then
    echo "Starting services..."
    for SVC in ${SVC_LIST_SPACES}
    do
      serviceToContainer ${SVC}
      echo -n "  "
      ${OCI_AGENT} start ${containerName}
    done
  else
    serviceCheck $@
    echo "Starting services..."
    for SVC in $@
    do
      serviceToContainer ${SVC}
      echo -n "  "
      ${OCI_AGENT} start ${containerName}
    done
  fi
}

stop() {
  if [ $# -eq 0 ]
  then
    echo "Stopping services..."
    for SVC in ${SVC_LIST_SPACES}
    do
      serviceToContainer ${SVC}
      if [ "${OCI_AGENT}" == "podman" ]
      then
        ${OCI_AGENT} stop -i -t 60 ${containerName} &
      else
        ${OCI_AGENT} stop -t 60 ${containerName} &
      fi
    done
    wait
  else
    serviceCheck $@
    echo "Stopping services.."
    for SVC in $@
    do
      serviceToContainer ${SVC}
      ${OCI_AGENT} stop -t 60 ${containerName} &
    done
    wait
  fi
}

restart() {
  if [ $# -eq 0 ]
  then
    echo "Restarting services..."
    for SVC in ${SVC_LIST_SPACES}
    do
      serviceToContainer ${SVC}
      echo -n "  "
      ${OCI_AGENT} restart -t 60 ${containerName} &
    done
    wait
    for SVC in ${SVC_LIST_SPACES}
    do
      serviceToContainer ${SVC}
    done
  else
    serviceCheck $@
    echo "Restarting services..."
    for SVC in $@
    do
      serviceToContainer ${SVC}
      echo -n "  "
      ${OCI_AGENT} restart -t 60 ${containerName} &
    done
    wait
    for SVC in $@
    do
      serviceToContainer ${SVC}
    done
  fi
}

serviceCheck() {
  shopt -s extglob
  for service in "$@"
  do
    case "$service" in
    ${SVC_LIST} ) : ;;
    *)
    logToStdout "${ERRORMSG}No such service: $service"
    echo "List of available services:"
    for ENTRY in ${SVC_LIST_SPACES}
    do
      if [ "${ENTRY}x" != "x" ]
      then
        echo "  ${ENTRY}"
      fi
    done
    exit 1
    ;;
    esac
  done
  shopt -u extglob
}

gather() {
  mkdir -p logs/support
  echo "Collecting service logs..."
  for ENTRY in ${SVC_LIST_SPACES}
  do
    if [ "${ENTRY}x" != "x" ]
    then
      serviceToContainer ${ENTRY}
      ${OCI_AGENT} logs ${containerName} >> logs/${ENTRY}.stdout 2>&1
    fi
  done

  echo "Collecting Kafka information..."
  kafka-topics --list > logs/support/kafka-topics.log
  kafka-consumer-groups --list > logs/support/kafka-consumer-groups.log
  for GROUP in $( kafka-consumer-groups --list )
  do
    kafka-consumer-groups --describe --group ${GROUP} >> logs/support/kafka-consumer-groups.log
  done

  echo "Collecting OCI image information..."
  inspect-images > logs/support/inspect-images.log
  echo "Collecting environment information..."
  free > logs/support/free.log
  df -k > logs/support/df.log
  echo "${DISPLAY_NAME} environment information" > logs/support/${OCI_AGENT}Info.log
  echo "----------------------------------------" >> logs/support/${OCI_AGENT}Info.log
  echo "'${OCI_AGENT} info' output:" >> logs/support/${OCI_AGENT}Info.log
  ${OCI_AGENT} info >> logs/support/${OCI_AGENT}Info.log
  echo "----------------------------------------" >> logs/support/${OCI_AGENT}Info.log
  echo "${OCI_AGENT} file system space report:" >> logs/support/${OCI_AGENT}Info.log
  if [ "${OCI_AGENT}" == "podman" ]
  then
    df -kh $( ${OCI_AGENT} info -f '{{ .Store.RunRoot }}' ) >> logs/support/${OCI_AGENT}Info.log
  elif [ "${OCI_AGENT}" == "docker" ]
  then
    df -kh $( ${OCI_AGENT} info -f '{{ .DockerRootDir }}' ) >> logs/support/${OCI_AGENT}Info.log
  fi
  echo "----------------------------------------" >> logs/support/${OCI_AGENT}Info.log
  echo "'${OCI_AGENT} version' output:" >> logs/support/${OCI_AGENT}Info.log
  ${OCI_AGENT} version >> logs/support/${OCI_AGENT}Info.log
  cat /etc/*-release > logs/support/linux.log
  uname -a > logs/support/uname.log
  ulimit -a > logs/support/ulimit.log
  ps > logs/support/ps.log

  tar czf support_${RUNDATE}.tar.gz logs ${NEW_CONFIG} ${IBM_CONFIG}
  echo ""
  echo "Must-gather information collected in ${BASEDIR}/support_${RUNDATE}.tar.gz"
  echo ""
}

purge() {
  if [ "${1}" == "SILENT" ]
  then
    PROCEED="Y"
  fi
  if [ "${PROCEED}" != "Y" ]
  then
    echo ""
    echo "This action will shut down and remove all running IBM Z AIOps containers"
    echo "     and delete the data volumes associated with them."
    echo ""
    echo "Are you sure you want to continue? (y/N)"
    read -e PROCEED
    PROCEED=${PROCEED:-"n"}   # accept no input as "NO"
  fi
  if [ "${PROCEED}" = "Y" ] || [ "${PROCEED}" = "y" ]
  then
    logToStdout "${INFOMSG}Shutting down and removing running IBM Z AIOps containers..."
    down
    echo ""
    logToStdout "${INFOMSG}Purging IBM Z AIOps data volumes..."
    for VOL in $( ${OCI_AGENT} volume ls -q )
    do
      if [[ ${VOL} =~ ${COMPOSE_PROJECT_NAME}.* ]]
      then
        ${OCI_AGENT} volume rm ${VOL}
      fi
    done
    ${OCI_AGENT} volume rm zaiops_shared
  else
    logToStdout "${INFOMSG}Not purging IBM Z AIOps data volumes as requested."
  fi
  echo ""
}

isContainerRunning() {
  serviceCheck $1
  serviceToContainer $1
  SVC_ID=$( ${OCI_AGENT} inspect --format '{{ .Id }}' ${containerName} 2>/dev/null )
  if [ $? -ne 0 ]
  then
    echo "Container $1 is not running."
    false
  else
    if [ -z $( ${OCI_AGENT} ps -q --no-trunc | grep ${SVC_ID} ) ]
    then
      echo "Container $1 is not running."
      false
    else
      true
    fi
  fi
}

kafka-topics() {
  if(isContainerRunning kafkabroker)
  then
    ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkabroker /opt/kafka/bin/kafka-topics.sh --bootstrap-server kafkabroker:19092 $@
  fi
}

kafka-console-consumer() {
  if(isContainerRunning kafkabroker)
  then
    ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkabroker /opt/kafka/bin/kafka-console-consumer.sh --bootstrap-server kafkabroker:19092 $@
  fi
}

kafka-consumer-groups() {
  if(isContainerRunning kafkabroker)
  then
    ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkabroker /opt/kafka/bin/kafka-consumer-groups.sh --bootstrap-server kafkabroker:19092 $@
  fi
}

kafka-prune() {
  PRODUCT=$1
  if [ "${PRODUCT}x" = "x" ]
  then
    echo "Missing parameter: Either ZAA or ZLDA or ZDiscovery must be specified."
    exit 1
  fi
  if(isContainerRunning kafkabroker)
  then
    ${SCRIPTDIR}/utils/kafka/kafkaPrune.sh -p $@
  fi
}

seed-ldap() {
  ${SCRIPTDIR}/utils/keycloak/loadKCIds.sh ${1}
}

update-keycloak-password(){ 
  ${SCRIPTDIR}/utils/keycloak/updateKCpassword.sh ${1}
}

set-log-level() {
  SLL_ENABLED="gateway"
  for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
  do
    eval TEMPVAR=\"\$${UFEATURE}_SETLOGLEVEL\"
    SLL_ENABLED+=" ${TEMPVAR}"
  done
  echo ""
  echo "Services supported for this operation are: ${SLL_ENABLED}"
  echo -n "Specify a service:  "
  read -e service
  serviceToContainer $service
  echo ""

  echo "Log levels supported for this operation are: error, warn, info, debug, trace"
  echo -n "Specify a log level:  "
  read -e logLevel
  if [ -z "$logLevel" -a "$logLevel" == "" ]; then
          echo "Specify the desired log level."
          exit 1
  fi
  levelCheck $logLevel
  
  if [[ "${#package[@]}" -gt 1 ]]; then
    echo ""
    selectPackage
  fi

  ${OCI_AGENT} exec $containerName $path $package $logLevel $xml

  echo ""
}

serviceToContainer() {
  if [ -z "$1" -a "$1" == "" ]; then
          echo "Specify the service to work with."
          exit 1
  fi
  serviceCheck $1
  path="/usr/local/bin/changeLogLevel.sh"
  if [[ "$1" == "kafkacontroller" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}kafkacontroller"
  elif [[ "$1" == "kafkabroker" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}kafkabroker"
  elif [[ "$1" == "gateway" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}gateway"
      package="com.ibm.zsystem.zmanaged.piGateway"
  elif [[ "$1" == "auth" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}auth"
  elif [[ "$1" == "discovery" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}discovery"
  else
    for FEATURE in ${INSTALLED_FEATURES}
    do 
      if [ -f ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh ]
      then
        . ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh serviceToContainer $1
      fi
    done
  fi
}

# If no command provided, show help; otherwise, run command with arguments
if [ $# -eq 0 ]
then
  help
  exit 0
else
  shopt -s extglob
  case "$1" in
  ${CMD_LIST} ) : ;;
  *)
    logToStdout "${ERRORMSG}Unrecognized command: $1"
    help
    exit 1
    ;;
  esac
  shopt -u extglob
  "$@"
fi
