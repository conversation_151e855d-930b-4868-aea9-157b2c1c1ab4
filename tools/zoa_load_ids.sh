#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2023, 2024
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            zoa_load_ids.sh
#
# Description:     IBM Z Data Analytics platform bulk id initialization
#                  (to be run inside container)
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         <PERSON><PERSON><PERSON> used to initialize LDAP IDs for IBM Z Data Analytics
#                  Analytics platform
#
# Syntax:          zoa_load_ids.sh <keycloak base64 password> <zoa_bulk_userid_file>
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
KEYCLOAK_ADMIN=$1
ZAIOPS_KEYCLOAK_ADMIN_PASS=$2
TRUSTSTORE_PASS=$3
ZAIOPS_KEYCLOAK_HOST=$4
KEYCLOAK_PORT=$5
KC_CONTEXT_ROOT=$6
KEYCLOAK_IDS=$7
FORCE_DIRECT_AUTHC=$8
KEYCLOAK_ADMIN_PASSWORD=$( echo "${ZAIOPS_KEYCLOAK_ADMIN_PASS}" | base64 -d )
TRUSTSTORE_PASS_C=$( echo "${TRUSTSTORE_PASS}" | base64 -d )
KEYCLOAK_HOST_LOWER=$( echo ${ZAIOPS_KEYCLOAK_HOST} | tr '[:upper:]' '[:lower:]' )
TRUSTSTORE=/ssl/zoasvc.ts
SERVER=http://auth:8080
OSTYPE=$( uname )

terminateCliSessions() {
  # Clear any active admin CLI sessions
  ADMIN_CLI_ID=$( /opt/keycloak/bin/kcadm.sh get clients -r master --fields id,clientId --format csv --noquotes  | grep admin-cli | cut -f 1 -d "," )
  SESSION_IDS=$( /opt/keycloak/bin/kcadm.sh get clients/${ADMIN_CLI_ID}/user-sessions -r master --fields id --format csv --noquotes )
  for SESSION_ID in ${SESSION_IDS}
  do
    /opt/keycloak/bin/kcadm.sh delete sessions/${SESSION_ID} -r master
  done
}

if [ "${KEYCLOAK_IDS}x" == "x" ]
then
  echo "ERROR: No id list file supplied."
  exit 1
fi

/opt/keycloak/bin/kcadm.sh config credentials --server ${SERVER}/${KC_CONTEXT_ROOT} --realm master --user ${KEYCLOAK_ADMIN} --password ${KEYCLOAK_ADMIN_PASSWORD}
if [ $? -eq 0 ]
then
  declare -A userFedIds
  if [ "${FORCE_DIRECT_AUTHC}x" != "forcex" ]
  then
    # Check if there is a RACF/LDAP 'direct' federation.  If so, disable it.
    userFeds=$( /opt/keycloak/bin/kcadm.sh get components -r IzoaKeycloak -q type="org.keycloak.storage.UserStorageProvider" --fields id,name,providerId --format csv --noquotes )
    for i in ${userFeds}
    do
      userFedId=$( echo "${i}" | cut -f 1 -d "," )
      userFedName=$( echo "${i}" | cut -f 2 -d "," )
      userFedProvider=$( echo "${i}" | cut -f 3 -d "," )
      if [ ${userFedProvider} == "racf" ]
      then
        if [ "${OSTYPE}" == "OS/390" ]
        then
          # We are under z/OS UNIX System Services; Keycloak returns JSON output as garbage ... and field positions in CSV output are not guaranteed
          #   need to attempt to get information via grep
          /opt/keycloak/bin/kcadm.sh get components/${userFedId} -r IzoaKeycloak --format csv | grep -q \"direct\"
          if [ $? == 0 ]
          then
            fedAuthType=direct
          else
            fedAuthType=not-direct
          fi
          /opt/keycloak/bin/kcadm.sh get components/${userFedId} -r IzoaKeycloak --format csv | grep -q \"true\"
          if [ $? == 0 ]
          then
            fedEnabled=true
          else
            fedEnabled=false
          fi
        else
          fedAuthType=$( /opt/keycloak/bin/kcadm.sh get components/${userFedId} -r IzoaKeycloak 2>/dev/null | grep \"authType\" | head -1 | cut -f 4 -d "\"" )
          fedEnabled=$( /opt/keycloak/bin/kcadm.sh get components/${userFedId} -r IzoaKeycloak 2>/dev/null | grep \"enabled\" | head -1 | cut -f 4 -d "\"" )
        fi
        if [ ${fedAuthType} == "direct" ] && [ ${fedEnabled} == "true" ]
        then
          echo "Temporarily disabling the ${userFedName} user federation because it uses the 'direct' authentication type..."
          userFedIds[${userFedName}]=${userFedId}
          /opt/keycloak/bin/kcadm.sh update components/${userFedId} -r IzoaKeycloak -s 'config.enabled=["false"]'
        fi
      fi
    done
  fi
  
  echo ""
  while read -r USER
  do
    USER=$( echo "${USER}" | tr -s '[:blank:]' )    # remove any extra blanks
    if (  [ "${USER:0:1}x" != "x" ] && [ "${USER:0:1}x" != "#x" ] )    # skip comments or blank lines
    then
      ID=$( /opt/keycloak/bin/kcadm.sh get users -r IzoaKeycloak --fields id -q username=${USER} --format csv --noquotes )
      if [[ ${ID} == "" ]]   # id doesn't exist
      then
        ID=$( /opt/keycloak/bin/kcadm.sh create users -r IzoaKeycloak -s username=${USER} -s enabled=true -i)
        if [[ ${ID} != "" ]]   # id successfully created
        then
          echo "Successfully created ID ${USER}"
        else
          echo "WARNING: Problem initializing user ${USER}."
        fi
      else
        echo "ID ${USER} already exists"
      fi
    fi

  done < ${KEYCLOAK_IDS}
  echo ""

  for key in "${!userFedIds[@]}"
  do
    echo "Re-enabling ${key} user federation..."
    /opt/keycloak/bin/kcadm.sh update components/${userFedIds[$key]} -r IzoaKeycloak -s 'config.enabled=["true"]'
  done

fi

terminateCliSessions > /dev/null 2>&1
