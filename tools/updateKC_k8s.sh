#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-LDA (C) Copyright IBM Corp. 2024
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            updateKC_k8s.sh
#
# Description:     IBM Z Operational Analytics common services installation
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Script used to the Keycloak authentication service installed
#                  as part of the IBM Z Operational Analytics common services
#
# Syntax:          updateKC_k8s.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

SCRIPTDIR="$( cd "$( dirname "$0" )/../.." && pwd )"
SCRIPTNAME="$( basename "$0" )"

. ${SCRIPTDIR}/utils/common_functions_k8s.sh

if [ "${SCRIPTNAME}" == "updateKC_k8s.sh" ]
then
  OCI_AGENT=kubectl
  DISPLAY_NAME=Kubernetes
elif [ "${SCRIPTNAME}" == "updateKC_oc.sh" ]
then
  OCI_AGENT=oc
  DISPLAY_NAME="OpenShift Container Platform"
else
  logToStdout "${ERRORMSG}Invalid invocation: ${SCRIPTNAME}. Unable to proceed."
  exit 1
fi

BASEDIR=$( dirname ${SCRIPTDIR} )
ARCH=$( uname -m )
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
DELETE_NETWORK=false

usage() {
  echo ""
  echo "Usage:"
  echo "  updateKC.sh                to update authentication service configuraton for use with"
  echo "                             Z Operational Analytics components"
  echo ""
}

checkService() {
  SERVICE_NAME=${1}
  RESPONSE=$( ${OCI_AGENT} get pods --namespace ${NAMESPACE} | grep ${SERVICE_NAME} ; echo _${PIPESTATUS[*]} )
  TRIMMED_RESPONSE=$( echo ${RESPONSE} | tr -d '\n' | tr ' ' '^' )
  IFS='_' read -r -a STATUSARRAY <<< "${TRIMMED_RESPONSE}"
  CLEANSTATUS=${STATUSARRAY[0]}
  IFS='^' read -r -a RCS <<< "${STATUSARRAY[1]}"
  if [ ${RCS[1]} -ne 0 ]
  then
    echo "No pods for Deployment ${SERVICE_NAME} exist."
    SERVICE_STATUS=DOWN
  else
    DETAIL=$( echo "${CLEANSTATUS}" | awk -F'^' '{ print $3 }' )
    echo "${DETAIL}" | grep -q Running
    if [ $? -eq 0 ]
    then
      echo "Deployment ${SERVICE_NAME} has running pods."
      SERVICE_STATUS=UP
    else
      echo "${DETAIL}" | grep -q Terminating
      if [ $? -eq 0 ]
      then
        echo "Deployment ${SERVICE_NAME} has terminating pods."
        SERVICE_STATUS=DOWN
      else
        echo "Deployment ${SERVICE_NAME} is in an undetermined state."
        SERVICE_STATUS=UNSTABLE
      fi
    fi
  fi
}

echo "Configuring authentication service"
echo ""
set -a
. ${BASEDIR}/${NEW_CONFIG}
. ${BASEDIR}/${IBM_CONFIG}
set +a
if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
then
  export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
fi
if [ "${SUBDOMAIN}x" == "x" ]
then
  AUTH_HOST=auth
else
  AUTH_HOST=auth.${SUBDOMAIN}
fi

COMMON_CONTAINER_PREFIX=zoa-

# Check Keycloak status; shut it down if needed
checkService ${COMMON_CONTAINER_PREFIX}auth
if [ "${SERVICE_STATUS}" == "UNSTABLE" ]
then
  logToStdout "${WARNMSG}Service ${COMMON_CONTAINER_PREFIX}auth is not ready. Unable to proceed."
  exit 1
elif [ "${SERVICE_STATUS}" == "DOWN" ]
then
  cd ${BASEDIR}
  # Bring up Keycloak in standard mode
  echo "Starting authentication service in standard mode..."
  eval "echo \"$(<${BASEDIR}/k8s-config/auth.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  # Apply ZDAp-specific configuration to Keycloak
  waitForPod ${COMMON_CONTAINER_PREFIX}auth
  if [ "${PODREADY}" == "TRUE" ]
  then
    cd ${BASEDIR}
    printf "%-50s %s " "Waiting for authentication service to be ready" "..."
    ${SCRIPTDIR}/k8sManageZoa.sh wait-for-service auth.zoa 8443
    if [ $? -eq 1 ]
    then
      shutdownKeycloak standard
      exit 1
    fi
  fi
fi

chmod 755 ${SCRIPTDIR}/utils/keycloak/*.sh
POD=$( ${OCI_AGENT} get pods --namespace ${NAMESPACE} | grep ${COMMON_CONTAINER_PREFIX}auth | grep Running | awk '{ print $1 }' )
if [ "${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}x" == "truex" ]
then
  dimDisplay
  ${OCI_AGENT} cp --namespace ${NAMESPACE} ${SCRIPTDIR}/utils/keycloak/zoa_create_perm_admin.sh ${POD}:/realm/
  resetDisplay
  ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}auth -- bash -c "/realm/zoa_create_perm_admin.sh ${ZAIOPS_KC_BOOTSTRAP_ADMIN} ${ZAIOPS_KC_BOOTSTRAP_PASSWORD} ${ZAIOPS_KEYCLOAK_ADMIN} ${ZAIOPS_KEYCLOAK_ADMIN_PASS} ${ZAIOPS_KC_CONTEXT_ROOT} ${ZAIOPS_KEYCLOAK_HOST} ${ZAIOPS_KEYCLOAK_PORT} ${ZAIOPS_ZOASVC_PASS}"
  sed -i -e "s%^ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=.*$%ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=false%g" ${BASEDIR}/${IBM_CONFIG}
  export ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=false
fi
dimDisplay
${OCI_AGENT} cp --namespace ${NAMESPACE} ${SCRIPTDIR}/utils/keycloak/zoa_kc_update.sh ${POD}:/realm/
resetDisplay
${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}auth -- bash -c "/realm/zoa_kc_update.sh ${ZAIOPS_KEYCLOAK_ADMIN} ${ZAIOPS_KEYCLOAK_ADMIN_PASS} ${ZAIOPS_KC_REALM_ADMIN} ${ZAIOPS_ZOAREALM_ADMIN_PASS} ${ZAIOPS_ZOASVC_PASS} ${ZAIOPS_KEYCLOAK_HOST} ${ZAIOPS_KEYCLOAK_PORT} ${IZOA_GATEWAY_PORT} ${ZAIOPS_KC_CONTEXT_ROOT}"

# If the service was down before, bring it back down
if [ "${SERVICE_STATUS}" = "DOWN" ]
then
  shutdownKeycloak standard
fi
