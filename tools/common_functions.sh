######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2022, 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

INFOMSG="\x1b[32;20mINFO    >> "
ERRORMSG="\x1b[31;20mERROR   >> "
WARNMSG="\x1b[33;20mWARNING >> "
SPACEMSG="           "
ENDMSG="\x1b[0m"

logToStdout() {
  MSG=${1}
  echo -e "${MSG}${ENDMSG}"
}

logToFile() {
  MSG=${1}
  (
  echo -e "${MSG}${ENDMSG}"
  ) | sed "s%\x1b\[[0-9;]*m%%g" >> ${LOGFILE}
}

logToFileAndStdout() {
  MSG=${1}
  (
  echo -e "${MSG}${ENDMSG}"
  ) | tee >(sed "s%\x1b\[[0-9;]*m%%g" >> ${LOGFILE})
}

shutdownKeycloak() {
  MODE=${1}
  echo "Shutting down authentication service (${MODE} mode)..."
  echo -n "  Stopping container "
  ${OCI_AGENT} stop ${COMMON_CONTAINER_PREFIX}auth
  echo -n "  Removing container "
  ${OCI_AGENT} rm ${COMMON_CONTAINER_PREFIX}auth
  if [ "${DELETE_NETWORK}" == "true" ]
  then
    echo -n "  Removing network "
    ${OCI_AGENT} network rm ${COMPOSE_PROJECT_NAME}_zaiops
  fi 
}

valNatNum() {
  PARM=${1}
  VALUE=${2}
  MINVAL=${3}
  RESIDUE=$( echo ${VALUE} | tr -d [0-9] )
  LENGTH=${#VALUE}
  FIRSTDIGIT=$( echo ${VALUE:0:$((${LENGTH}-(${LENGTH}-1)))} )
  if [ "${RESIDUE}x" != "x" ]
  then
    logToStdout "\n${ERRORMSG}${PARM} must be a natural number."
    echo "The actual value of ${PARM} (${VALUE}) contains the following disallowed characters: ${RESIDUE}"
    echo ""
    exit 1
  elif [ ${LENGTH} == 1 ] && [ ${FIRSTDIGIT} == 0 ] && [ ${MINVAL} -gt 0 ]
  then
    logToStdout "\n${ERRORMSG}${PARM} must be a positive natural number."
    echo "The actual value of ${PARM} is ${VALUE}."
    echo ""
    exit 1
  fi
}

valLow() {
  PARM=${1}
  VALUE=${2}
  LOWVAL=${3}
  if [ ${VALUE} -lt ${LOWVAL} ]
  then
    logToStdout "\n${WARNMSG}You are reducing the value of ${PARM} below the factory preset of ${LOWVAL}."
    echo "The actual value of ${PARM} is ${VALUE}."
    echo "The application may experience increased performance issues with this setting."
    echo ""
    read -n 1 -s -r -p "Press any key to continue..."
    echo ""
  fi
}

valHigh() {
  PARM=${1}
  VALUE=${2}
  HIGHVAL=${3}
  if [ ${VALUE} -gt ${HIGHVAL} ]
  then
    logToStdout "\n${WARNMSG}You have selected a very high value for ${PARM}."
    echo "The actual value of ${PARM} is ${VALUE}."
    echo "The application may experience memory issues with this setting."
    echo ""
    read -n 1 -s -r -p "Press any key to continue..."
    echo ""
  fi
} 
  
valWindow() {
  PARM=${1}
  VALUE=${2}
  LENGTH=${#VALUE}
  LASTDIGIT=$( echo ${VALUE: -$((${LENGTH}-(${LENGTH}-1)))} )
  NUMBER=$( echo ${VALUE:0:$((${LENGTH}-1))} )
  if [ "${LASTDIGIT}" != "h" ] && [ "${LASTDIGIT}" != "m" ]
  then
    logToStdout "\n${ERRORMSG}The value of ${PARM} must be a time unit in hours (h) or minutes (m)."
    echo "The actual value of ${PARM} is ${VALUE}."
    echo ""
    exit 1
  fi
  valNatNum ${PARM} ${NUMBER} 1
}

testRoute() {
  THISIP=${1}
  ROUTABLE=tbd
  while [ "${ROUTABLE}" != "true" ]
  do
    echo ${THISIP} | grep -qE '^(172\.1[6-9]\.|172\.2[0-9]\.|172\.3[0-1]\.|10\.|192\.168\.)'
    if [ $? -eq 0 ]
    then
      ROUTABLE=false
      echo "IP address '${THISIP}' is not in a routable address space and therefore not reachable from other networks."
      echo "Do you want to use it anyway? (y/N)"
      read -e USEIP
      USEIP=${USEIP:="n"}
      USEIP=$( echo ${USEIP} | tr [:upper:] [:lower:] )
      if [ "${USEIP}" == "y" ]
      then
        ROUTABLE=true
      else
        echo "Provide a routable IP address that can be used to reach this system:"
        read -e THISIP
        testIP ${THISIP}
      fi
    else
      ROUTABLE=true
    fi
  done
}

testIP() {
  THISIP=${1}
  VALID=tbd
  while [ "${VALID}" != "true" ]
  do
    echo ${THISIP} | grep -qE "^([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})$"
    if [ $? -eq 0 ]
    then
      VALID=true
    else
      VALID=false
      echo "IP address '${THISIP}' is not a well-formed address."
      echo "Provide a well-formed IP address:"
      read -e THISIP
    fi
  done
}

getHostAndIp() {
  # Determine hostname of the system
  THISHOST=$( hostname -f )
  THISHOST_LOWER=$( hostname -f | tr [:upper:] [:lower:] )
  THISHOST_LOWER=$( hostname -f | tr [:upper:] [:lower:] )
  echo ${THISHOST_LOWER} | grep -qE '(\.)'
  if [ $? -eq 0 ]
  then
    HASFQDN="TRUE"
  else
    HASFQDN="FALSE"
  fi
  if [ "${hashost}" = "false" ]
  then
    IPARRAY=( $(getent ahosts ${THISHOST_LOWER} | grep -v "::" | grep STREAM | awk '{ print $1 }') )
  else
    IPARRAY=( $(host ${THISHOST_LOWER} | grep -vi IPv6 | awk '{ print $NF }' | grep -v ^[[:alpha:]]*$) )
    # Alphabetic characters mean invalid IP address(es)
    echo "${IPARRAY}" | grep -qE '(.*[a-zA-Z].*)'
    if [ $? -eq 0 ]
    then
      # 'host' command did not return any IP addresses; try getent instead
      IPARRAY=( $(getent ahosts ${THISHOST_LOWER} | grep -v "::" | grep STREAM | awk '{ print $1 }') )
    fi
  fi
  IPCOUNT=${#IPARRAY[@]}
  TOPIP=${IPARRAY[0]}
  if [ ${IPCOUNT} -eq 1 ]
  then
    THISIP=${TOPIP}
  else
    IPLIST="${TOPIP}"
    for (( n=1; n<${IPCOUNT}; n++ ))
    do
      IPLIST="${IPLIST}, ${IPARRAY[n]}"
    done
    if [ "${USEFIRSTIP}" == "Y" ] || [ "${USEFIRSTIP}" == "y" ]
    then
      THISIP=${TOPIP}
    else
      echo ""
      echo "This system reports multiple IPv4 addresses (${IPLIST})."
      echo "Which of these IP addresses can be accessed from the browser you will use"
      echo "to access this product's web user interface? (default: ${TOPIP})"
      read -e THISIP
      THISIP=${THISIP:-${TOPIP}}
      while :
      do
        findElement "${THISIP}" "${IPARRAY[@]}"
        if [ $? -eq 0 ]
        then
          break
        else
          echo ""
          echo "Invalid answer. The only recognized IP addresses are ${IPLIST}."
          echo "Please try again:"
          read -e THISIP
          THISIP=${THISIP:-${TOPIP}}
        fi
      done
    fi
    echo ""
    echo "The following IP address will be used for product configuration: ${THISIP}"
    echo ""
  fi
  testRoute ${THISIP}
  # Update config file with system's hostname and IP address
  if [ "${HASFQDN}" == "TRUE" ]
  then
    sed -i -e "s%^ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${THISHOST_LOWER}%g" ${BASEDIR}/zoa_env.config
    sed -i -e "s%^CDP_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%CDP_KAFKA_BOOTSTRAP_SERVER_HOST=${THISHOST_LOWER}%g" ${BASEDIR}/zoa_env.config
    sed -i -e "s%^ZAIOPS_KEYCLOAK_HOST=.*$%ZAIOPS_KEYCLOAK_HOST=${THISHOST_LOWER}%g" ${BASEDIR}/.zoa_factory.config
    sed -i -e "s%^EXTERNAL_GATEWAY_HOST=.*$%EXTERNAL_GATEWAY_HOST=${THISHOST_LOWER}%g" ${BASEDIR}/.zoa_factory.config
  else
    sed -i -e "s%^ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${THISIP}%g" ${BASEDIR}/zoa_env.config
    sed -i -e "s%^CDP_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%CDP_KAFKA_BOOTSTRAP_SERVER_HOST=${THISIP}%g" ${BASEDIR}/zoa_env.config
    sed -i -e "s%^ZAIOPS_KEYCLOAK_HOST=.*$%ZAIOPS_KEYCLOAK_HOST=${THISIP}%g" ${BASEDIR}/.zoa_factory.config
    sed -i -e "s%^EXTERNAL_GATEWAY_HOST=.*$%EXTERNAL_GATEWAY_HOST=${THISIP}%g" ${BASEDIR}/.zoa_factory.config
  fi
  sed -i -e "s%^ZAIOPS_KEYCLOAK_IP=.*$%ZAIOPS_KEYCLOAK_IP=${THISIP}%g" ${BASEDIR}/.zoa_factory.config
}

findElement() {
  local e match="$1"
  shift
  for e; do [[ "$e" == "$match" ]] && return 0; done
  return 1
}

help() {
  echo "Core commands:"
  echo -e "  help                       Show this usage information"
  echo -e "  up                         Create and start containers"
  echo -e "  down                       Stop and remove containers and networks"
  echo -e "  logs                       View output from containers"
  echo -e "  get-log-level              View log level of a specific container"
  echo -e "  set-log-level              Set log level for a specific container"
  echo -e "  ps                         List containers"
  echo -e "  start                      Start services"
  echo -e "  stop                       Stop services"
  echo -e "  restart                    Restart services"
  echo -e "  purge                      Stop and remove containers and networks and purge data volumes"
  echo -e "  inspect-images             Print information about OCI images"
  echo -e "  kafka-topics               Manage Kafka topics"
  echo -e "  kafka-console-consumer     Show contents of a Kafka topic"
  echo -e "  kafka-consumer-groups      Show information about a Kafka consumer group"
  echo -e "  kafka-prune [ ZAA | ZLDA | ZDiscovery ] Prune data from a Kafka topic"
  echo -e "                             Optionally, reconfigure data retention duration for the topic"
  echo -e "  kafka-debug-logging [ enable | disable ]"
  echo -e "                             Enable or disable debug logging for the Kafka broker service"
  echo -e "  config-certificates [ generate | force-generate | generate-csr-only | use-provided"
  echo -e "                        list-certs | import-cert | sync-certs | delete-cert | export ]"
  echo -e "                             (Re-)configure TLS certificates, keystore and truststore"
  echo -e "  move-data                  Move data from one named volume to another"
  echo -e "  backup-data                Back up data from one named volume to host file system"
  echo -e "  restore-data               Restore backup on host file system to named volume"
  echo -e "  seed-ldap [ force ]        Seed authentication service with LDAP user IDs based on an input file"
  echo -e "                             Optionally, use federations with 'direct' authentication"
  echo -e "  gather                     Zip log and configuration files"
  echo -e "  update-keycloak-password [ keycloakadmin | zoakcadmin | <other id>]"
  echo -e "                             Update the password for keycloak administrator ids"
  for FEATURE in ${INSTALLED_FEATURES}
  do
    if [ -f ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh ]
    then
      . ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh help
    fi
  done
}

wait-for-service() {
  HOST=${1}
  PORT=${2}
  NOEXIT=${3}
  if [ "${HOST}" == "auth" ]
  then
    SERVICE=${HOST}
  else
    SERVICE=gateway
  fi
  START=$( date +"%s" )
  ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}${SERVICE} bash -c "wait-for-it.sh --quiet --progress --strict --timeout=${SERVICE_TIMEOUT} ${HOST}:${PORT}"
  if [ $? -eq 0 ]
  then
    PORTCHECK=PASSED
  else
    PORTCHECK=FAILED
    echo "service still not ready after ${SERVICE_TIMEOUT} seconds; giving up."
    logToStdout "${ERRORMSG}Service status:"
    echo "--------------------------------------------------------------------------------"
    ${SCRIPTDIR}/${SCRIPT_PREFIX}ManageZoa.sh ps
    echo "--------------------------------------------------------------------------------"
  fi
  if [ "${HOST}" == "auth" ] && [ "${PORTCHECK}" == "PASSED" ]
  then
    APICHECK=$( check-kc-readiness )
    if [ "${APICHECK}" == "PASSED" ]
    then
      ALLCHECKS=PASSED
    else
      echo "service response is invalid; giving up."
      ALLCHECKS=FAILED
    fi
  else
    ALLCHECKS=${PORTCHECK}
  fi
  if [ "${ALLCHECKS}" == "PASSED" ]
  then
    END=$( date +"%s" )
    echo "successful after $(( ${END} - ${START} )) seconds."
  else
    if [ "${NOEXIT}" == "NOEXIT" ]
    then
      return 1
    else
      exit 1
    fi
  fi
}

check-kc-readiness() {
  ${OCI_AGENT} cp ${SCRIPTDIR}/utils/keycloak/checkKCreadiness.sh ${COMMON_CONTAINER_PREFIX}auth:/tmp/
  if [ "${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}x" == "truex" ]
  then
    KC_TEST_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN}
    KC_TEST_ADMIN_PWD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD}
  else
    KC_TEST_ADMIN=${ZAIOPS_KEYCLOAK_ADMIN}
    KC_TEST_ADMIN_PWD=${ZAIOPS_KEYCLOAK_ADMIN_PASS}
  fi
  ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}auth bash -c "/tmp/checkKCreadiness.sh ${KC_TEST_ADMIN} ${KC_TEST_ADMIN_PWD} auth ${ZAIOPS_KC_CONTEXT_ROOT}"
}

levelCheck() {
  for level in "$@"
  do
    case "$level" in
    "trace"|"debug"|"info"|"warn"|"error") : ;;
    *)
    logToStdout "${ERRORMSG}No such log level: $level"
    echo "List of available log levels:"
    echo -e " error"
    echo -e " warn"
    echo -e " info"
    echo -e " debug"
    echo -e " trace"
    exit 1
    ;;
    esac
  done
}

kafka-debug-logging() {
  ACTION=$1
  # First for the Kafka broker
  if(isContainerRunning kafkabroker)
  then
    if [ "${ACTION}x" == "x" ]
    then
      logToStdout "${ERRORMSG}No action specified. Unable to proceed."
      exit 1
    fi
    NODE_ID=$( ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkabroker bash -c "grep ^node.id \${KAFKA_HOME}/data/kafka-broker-logs/meta.properties | cut -f 2- -d '='" )
    if [ "$( echo ${ACTION} | tr [:upper:] [:lower:] )" == "enable" ]
    then
      ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkabroker /opt/kafka/bin/kafka-configs.sh --bootstrap-server kafkabroker:19092 --alter --add-config "kafka=${ZOA_DEBUG_LEVEL},org.apache.kafka=${ZOA_DEBUG_LEVEL}" --entity-type broker-loggers --entity-name ${NODE_ID}
    elif [ "$( echo ${ACTION} | tr [:upper:] [:lower:] )" == "disable" ]
    then
      ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkabroker /opt/kafka/bin/kafka-configs.sh --bootstrap-server kafkabroker:19092 --alter --delete-config "kafka,org.apache.kafka" --entity-type broker-loggers --entity-name ${NODE_ID}
    else
      logToStdout "${ERRORMSG}Invalid action '${ACTION}' specified. Only 'enable' and 'disable' are supported."
      exit 1
    fi
  else
    logToStdout "${WARNMSG}Kafka broker service must be started before running this command."
  fi
  # And the same thing for the Kafka controller
  if(isContainerRunning kafkacontroller)
  then
    if [ "${ACTION}x" == "x" ]
    then
      logToStdout "${ERRORMSG}No action specified. Unable to proceed."
      exit 1
    fi
    NODE_ID=$( ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkacontroller bash -c "grep ^node.id \${KAFKA_HOME}/data/kafka-controller-logs/meta.properties | cut -f 2- -d '='" )
    if [ "$( echo ${ACTION} | tr [:upper:] [:lower:] )" == "enable" ]
    then
      ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkacontroller /opt/kafka/bin/kafka-configs.sh --bootstrap-controller kafkacontroller:9092 --alter --add-config "kafka=${ZOA_DEBUG_LEVEL},org.apache.kafka=${ZOA_DEBUG_LEVEL}" --entity-type broker-loggers --entity-name ${NODE_ID}
    elif [ "$( echo ${ACTION} | tr [:upper:] [:lower:] )" == "disable" ]
    then
      ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkacontroller /opt/kafka/bin/kafka-configs.sh --bootstrap-controller kafkacontroller:9092 --alter --delete-config "kafka,org.apache.kafka" --entity-type broker-loggers --entity-name ${NODE_ID}
    else
      logToStdout "${ERRORMSG}Invalid action '${ACTION}' specified. Only 'enable' and 'disable' are supported."
      exit 1
    fi
  else
    logToStdout "${WARNMSG}Kafka controller service must be started before running this command."
  fi
}

get-log-level() {
  SLL_ENABLED="gateway"
  for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
  do
    eval TEMPVAR=\"\$${UFEATURE}_SETLOGLEVEL\"
    SLL_ENABLED+=" ${TEMPVAR}"
  done
  echo ""
  echo "Services supported for this operation are: ${SLL_ENABLED}"
  echo -n "Specify a service:  "
  read -e service
  serviceToContainer $service
  
  if [[ "${#package[@]}" -gt 1 ]]; then
    echo ""
    selectPackage
  fi

  logLevel="get"
  echo ""
  echo -n "Log level reported by Service '"${service}"':  "
  ${OCI_AGENT} exec $containerName $path $package $logLevel $xml
  echo ""
}

getAdminCredentials() {
  if [ "${ADMINTMU}x" = "x" ]
  then
    echo ""
    # Get administrative user ID
    echo -n "Provide an administrative user ID for the Problem Insights server (default: piadmin):  "
    read -e ADMINTMU
    ADMINTMU=${ADMINTMU:-"piadmin"}
  fi
  if [ "${ADMINTMP}x" = "x"  ]
  then
    echo ""
    # Get administrative password
    echo "Provide the password for ${ADMINTMU}:"
    stty -echo
    while [[ -z "${ADMINTMP}" ]]; do
      read -e ADMINTMP
    done
    stty echo
  fi
  echo ""
  echo ""
}

checkIPresult() {
  RESULT="$1"
  OBJECT=$2
  echo "${RESULT}" | grep -q "Command successful"
  if [ $? -eq 0 ]
  then
    logToStdout "  ${INFOMSG}Object '"${PACK}"' was processed successfully."
  else
    logToStdout "  ${ERRORMSG}A problem occurred while processing object '"${PACK}"'."
    logToStdout "  ${SPACEMSG}See the Problem Insights service log for details."
  fi
}

inspect-images() {
  echo "********************************************************************************"
  echo "*                                                                              *"
  echo "*                        IBM Z AIOps OCI image report                          *"
  echo "*                                                                              *"
  echo "********************************************************************************"

  ZOA_IMG_CORE=$(${OCI_AGENT} images -f 'label=feature=IBM Z AIOps - Common Services - Core' -q)
  ALL_IMAGES="${ZOA_IMG_CORE}"
  for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
  do
    eval TEMPVAR=\"\$${UFEATURE}_OCI_FEATURE_LABEL\"
    TEMP_IMG_LIST=$(${OCI_AGENT} images -f "label=feature=${TEMPVAR}" -q)
    ALL_IMAGES+=" ${TEMP_IMG_LIST}"
  done
  for IMG in ${ALL_IMAGES}
  do
    TAGS=$( ${OCI_AGENT} inspect --format '{{ index .RepoTags }}' ${IMG} | tr -d "[]" )
    FEATURE=$( ${OCI_AGENT} inspect --format '{{ index .Config.Labels "feature" }}' ${IMG} )
    REPO=$( ${OCI_AGENT} inspect --format '{{ index .Config.Labels "repo" }}' ${IMG} )
    COMMIT=$( ${OCI_AGENT} inspect --format '{{ index .Config.Labels "commit" }}' ${IMG} )
    CREATED=$( ${OCI_AGENT} inspect --format '{{ index .Created }}' ${IMG} )
    CHECKSUMS=$( ${OCI_AGENT} inspect --format '{{ index .RootFS.Layers }}' ${IMG} | tr -d "[]" )
    echo "  ${TAGS}"
    echo "    Feature:      ${FEATURE}"
    echo "    Repository:   ${REPO}"
    echo "    Commit level: ${COMMIT}"
    echo "    Created:      ${CREATED}"
    echo "    Checksums: "
    for SHA in $( echo ${CHECKSUMS} )
    do
      echo "      ${SHA}"
    done
    echo "--------------------------------------------------------------------------------"
  done
}

startUtil() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} run -d -t -u ${ZOA_UID}:0 --rm --name zaiops-util-${UTILTSTAMP} --entrypoint /bin/bash -v zaiops_shared:/shared:rw ibm-zaiops/zoa-service-discovery:${TAG}-${ARCH} > /dev/null
}

stopUtil() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} stop zaiops-util-${UTILTSTAMP} > /dev/null
}

dumpTLSArtifacts() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} cp zaiops-util-${UTILTSTAMP}:/shared/config/zoasvc.tls ${BASEDIR} > /dev/null
}

tmpUnpackCerts() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "if [ -f /shared/config/zoasvc.tls ] ; then mkdir -p /shared/tls_tmp && rm -f /shared/tls_tmp/* ; cd /shared/tls_tmp ; cat /shared/config/zoasvc.tls | base64 -d | tar xz ; else echo \"${ERRORMSG}TLS artifacts not found.\" ; exit 1 ; fi"
}

tmpUnpackCertsNoError() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "if [ -f /shared/config/zoasvc.tls ] ; then mkdir -p /shared/tls_tmp && rm -f /shared/tls_tmp/* ; cd /shared/tls_tmp ; cat /shared/config/zoasvc.tls | base64 -d | tar xz ; fi"
}

tmpRepackCerts() {
  UTILTSTAMP=${1}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "if [ -f /shared/tls_tmp/zoasvc.ts ] && [ -s /shared/tls_tmp/zoasvc.ts ] ; then cd /shared/tls_tmp ; rm -f /shared/config/zoasvc.tls ; tar cz --exclude *.sh *.* | base64 > /shared/config/zoasvc.tls ; chmod 644 /shared/config/zoasvc.tls ; cd /tmp ; rm -Rf /shared/tls_tmp ; else echo \"${ERRORMSG}Truststore zoasvc.ts missing or empty\" ; exit 1 ; fi"
}

genTlsPwd() {
  # Ensure that keystore password starts with an alphabetic character
  ZOASVC_PASS_PREFIX=$( < /dev/urandom tr -dc A-Za-z 2>/dev/null | head -c${1:-1};echo; )
  ZOASVC_PASS_MAIN=$( < /dev/urandom tr -dc A-Za-z0-9@?^-_ 2>/dev/null | head -c${1:-15};echo; )
  ZOASVC_PASS=${ZOASVC_PASS_PREFIX}${ZOASVC_PASS_MAIN}
  ZOASVC_PASS_B=$( echo "${ZOASVC_PASS}" | base64 )
}

writeTlsPwd() {
  sed -i -e "s%^ZAIOPS_ZOASVC_PASS=.*$%ZAIOPS_ZOASVC_PASS=${ZOASVC_PASS_B}%g" ${BASEDIR}/${NEW_CONFIG}
}

createZoaSecCert() {
  ORIGIN=$( pwd )
  RUNTIME=$( date +"%Y%m%d%H%M" )
  . ${BASEDIR}/zoa_env.config
  ZOASVC_PASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  echo "Generating new security administration artifacts..."
  mkdir -p ${BASEDIR}/tmp_${RUNTIME}/tls_tmp && cd ${BASEDIR}/tmp_${RUNTIME}/tls_tmp
  (
  cat <<- END
#!/bin/bash
OSSL_LEGACY=""
OSSL_VERSION=\$( openssl version 2>/dev/null | awk '{ print \$2 }' | tr -d ".a-zA-Z-" )
if [ \${OSSL_VERSION} -gt 200 ]
then  
  OSSL_LEGACY="-legacy"
fi  
echo ${ZOASVC_PASS} > .pw
echo ${ZOASVC_PASS} >> .pw
# Private key for root CA
openssl genpkey -out internalCA.key -algorithm EC \
    -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
    -pass file:.pw
# Certificate for root CA
openssl req -x509 -sha256 -days 3650 -key internalCA.key \
    -out internalCA.crt -subj "/CN=${THISHOST_LOWER}" \
    -passout file:.pw -passin file:.pw
# Private key for internal datastore security
openssl genpkey -out datastore.key -algorithm EC \
    -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
    -pass file:.pw
# Certificate signing request for datastore
openssl req -new -key datastore.key -subj "/CN=${THISHOST_LOWER}" \
  -passout file:.pw -passin file:.pw \
  -out datastore.csr
# Create extensions file
echo "authorityKeyIdentifier=keyid,issuer" > datastore.ext
echo "basicConstraints=CA:FALSE" >> datastore.ext
echo "subjectAltName = @alt_names" >> datastore.ext
echo "[alt_names]" >> datastore.ext
echo "DNS.1 = ${THISHOST_LOWER}" >> datastore.ext
echo "DNS.2 = localhost" >> datastore.ext
echo "DNS.3 = datastore" >> datastore.ext
echo "DNS.4 = dashboards" >> datastore.ext
echo "DNS.5 = host.docker.internal" >> datastore.ext
echo "IP.1 = ${THISIP}" >> datastore.ext
echo "IP.2 = 127.0.0.1" >> datastore.ext
# Sign datastore certificate
openssl x509 -req -CA internalCA.crt -CAkey internalCA.key \
  -in datastore.csr -out datastore.crt -days 3650 \
  -CAcreateserial -extfile datastore.ext \
  -passin file:.pw
# Import everything into a keystore
openssl pkcs12 -export \${OSSL_LEGACY} -out datastore.ks -name datastore \
  -inkey datastore.key -in datastore.crt \
  -passin file:.pw -passout file:.pw
# Truststore for client
# Remove any old certificates
keytool -delete -alias datastore -keystore datastore.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias zoa-services -keystore datastore.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias internal-root -keystore datastore.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-root -keystore datastore.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
# Import fresh certificates
keytool -import -alias datastore -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore datastore.ts -trustcacerts \
  -file datastore.crt
# Datastore truststore also needs zoasvc.crt
keytool -import -alias zoa-services -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore datastore.ts -trustcacerts \
  -file zoasvc.crt
# ...and the rootCA certificate
keytool -import -alias ca-root -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore datastore.ts -trustcacerts \
  -file rootCA.crt
# ...and the internalCA certificate
keytool -import -alias internal-root -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore datastore.ts -trustcacerts \
  -file internalCA.crt
# Private key for security admin
openssl genpkey -out secadmin.key.temp -algorithm EC \
  -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
  -pass file:.pw
# Certificate signing request for security admin
openssl req -new -key secadmin.key.temp \
  -subj "/L=internal/O=zoa/OU=zoa/CN=secadmin" \
  -passout file:.pw -passin file:.pw \
  -out secadmin.csr
# Sign certificate for security admin
openssl x509 -req -CA internalCA.crt -CAkey internalCA.key \
  -in secadmin.csr -out secadmin.crt -days 3650 \
  -CAcreateserial -passin file:.pw
# Convert security admin key to PKCS8 format
openssl pkcs8 -inform PEM -outform PEM \
  -in secadmin.key.temp -topk8 \
  -v1 PBE-SHA1-3DES -out secadmin.key \
  -passout file:.pw -passin file:.pw
# Remove temporary (non-PKCS8) key
rm -f secadmin.key.temp
# Remove password file
rm -f .pw
END
  ) > secproc.sh
  cd ${BASEDIR}/tmp_${RUNTIME}/
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tar cf sec_config.tar tls_tmp && ${OCI_AGENT} cp sec_config.tar zaiops-util-${UTILTSTAMP}:/shared/
  tmpUnpackCerts ${UTILTSTAMP}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "tar -C /shared -xf /shared/sec_config.tar ; chmod 755 /shared/tls_tmp/*.sh ; rm -f /shared/sec_config.tar ; cd /shared/tls_tmp ; ./secproc.sh"
  tmpRepackCerts ${UTILTSTAMP}
  dumpTLSArtifacts ${UTILTSTAMP}
  stopUtil ${UTILTSTAMP}
  # Clean up
  cd ${ORIGIN} && rm -Rf ${BASEDIR}/tmp_${RUNTIME}
}

createZoaSvcCsr() {
  . ${BASEDIR}/zoa_env.config
  ZOASVC_PASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  CHECKIP=${1}
  THISHOST_LOWER=${2}
  ORIGIN=$( pwd )
  RUNTIME=$( date +"%Y%m%d%H%M" )
  if [ "${CHECKIP}" == "true" ]
  then
    getHostAndIp
  fi
  # Check whether keystore already exists. If it doesn't, bail since we don't have the basics to create a CSR from
  if [ ! -f ${BASEDIR}/zoasvc.tls ]
  then
    logToStdout "${ERROMSG}No private key found to generate signing request with. Unable to proceed."
    exit 1
  else
    mkdir -p ${BASEDIR}/tmp_${RUNTIME}/tls_tmp && cd ${BASEDIR}/tmp_${RUNTIME}/tls_tmp
    (
    cat <<- END
#!/bin/bash
echo ${ZOASVC_PASS} > .pw
echo ${ZOASVC_PASS} >> .pw
# Create CSR configuration file
echo "[req]" > zoasvc.conf
echo "distinguished_name = req_distinguished_name" >> zoasvc.conf
echo "prompt = no" >> zoasvc.conf
echo "" >> zoasvc.conf
echo "[req_distinguished_name]" >> zoasvc.conf
if [ "${COUNTRY}x" != "x" ]
then
  echo "C = ${COUNTRY}" >> zoasvc.conf
fi
if [ "${STATE}x" != "x" ]
then
  echo "ST = ${STATE}" >> zoasvc.conf
fi
if [ "${LOCATION}x" != "x" ]
then
  echo "L = ${LOCATION}" >> zoasvc.conf
fi
if [ "${ORG_NAME}x" != "x" ]
then
  echo "O = ${ORG_NAME}" >> zoasvc.conf
fi
if [ "${ORG_UNIT}x" != "x" ]
then
  echo "OU = ${ORG_UNIT}" >> zoasvc.conf
fi
echo "CN = ${THISHOST_LOWER}" >> zoasvc.conf
# Create certificate signing request
rm -f zoasvc.csr
openssl req -new -key zoasvc.key -config zoasvc.conf \
  -passout file:.pw -passin file:.pw \
  -out zoasvc.csr
# Remove password file
rm -f .pw
mkdir -p /shared/config
tar cz --exclude *.sh *.* | base64 > /shared/config/zoasvc.tls
chmod 644 /shared/config/zoasvc.tls
END
    ) > csrproc.sh
    cd ${BASEDIR}/tmp_${RUNTIME}/
    UTILTSTAMP=$( date +"%s" )
    startUtil ${UTILTSTAMP}
    tmpUnpackCertsNoError ${UTILTSTAMP}
    tar cf csr_config.tar tls_tmp && ${OCI_AGENT} cp csr_config.tar zaiops-util-${UTILTSTAMP}:/shared/
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "rm -f /shared/config/zoasvc.tls ; tar -C /shared -xf /shared/csr_config.tar ; chmod 755 /shared/tls_tmp/*.sh ; rm -f /shared/csr_config.tar"
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "cd /shared/tls_tmp ; ./csrproc.sh ; cp ./zoasvc.csr /tmp/ ; cd /shared ; rm -Rf tls_tmp"
    dumpTLSArtifacts ${UTILTSTAMP}
    ${OCI_AGENT} cp zaiops-util-${UTILTSTAMP}:/tmp/zoasvc.csr ${BASEDIR} > /dev/null && logToStdout "${INFOMSG}Certificate signing request available at ${BASEDIR}/zoasvc.csr"
    stopUtil ${UTILTSTAMP}
    # Clean up
    cd ${ORIGIN} && rm -Rf ${BASEDIR}/tmp_${RUNTIME}
    echo ""
  fi
}

createZoaSvcCert() { 
  CMDOPT=${1}
  CHECKIP=${2}
  THISIP=${3}
  THISHOST_LOWER=${4}
  shift $#
  ORIGIN=$( pwd )
  RUNTIME=$( date +"%Y%m%d%H%M" )
  shift 2
  if [ "${CHECKIP}" == "true" ]
  then
    getHostAndIp
  fi
  genTlsPwd
  # Check whether keystore already exists. If true, use it.
  if [ ! -f ${BASEDIR}/zoasvc.tls ] || [ "${CMDOPT}" == "FORCEGEN" ]
  then
    echo "Generating new certificates..."
    mkdir -p ${BASEDIR}/tmp_${RUNTIME}/tls_tmp && cd ${BASEDIR}/tmp_${RUNTIME}/tls_tmp
    (
    cat <<- END
#!/bin/bash
CRYPT_ALGO=${CRYPT_ALGO:-EC}
OSSL_LEGACY=""
OSSL_VERSION=\$( openssl version 2>/dev/null | awk '{ print \$2 }' | tr -d ".a-zA-Z-" )
if [ \${OSSL_VERSION} -gt 200 ]
then  
  OSSL_LEGACY="-legacy"
fi  
echo ${ZOASVC_PASS} > .pw
echo ${ZOASVC_PASS} >> .pw
# Private key for root CA
rm -f rootCA.key
openssl genpkey -out rootCA.key -algorithm EC \
    -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
    -pass file:.pw
# Certificate for root CA
rm -f rootCA.crt
openssl req -x509 -sha256 -days 3650 -key rootCA.key \
    -out rootCA.crt -subj "/CN=${THISHOST_LOWER}" \
    -passout file:.pw -passin file:.pw
# Private key for application
rm -f zoasvc.key
if [ "\${CRYPT_ALGO}" == "RSA" ]
then
  openssl genpkey -out zoasvc.key -algorithm RSA \
      -aes256 -pass file:.pw
else
  if [ "\${CRYPT_ALGO}" != "EC" ]
  then
    echo "Invalid cryptographic algorithm '\${CRYPT_ALGO}' specified. Using Elliptic Curve instead."
  fi
  openssl genpkey -out zoasvc.key -algorithm EC \
      -pkeyopt ec_paramgen_curve:prime256v1 -aes256 \
      -pass file:.pw
fi
# Create CSR configuration file
echo "[req]" > zoasvc.conf
echo "distinguished_name = req_distinguished_name" >> zoasvc.conf
echo "prompt = no" >> zoasvc.conf
echo "" >> zoasvc.conf
echo "[req_distinguished_name]" >> zoasvc.conf
if [ "${COUNTRY}x" != "x" ]
then
  echo "C = ${COUNTRY}" >> zoasvc.conf
fi
if [ "${STATE}x" != "x" ]
then
  echo "ST = ${STATE}" >> zoasvc.conf
fi
if [ "${LOCATION}x" != "x" ]
then
  echo "L = ${LOCATION}" >> zoasvc.conf
fi
if [ "${ORG_NAME}x" != "x" ]
then
  echo "O = ${ORG_NAME}" >> zoasvc.conf
fi
if [ "${ORG_UNIT}x" != "x" ]
then
  echo "OU = ${ORG_UNIT}" >> zoasvc.conf
fi
echo "CN = ${THISHOST_LOWER}" >> zoasvc.conf
# Create certificate signing request
rm -f zoasvc.csr
openssl req -new -key zoasvc.key -config zoasvc.conf \
  -passout file:.pw -passin file:.pw \
  -out zoasvc.csr
# Create extensions configuration file for certificate signing
echo "authorityKeyIdentifier=keyid,issuer" > zoasvc.ext
echo "basicConstraints=CA:FALSE" >> zoasvc.ext
echo "subjectAltName = @alt_names" >> zoasvc.ext
echo "[alt_names]" >> zoasvc.ext
echo "DNS.1 = ${THISHOST_LOWER}" >> zoasvc.ext
echo "DNS.2 = localhost" >> zoasvc.ext
echo "DNS.3 = host.docker.internal" >> zoasvc.ext
echo "IP.1 = ${THISIP}" >> zoasvc.ext
echo "IP.2 = 127.0.0.1" >> zoasvc.ext
# Sign certificate
rm -f zoasvc.crt
openssl x509 -req -CA rootCA.crt -CAkey rootCA.key \
  -in zoasvc.csr -out zoasvc.crt -days 3650 \
  -CAcreateserial -extfile zoasvc.ext \
  -passin file:.pw
# Import everything into a keystore
rm -f zoasvc.ks
openssl pkcs12 -export \${OSSL_LEGACY} -out zoasvc.ks -name zoa-services \
  -inkey zoasvc.key -in zoasvc.crt \
  -passin file:.pw -passout file:.pw
# Truststore for client
# If truststores already exist, change truststore password:
if [ -f zoasvc.ts ]
then
  . .pw.ts
  keytool -storepasswd -keystore zoasvc.ts -new "\${NEWPASS}" \
    -storepass "\${OLDPASS}" -storetype pkcs12
fi
if [ -f datastore.ts ]
then
  . .pw.ts
  keytool -storepasswd -keystore datastore.ts -new "\${NEWPASS}" \
    -storepass "\${OLDPASS}" -storetype pkcs12
fi
rm -f .pw.ts
# Remove any old certificates
keytool -delete -alias zoa-services -keystore zoasvc.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-intermediate -keystore zoasvc.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-root -keystore zoasvc.ts \
  -storepass "${ZOASVC_PASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -import -alias zoa-services -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore zoasvc.ts -trustcacerts \
  -file zoasvc.crt
keytool -import -alias ca-root -noprompt -storetype pkcs12 \
  -keypass "${ZOASVC_PASS}" -storepass "${ZOASVC_PASS}" \
  -keystore zoasvc.ts -trustcacerts \
  -file rootCA.crt
# PEM file with all certificates for OpenSearch and OpenSearch Dashboards
openssl pkcs12 -in zoasvc.ts -out zoasvc_all.pem \
  -nokeys -passin file:.pw
# Remove password file
rm -f .pw
mkdir -p /shared/config
tar cz --exclude *.sh *.* | base64 > /shared/config/zoasvc.tls
chmod 644 /shared/config/zoasvc.tls
END
    ) > tlsproc.sh
    echo "OLDPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )" > .pw.ts
    echo "NEWPASS=${ZOASVC_PASS}" >> .pw.ts
    cd ${BASEDIR}/tmp_${RUNTIME}/
    UTILTSTAMP=$( date +"%s" )
    startUtil ${UTILTSTAMP}
    # Reuse existing artifacts only if ${BASEDIR}/zoasvc.tls is present.
    # If it is not present, then we have to assume that no valid password is present for whatever existing TLS artifacts are hanging around.
    # Attempting to re-use them would only cause errors.
    if [ -f ${BASEDIR}/zoasvc.tls ]
    then
      tmpUnpackCertsNoError ${UTILTSTAMP}
    else
      ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "mkdir -p /shared/tls_tmp && rm -f /shared/tls_tmp/*"
    fi
    tar cf tls_config.tar tls_tmp && ${OCI_AGENT} cp tls_config.tar zaiops-util-${UTILTSTAMP}:/shared/
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "rm -f /shared/config/zoasvc.tls ; tar -C /shared -xf /shared/tls_config.tar ; chmod 755 /shared/tls_tmp/*.sh ; rm -f /shared/tls_config.tar"
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "cd /shared/tls_tmp ; ./tlsproc.sh ; cd /shared ; rm -Rf tls_tmp"
    dumpTLSArtifacts ${UTILTSTAMP}
    stopUtil ${UTILTSTAMP}
    # Clean up
    cd ${ORIGIN} && rm -Rf ${BASEDIR}/tmp_${RUNTIME}
    writeTlsPwd
  elif [ -f ${BASEDIR}/zoasvc.tls ] && [ "${CMDOPT}" != "FORCEGEN" ]
  then
    logToStdout "${INFOMSG}TLS artifacts already exist, and 'force-generate' option was not specified.\n${SPACEMSG}Existing TLS artifacts will be reused."
    UTILTSTAMP=$( date +"%s" )
    startUtil ${UTILTSTAMP}
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "mkdir -p /shared/config"
    ${OCI_AGENT} cp ${BASEDIR}/zoasvc.tls zaiops-util-${UTILTSTAMP}:/shared/config/
    stopUtil ${UTILTSTAMP}
    echo ""
  fi
}

listTrustedCerts() {
  TSPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tmpUnpackCerts ${UTILTSTAMP}
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "keytool -list -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' ; rm -Rf /shared/tls_tmp"
  echo ""
  stopUtil ${UTILTSTAMP}
}

deleteTrustedCert() {
  ALIAS=${1}
  NOSTART=${2}
  TSPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  if [ "${ALIAS}x" == "x" ]
  then
    logToStdout "${ERRORMSG}No alias provided."
    exit 1
  fi
  RC=0
  # If called from another function, the utility container is expected to be already running; otherwise, start it
  if [ "${NOSTART}x" == "x" ]
  then
    UTILTSTAMP=$( date +"%s" )
    startUtil ${UTILTSTAMP}
    tmpUnpackCerts ${UTILTSTAMP}
  fi
  ${OCI_AGENT} exec zaiops-util-${UTILTSTAMP} bash -c "keytool -list -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' -alias ${ALIAS}" > /dev/null
  if [ $? -eq 0 ]
  then
    # Certificate exists and can be deleted
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "keytool -delete -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' -alias ${ALIAS} ; echo '${TSPASS}' > /tmp/.pw ; openssl pkcs12 -in /shared/tls_tmp/zoasvc.ts -out /shared/tls_tmp/zoasvc_all.pem -nokeys -passin file:/tmp/.pw ; rm /tmp/.pw"
  else
    logToStdout "${ERRORMSG}Certificate with alias '${ALIAS}' does not exist in trust store."
    RC=1
  fi
  ${OCI_AGENT} exec zaiops-util-${UTILTSTAMP} bash -c "keytool -list -keystore /shared/tls_tmp/datastore.ts -storepass '${TSPASS}' -alias ${ALIAS}" > /dev/null
  if [ $? -eq 0 ]
  then
    # Certificate exists and can be deleted
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "keytool -delete -keystore /shared/tls_tmp/datastore.ts -storepass '${TSPASS}' -alias ${ALIAS}"
  fi
  # If called from another function, that function is expected to have further plans for the TLS artifacts; otherwise, repackage them
  if [ "${NOSTART}x" == "x" ]
  then
    tmpRepackCerts ${UTILTSTAMP}
    dumpTLSArtifacts ${UTILTSTAMP}
    stopUtil ${UTILTSTAMP}
    echo ""
  fi
  if [ ${RC} -ne 0 ]
  then
    exit 1
  fi
}

syncExternalCertStores() {
  TSPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tmpUnpackCerts ${UTILTSTAMP}
  echo ""
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "echo '${TSPASS}' > /tmp/.pw ; openssl pkcs12 -in /shared/tls_tmp/zoasvc.ts -out /shared/tls_tmp/zoasvc_all.pem -nokeys -passin file:/tmp/.pw ; rm /tmp/.pw"
  tmpRepackCerts ${UTILTSTAMP}
  dumpTLSArtifacts ${UTILTSTAMP}
  stopUtil ${UTILTSTAMP}
  echo ""
}

importCert() {
  TSPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  CERTPATH=${1}
  CERTFILE=$( basename ${CERTPATH} )
  ALIAS=${2}
  if [ "${CERTFILE}x" == "x" ]
  then
    logToStdout "${ERRORMSG}Can't extract certificate file name from path '${CERTPATH}'."
    exit 1
  elif [ "${ALIAS}x" == "x" ]
  then
    logToStdout "${ERRORMSG}No alias provided."
    exit 1
  fi
  unset RESPONSE
  RC=0
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tmpUnpackCerts ${UTILTSTAMP}
  echo ""
  ${OCI_AGENT} exec zaiops-util-${UTILTSTAMP} bash -c "keytool -list -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' -alias ${ALIAS}" > /dev/null
  if [ $? -eq 0 ]
  then
    echo "Alias ${ALIAS} already exists. Do you want to replace it with the certificate at ${CERTPATH}? (y/N)  "
    read -e RESPONSE
    RESPONSE=${RESPONSE:-"n"}
    if [ "${RESPONSE}" == "y" ] || [ "${RESPONSE}" == "Y" ]
    then
      deleteTrustedCert ${ALIAS} NOSTART
    elif [ "${RESPONSE}" == "n" ] || [ "${RESPONSE}" == "N" ]
    then
      RC=999
      echo ""
    else
      logToStdout "${ERRORMSG}Invalid response '${RESPONSE}'."
      RC=1
    fi
  fi
  if [ ${RC} -eq 0 ]
  then
    ${OCI_AGENT} cp ${CERTPATH} zaiops-util-${UTILTSTAMP}:/tmp
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "keytool -import -keystore /shared/tls_tmp/zoasvc.ts -storepass '${TSPASS}' -alias ${ALIAS} -noprompt -trustcacerts -keypass '${TSPASS}' -file /tmp/${CERTFILE} ; keytool -import -keystore /shared/tls_tmp/datastore.ts -storepass '${TSPASS}' -alias ${ALIAS} -noprompt -trustcacerts -keypass '${TSPASS}' -file /tmp/${CERTFILE} ; echo '${TSPASS}' > /tmp/.pw ; openssl pkcs12 -in /shared/tls_tmp/zoasvc.ts -out /shared/tls_tmp/zoasvc_all.pem -nokeys -passin file:/tmp/.pw ; rm /tmp/${CERTFILE} /tmp/.pw"
  fi
  tmpRepackCerts ${UTILTSTAMP}
  dumpTLSArtifacts ${UTILTSTAMP}
  stopUtil ${UTILTSTAMP}
  echo ""
  if [ ${RC} -eq 1 ]
  then
    echo ""
  fi
}

# Basic validation of provided host name
valHost() {
  HOSTVALID="Y"
  TESTHOST=${1}
  CHARTEST=$( echo ${TESTHOST} | tr -d [a-zA-Z0-9.] | tr -d '-' )
  echo ${TESTHOST} | grep -q ^[a-zA-Z0-9]
  FIRSTTEST=$?
  echo ${TESTHOST} | grep -q [.]
  SEGTEST=$?
  if [ "${TESTHOST}x" = "x" ]
  then
    logToStdout "${ERRORMSG}Empty host name provided; please try again."
    HOSTVALID="N"
    echo ""
  elif [ ! "${CHARTEST}x" = "x" ]
  then
    logToStdout "${ERRORMSG}Host name contains invalid characters; please try again."
    HOSTVALID="N"
    echo ""
  elif [ ! ${FIRSTTEST} -eq 0 ]
  then
    logToStdout "${ERRORMSG}Host name must start with a letter or a number; please try again."
    HOSTVALID="N"
    echo ""
  elif [ ! ${SEGTEST} -eq 0 ]
  then
    logToStdout "${ERRORMSG}Host name contains no domain suffix; please try again."
    HOSTVALID="N"
    echo ""
  fi
}

valIp() {
  IPVALID=1
  TESTIP=${1}
  if [[ ${TESTIP} =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]
  then
    # IP address has valid 4-segment structure
    OLD_IFS=${IFS}
    IFS='.'
    IPARRAY=(${TESTIP})
    IFS=${OLDIFS}
    [[ ${IPARRAY[0]} -le 255 && ${IPARRAY[1]} -le 255 && ${IPARRAY[2]} -le 255 && ${IPARRAY[3]} -le 255 ]]
    IPVALID=$?
  fi
  return ${IPVALID}
}

ipDecision() {
  CONFIRMED_HOST=""
  CONFIRMED_IP=""
  if [ "${ZAIOPS_KEYCLOAK_IP}x" == "x" ] || [ "${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}x" == "x" ]
  then
    echo ""
    echo "Hostname and IP address are not fully configured, but are required for certificate configuration."
    echo "You can either specify the values or allow this program to detect them for you."
    echo "Do you want to specify the values yourself? (Y/n)"
    read -e SPECIFY
    SPECIFY=${SPECIFY:-"y"}
  else
    echo ""
    echo "The following values are currently configured for IP address and hostname of this system:"
    echo "  Hostname:   ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}"
    echo "  IP address: ${ZAIOPS_KEYCLOAK_IP}"
    echo ""
    echo "You can either use the configured values for certificate configuration or specify new values."
    echo "Do you want to use the existing values? (Y/n)"
    read -e REUSE
    REUSE=${REUSE:-"y"}
  fi
  if [ "$( echo ${SPECIFY} | tr [:upper:] [:lower:] )x" == "nx" ]
  then
    echo ""
    echo ""
    CHECKIP=true
  elif [ "$( echo ${REUSE} | tr [:upper:] [:lower:] )x" == "yx" ]
  then
    CHECKIP=false
    CONFIRMED_IP=${ZAIOPS_KEYCLOAK_IP}
    CONFIRMED_HOST=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}
  elif [ "$( echo ${SPECIFY} | tr [:upper:] [:lower:] )x" == "yx" ] || [ "$( echo ${REUSE} | tr [:upper:] [:lower:] )x" == "nx" ]
  then
    CHECKIP=false
    echo ""
    echo "Please provide the hostname you want to use:"
    read -e PROVIDED_HOST
    echo "Please provide the matching IP address:"
    read -e PROVIDED_IP
    if valIp ${PROVIDED_IP}
    then
      CONFIRMED_IP=${PROVIDED_IP}
      sed -i -e "s%^ZAIOPS_KEYCLOAK_IP=.*$%ZAIOPS_KEYCLOAK_IP=${CONFIRMED_IP}%g" ${BASEDIR}/.zoa_factory.config
    else
      logToStdout "${ERRORMSG}IP address '${PROVIDED_IP}' is not valid. Please try again.\n"
      exit 1
    fi
    valHost ${PROVIDED_HOST}
    if [ "${HOSTVALID}" == "Y" ]
    then
      CONFIRMED_HOST=$( echo ${PROVIDED_HOST} | tr [:upper:] [:lower:] )
      sed -i -e "s%^ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${CONFIRMED_HOST}%g" ${BASEDIR}/zoa_env.config
      sed -i -e "s%^CDP_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%CDP_KAFKA_BOOTSTRAP_SERVER_HOST=${CONFIRMED_HOST}%g" ${BASEDIR}/zoa_env.config
      sed -i -e "s%^ZAIOPS_KEYCLOAK_HOST=.*$%ZAIOPS_KEYCLOAK_HOST=${CONFIRMED_HOST}%g" ${BASEDIR}/.zoa_factory.config
      sed -i -e "s%^EXTERNAL_GATEWAY_HOST=.*$%EXTERNAL_GATEWAY_HOST=${CONFIRMED_HOST}%g" ${BASEDIR}/.zoa_factory.config
    else
      exit 1
    fi
  else
    logToStdout "${ERRORMSG}You provided an invalid response. Only 'y' or 'n' are permitted."
    exit 1
  fi
}

getProvidedCerts() {
  ORIGIN=$( pwd )
  mkdir -p ${BASEDIR}/tls_tmp
  # Collect require information/artifacts
  declare {TLSPATH,ROOTCERT,INTERMEDCERT,CERTNAME,PKEY,PKEYPASS}="unset"
  INTERMEDCERTNEEDED=true
  if [ -f ${BASEDIR}/tls_tmp/.tmp.tlsconfig ]
  then
    # Temporary TLS configuration file exists; read it
    . ${BASEDIR}/tls_tmp/.tmp.tlsconfig
  fi
  if [ "${ZOA_AUTO_CACRT}" == "true" ]
  then
    TLSPATH=${ZOA_AUTO_TLSPATH}
    CERTNAME=${ZOA_AUTO_CERTNAME}
    PKEY=${ZOA_AUTO_PKEY}
    ROOTCERT=${ZOA_AUTO_ROOTCERT}
    INTERMEDCERT=${ZOA_AUTO_INTERMEDCERT}
    INTERMEDCERTNEEDED=${ZOA_AUTO_INTERMEDCERTNEEDED}
    PKEYPASS=${ZOA_AUTO_PKEYPASS}
  else
    echo "For this process, you will need to provide the following artifacts:"
    echo "  - A CA-signed certificate in PEM format."
    echo "  - The root certificate and -- if provided -- the intermediate certificate"
    echo "    required to validate the authenticity of the CA-signed certificate,"
    echo "    both in PEM format."
    echo "    NOTE: Certificate chains are currently not supported."
    echo "  - The private key used for the CA signing request."
    echo "    Both RSA keys and EC keys are supported."
    echo ""
    echo "If you have all these artifacts available, press any key to continue."
    echo "Otherwise, press Ctrl-C to cancel."
    read -n 1 -s -r
    echo ""
    DEFAULT=${TLSPATH}
    echo "Provide the absolute path to the directory in which the CA-signed"
    echo "TLS artifacts can be found (default: '${DEFAULT}'):"
    read -e TLSPATH
    if [ "${DEFAULT}" != "unset" ]
    then
      TLSPATH=${TLSPATH:-${DEFAULT}}
    fi
    echo ""
    if [ "${TLSPATH}x" == "x" ]
    then
      logToStdout "${ERRORMSG}Your input was empty. Unable to proceed.\n"
      exit 1
    elif [ ! -d ${TLSPATH} ]
    then
      logToStdout "${ERRORMSG}'${TLSPATH}' is not a directory. Unable to proceed.\n"
      exit 1
    elif [ ! -r ${TLSPATH} ]
    then
      logToStdout "${ERRORMSG}'${TLSPATH} is not readable. Unable to proceed.\n"
      exit 1
    else
      echo "TLSPATH=${TLSPATH}" > ${BASEDIR}/tls_tmp/.tmp.tlsconfig
    fi
    DEFAULT=${CERTNAME}
    echo "Provide the name of the CA-signed certificate file (default: '${DEFAULT}'):"
    read -e CERTNAME
    if [ "${DEFAULT}" != "unset" ]
    then
      CERTNAME=${CERTNAME:-${DEFAULT}}
    fi
    echo ""
    if [ "${CERTNAME}x" == "x" ]
    then
      logToStdout "${ERRORMSG}Your input was empty. Unable to proceed.\n"
      exit 1
    elif [ ! -f ${TLSPATH}/${CERTNAME} ]
    then
      logToStdout "${ERRORMSG}'${TLSPATH}/${CERTNAME}' is not a regular file. Unable to proceed.\n"
      exit 1
    else
      echo "CERTNAME=${CERTNAME}" >> ${BASEDIR}/tls_tmp/.tmp.tlsconfig
    fi
    DEFAULT=${PKEY}
    echo "Provide the name of the private key file (default: '${DEFAULT}'):"
    read -e PKEY
    if [ "${DEFAULT}" != "unset" ]
    then
      PKEY=${PKEY:-${DEFAULT}}
    fi
    echo ""
    if [ "${PKEY}x" == "x" ]
    then
      logToStdout "${ERRORMSG}Your input was empty. Unable to proceed.\n"
      exit 1
    elif [ ! -f ${TLSPATH}/${PKEY} ]
    then
      logToStdout "${ERRORMSG}'${TLSPATH}/${PKEY}' is not a regular file. Unable to proceed.\n"
      exit 1
    else
      echo "PKEY=${PKEY}" >> ${BASEDIR}/tls_tmp/.tmp.tlsconfig
    fi
    DEFAULT=${ROOTCERT}
    echo "Provide the name of the CA root certificate file (default: '${DEFAULT}'):"
    read -e ROOTCERT
    if [ "${DEFAULT}" != "unset" ]
    then
      ROOTCERT=${ROOTCERT:-${DEFAULT}}
    fi
    echo ""
    if [ "${ROOTCERT}x" == "x" ]
    then
      logToStdout "${ERRORMSG}Your input was empty. Unable to proceed.\n"
      exit 1
    elif [ ! -f ${TLSPATH}/${ROOTCERT} ]
    then
      logToStdout "${ERRORMSG}'${TLSPATH}/${ROOTCERT}' is not a regular file. Unable to proceed.\n"
      exit 1
    else
      echo "ROOTCERT=${ROOTCERT}" >> ${BASEDIR}/tls_tmp/.tmp.tlsconfig
    fi
    DEFAULT=${INTERMEDCERT}
    echo "If your certification chain requires an intermediate certificate, provide the name"
    echo "of that certificate file (default: '${DEFAULT}'):"
    read -e INTERMEDCERT
    if [ "${DEFAULT}" != "unset" ]
    then
      INTERMEDCERT=${INTERMEDCERT:-${DEFAULT}}
    fi
    echo ""
    if [ "${INTERMEDCERT}x" == "x" ]
    then
      logToStdout "${INFOMSG}You did not provide an intermediate certificate file."
      INTERMEDCERTNEEDED=false
      echo "INTERMEDCERT=${INTERMEDCERT}" >> ${BASEDIR}/tls_tmp/.tmp.tlsconfig
      echo "INTERMEDCERTNEEDED=${INTERMEDCERTNEEDED}" >> ${BASEDIR}/tls_tmp/.tmp.tlsconfig
    else
      if [ ! -f ${TLSPATH}/${INTERMEDCERT} ]
      then
        logToStdout "${ERRORMSG}'${TLSPATH}/${INTERMEDCERT}' is not a regular file. Unable to proceed.\n"
        exit 1
      else
        echo "INTERMEDCERT=${INTERMEDCERT}" >> ${BASEDIR}/tls_tmp/.tmp.tlsconfig
        echo "INTERMEDCERTNEEDED=${INTERMEDCERTNEEDED}" >> ${BASEDIR}/tls_tmp/.tmp.tlsconfig
      fi
    fi
    echo "Provide the password for the private key (if the key is not password protected, press Enter):"
    stty -echo
    read -e PKEYPASS
    stty echo
    if [ "${PKEYPASS}x" == "x" ]
    then
      logToStdout "${INFOMSG}You provided an empty password."
    fi
  fi
  echo "PKEYPASS=${PKEYPASS}" >> ${BASEDIR}/tls_tmp/.tmp.tlsconfig
  echo "${PKEYPASS}" > ${BASEDIR}/tls_tmp/.pw.key
  echo "OLDPASS=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )" > ${BASEDIR}/tls_tmp/.pw.ts
  echo "$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )" > ${BASEDIR}/tls_tmp/.pw.ts.ossl
  genTlsPwd
  echo "${ZOASVC_PASS}" >> ${BASEDIR}/tls_tmp/.pw.key
  echo "NEWPASS=${ZOASVC_PASS}" >> ${BASEDIR}/tls_tmp/.pw.ts
  echo "${ZOASVC_PASS}" >> ${BASEDIR}/tls_tmp/.pw.ts.ossl
  echo "${ZOASVC_PASS}" > ${BASEDIR}/tls_tmp/.pw.ks
  echo "${ZOASVC_PASS}" >> ${BASEDIR}/tls_tmp/.pw.ks
  (
  cat <<- END
#!/bin/bash

INFOMSG="\x1b[32;20mINFO    >> "
ERRORMSG="\x1b[31;20mERROR   >> "
WARNMSG="\x1b[33;20mWARNING >> "
SPACEMSG="           "
ENDMSG="\x1b[0m"

logToStdout() {
  MSG=\${1}
  echo -e "\${MSG}\${ENDMSG}"
}
OSSL_LEGACY=""
OSSL_VERSION=\$( openssl version 2>/dev/null | awk '{ print \$2 }' | tr -d ".a-zA-Z-" )
if [ \${OSSL_VERSION} -gt 200 ]
then  
  OSSL_LEGACY="-legacy"
fi
cd /shared/tls_tmp
# Remove remnants of self-signed certificate as they are no longer needed
rm -f rootCA.key rootCA.srl zoasvc.csr zoasvc.ext
echo ""
echo "Updating TLS artifacts..."
echo ""
# Assign new password to private key for external TLS
openssl rsa -check -in ./zoasvc.key -passin file:.pw.key -noout
RC=$?
if [ \${RC} -eq 0 ]
then
  # This is an RSA key
  openssl rsa -in ./zoasvc.key -passin file:.pw.key -passout file:.pw.key -out ./zoasvc.key.new -aes256
  mv -f ./zoasvc.key.new ./zoasvc.key
else
  # Not an RSA key; try EC
  openssl ec -check -in ./zoasvc.key -passin file:.pw.key -noout
  RC=$?
  if [ \${RC} -eq 0 ]
  then
    openssl ec -in ./zoasvc.key -passin file:.pw.key -passout file:.pw.key -out ./zoasvc.key.new -aes256
    mv -f ./zoasvc.key.new ./zoasvc.key
  else
    echo ""
    logToStdout "\${ERRORMSG}The provided keyfile contains neither an RSA key nor an EC key. Unable to proceed."
    echo ""
    exit 1
  fi
fi
# Assign new password to private key for internal TLS
openssl ec -in ./datastore.key -passin file:.pw.ts.ossl \
  -passout file:.pw.ts.ossl -out ./datastore.key.new \
  -aes256
mv -f ./datastore.key.new ./datastore.key
# Assign new password to security admin key
openssl pkcs8 -inform PEM -outform PEM -in secadmin.key \
  -out secadmin.key.new -topk8 -v1 PBE-SHA1-3DES \
  -passin file:.pw.ts.ossl -passout file:.pw.ts.ossl
mv -f secadmin.key.new secadmin.key
# Import everything into a new external keystore
openssl pkcs12 -export \${OSSL_LEGACY} -out zoasvc.ks -name zoa-services \
  -inkey zoasvc.key -in zoasvc.crt \
  -passin file:.pw.ks -passout file:.pw.ks
# Import everyting into a new internal keystore
openssl pkcs12 -export \${OSSL_LEGACY} -out datastore.ks -name datastore \
  -inkey datastore.key -in datastore.crt \
  -passin file:.pw.ks -passout file:.pw.ks
# Source password file
. .pw.ts
# Change truststore passwords
keytool -storepasswd -keystore zoasvc.ts -new "\${NEWPASS}" \
  -storepass "\${OLDPASS}" -storetype pkcs12
keytool -storepasswd -keystore datastore.ts -new "\${NEWPASS}" \
  -storepass "\${OLDPASS}" -storetype pkcs12
# Remove any old certificates
keytool -delete -alias zoa-services -keystore zoasvc.ts \
  -storepass "\${NEWPASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias zoa-services -keystore datastore.ts \
  -storepass "\${NEWPASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-intermediate -keystore zoasvc.ts \
  -storepass "\${NEWPASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-intermediate -keystore datastore.ts \
  -storepass "\${NEWPASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-root -keystore zoasvc.ts \
  -storepass "\${NEWPASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
keytool -delete -alias ca-root -keystore datastore.ts \
  -storepass "\${NEWPASS}" -storetype pkcs12 2>&1 | grep -v "does not exist"
# Import new certificates
keytool -import -alias zoa-services -noprompt -storetype pkcs12 \
  -keypass "\${NEWPASS}" -storepass "\${NEWPASS}" \
  -keystore zoasvc.ts -trustcacerts \
  -file zoasvc.crt
keytool -import -alias zoa-services -noprompt -storetype pkcs12 \
  -keypass "\${NEWPASS}" -storepass "\${NEWPASS}" \
  -keystore datastore.ts -trustcacerts \
  -file zoasvc.crt
if [ -f intermedCA.crt ]
then
  keytool -import -alias ca-intermediate -noprompt -storetype pkcs12 \
    -keypass "\${NEWPASS}" -storepass "\${NEWPASS}" \
    -keystore zoasvc.ts -trustcacerts \
    -file intermedCA.crt
  keytool -import -alias ca-intermediate -noprompt -storetype pkcs12 \
    -keypass "\${NEWPASS}" -storepass "\${NEWPASS}" \
    -keystore datastore.ts -trustcacerts \
    -file intermedCA.crt
else
  logToStdout "\${INFOMSG}No CA intermediate certificate found."
fi
if [ -f rootCA.crt ]
then
  keytool -import -alias ca-root -noprompt -storetype pkcs12 \
    -keypass "\${NEWPASS}" -storepass "\${NEWPASS}" \
    -keystore zoasvc.ts -trustcacerts \
    -file rootCA.crt
  keytool -import -alias ca-root -noprompt -storetype pkcs12 \
    -keypass "\${NEWPASS}" -storepass "\${NEWPASS}" \
    -keystore datastore.ts -trustcacerts \
    -file rootCA.crt
else
  logToStdout "\${INFOMSG}No CA root certificate found."
fi
# PEM file with all certificates for OpenSearch and OpenSearch Dashboards
openssl pkcs12 -in zoasvc.ts -out zoasvc_all.pem \
  -nokeys -passin file:.pw.ks
# Remove password file
rm -f .pw*
echo ""
echo "Done!"
echo ""
END
  ) > ${BASEDIR}/tls_tmp/caimport.sh
  # Assemble all required parts
  cp ${TLSPATH}/${ROOTCERT} ${BASEDIR}/tls_tmp/rootCA.crt
  cp ${TLSPATH}/${INTERMEDCERT} ${BASEDIR}/tls_tmp/intermedCA.crt
  cp ${TLSPATH}/${CERTNAME} ${BASEDIR}/tls_tmp/zoasvc.crt
  cp ${TLSPATH}/${PKEY} ${BASEDIR}/tls_tmp/zoasvc.key
  cd ${BASEDIR}
  UTILTSTAMP=$( date +"%s" )
  startUtil ${UTILTSTAMP}
  tmpUnpackCerts ${UTILTSTAMP}
  tar cf ${BASEDIR}/tls_tmp.tar tls_tmp && ${OCI_AGENT} cp tls_tmp.tar zaiops-util-${UTILTSTAMP}:/shared && rm -f tls_tmp.tar
  # Run script in utility container
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "tar -C /shared -xf /shared/tls_tmp.tar ; chmod 755 /shared/tls_tmp/*.sh ; rm -f /shared/tls_tmp.tar"
  ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "cd /shared/tls_tmp ; ./caimport.sh"
  tmpRepackCerts ${UTILTSTAMP}
  dumpTLSArtifacts ${UTILTSTAMP}
  stopUtil ${UTILTSTAMP}
  cd ${ORIGIN}
  writeTlsPwd
  # Clean up
  rm -Rf ${BASEDIR}/tls_tmp
  echo ""
}

config-certificates() {
  echo ""
  if [ "$1" == "deploy-generate" ]
  then
    # Initial certificate generation during deployment
    CHECKIP=false
    THISIP=${2}
    THISHOST_LOWER=${3}
    createZoaSvcCert GEN ${CHECKIP} ${THISIP} ${THISHOST_LOWER}
    createZoaSecCert
  elif [ "$1" == "generate" ]
  then
    ipDecision
    if [ "${CHECKIP}" == "false" ]
    then
      createZoaSvcCert GEN ${CHECKIP} ${CONFIRMED_IP} ${CONFIRMED_HOST}
      createZoaSecCert
    else
      createZoaSvcCert GEN ${CHECKIP}
      createZoaSecCert
    fi
  elif [ "$1" == "generate-csr-only" ]
  then
    ipDecision
    if [ "${CHECKIP}" == "false" ]
    then
      createZoaSvcCsr ${CHECKIP} ${CONFIRMED_HOST}
    else
      createZoaSvcCsr ${CHECKIP}
    fi
  elif [ "$1" == "force-generate" ]
  then
    ipDecision
    if [ "${CHECKIP}" == "false" ]
    then
      createZoaSvcCert FORCEGEN ${CHECKIP} ${CONFIRMED_IP} ${CONFIRMED_HOST}
      createZoaSecCert
    else
      createZoaSvcCert FORCEGEN ${CHECKIP}
      createZoaSecCert
    fi
  elif [ "$1" == "use-provided" ]
  then
    getProvidedCerts
  elif [ "$1" == "list-certs" ]
  then
    listTrustedCerts
  elif [ "$1" == "import-cert" ]
  then
    unset ALIAS CERTPATH
    if [ "${ZOA_AUTO_ALIAS}x" == "x" ] || [ "${ZOA_AUTO_CERTPATH}x" == "x" ]
    then
      echo ""
      echo "Provide the full path to the certificate file you wish to import into the truststore:"
      while [[ -z "${CERTPATH}" ]]
      do
        read -e CERTPATH
      done
      echo "Provide an alias to be used for the certificate:"
      while [[ -z "${ALIAS}" ]]
      do
        read -e ALIAS
      done
    else
      CERTPATH="${ZOA_AUTO_CERTPATH}"
      ALIAS="${ZOA_AUTO_ALIAS}"
    fi
    if [ -s "${CERTPATH}" ] && [ -r "${CERTPATH}" ]
    then
      importCert "${CERTPATH}" "${ALIAS}"
    else
      logToStdout "${ERRORMSG}Certificate file ${CERTPATH} missing, empty or not readable."
      exit 1
    fi
  elif [ "$1" == "sync-certs" ]
  then
    syncExternalCertStores
  elif [ "$1" == "delete-cert" ]
  then
    unset ALIAS
    echo ""
    echo "Provide the alias of the certificate you wish to delete from the truststore:"
    while [[ -z "${ALIAS}" ]]
    do
      read -e ALIAS
    done
    deleteTrustedCert ${ALIAS}
  elif [ "$1" == "export" ]
  then
    if [ -f ${BASEDIR}/zoasvc.tls ]
    then
      mkdir -p ${BASEDIR}/ssl_export
      cd ${BASEDIR}/ssl_export
      cat ${BASEDIR}/zoasvc.tls | base64 -d | tar xz
      cd ${BASEDIR}
      echo "TLS artifacts have been extracted to ${BASEDIR}/ssl_export."
    else
      echo "No ${BASEDIR}/zoasvc.tls file found to extract."
    fi
    echo ""
  else
    logToStdout "${ERRORMSG}Missing or unsupported subcommand '${1}'.\n"
    echo "Supported subcommands:"
    echo "  generate | force-generate | generate-csr-only | use-provided | list-certs | import-cert | delete-cert | export"
    echo ""
    exit 1
  fi
}

move-data() {
  OLDVOL=$1
  NEWVOL=$2
  if [ "${OLDVOL}x" = "x" ] || [ "${NEWVOL}x" = "x" ]
  then
    echo "Two parameters (old_volume_name, new_volume_name) are required."
    echo "Switching into interactive mode to collect the parameter values..."
    echo ""
    echo -n "What volume do you want to move the data FROM?  "
    read -e OLDVOL
    if [ "{OLDVOL}x" = "x" ]
    then
      logToStdout "${ERRORMSG}No volume specified. Unable to proceed."
      exit 1
    else
      RESULT=$( ${OCI_AGENT} volume ls -q | grep -cx ${OLDVOL} )
      if [ ${RESULT} -eq 0 ]
      then
        logToStdout "${ERRORMSG}Specified volume does not exist. Unable to proceed."
        exit 1
      elif [ ${RESULT} -gt 1 ]
      then
        logToStdout "${ERRORMSG}More than one volume by the specified name found. Unable to proceed."
        exit 1
      else
        logToStdout "${INFOMSG}Volume ${OLDVOL} found."
      fi
    fi
    echo ""
    echo -n "What volume do you want to move the data TO?  "
    read -e NEWVOL
    if [ "{NEWVOL}x" = "x" ]
    then
      logToStdout "${ERRORMSG}No volume specified. Unable to proceed."
      exit 1
    else
      RESULT=$( ${OCI_AGENT} volume ls -q | grep -cx ${NEWVOL} )
      if [ ${RESULT} -eq 0 ]
      then
        echo -n "Specified volume does not exist. Do you want to create it? (Y/n)  "
        read -e VOLCREATE
        VOLCREATE=${VOLCREATE:-"y"}
        VOLCREATE=$( echo ${VOLCREATE} | tr [:upper:] [:lower:] )
        if [ "${VOLCREATE}" = "y" ]
        then
          ${OCI_AGENT} volume create ${NEWVOL}
        else
          logToStdout "${INFOMSG}You decided not to create the target volume."
          exit 0
        fi
      elif [ ${RESULT} -gt 1 ]
      then
        logToStdout "${ERRORMSG}More than one volume by the specified name found. Unable to proceed."
        exit 1
      else
        logToStdout "${INFOMSG}Volume ${NEWVOL} found."
      fi
    fi
  else
    RESULT=$( ${OCI_AGENT} volume ls -q | grep -cx ${OLDVOL} )
    if [ ${RESULT} -eq 0 ]
    then
      logToStdout "${ERRORMSG}Origin volume '${OLDVOL}' does not exist. Unable to proceed."
      exit 1
    fi
    RESULT=$( ${OCI_AGENT} volume ls -q | grep -cx ${NEWVOL} )
    if [ ${RESULT} -eq 0 ]
    then
      ${OCI_AGENT} volume create ${NEWVOL}
    fi
  fi
  set -a
  . ${BASEDIR}/${NEW_CONFIG}
  . ${BASEDIR}/${IBM_CONFIG}
  set +a
  if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
  then
    export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
  fi
  ARCH=$( uname -m )
  echo "CAUTION: This is a DESTRUCTIVE action. All existing data in the target volume will be overwritten."
  echo -n "         Are you sure you want to continue? (y/N)  "
  read -e COPYVOL
  COPYVOL=${COPYVOL:-"n"}
  COPYVOL=$( echo ${COPYVOL} | tr [:upper:] [:lower:] )
  if [ "${COPYVOL}" == "y" ]
  then
    ${OCI_AGENT} run --rm -d -t -u ${ZOA_UID}:0 --name zaiops-util-${UTILTSTAMP} --entrypoint /bin/bash -v ${OLDVOL}:/from:ro -v ${NEWVOL}:/to ibm-zaiops/zoa-service-discovery:${TAG}-${ARCH} > /dev/null
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "cd /from ; rm -Rf /to/* ; cp -av . /to"
    ${OCI_AGENT} stop zaiops-util-${UTILTSTAMP} > /dev/null
    if [ "${NEWVOL}" == "${COMPOSE_PROJECT_NAME}_zaiops_datastore" ] && [ "$( echo ${ZDAP_ENABLED}x | tr [:lower:] [:upper:] )" == "TRUEX" ] && [ -f ${BASEDIR}/bin/utils/installOSextensions.sh ]
    then
      logToStdout "${INFOMSG}Z Data Analytics Platform is enabled. Need to reload platform extensions."
      ${BASEDIR}/bin/utils/installOSextensions.sh dashboards import 2>&1
    fi
  else
    logToStdout "${INFOMSG}You decided not to copy data to the target volume."
    exit 0
  fi
}

backup-data() {
  SOURCEVOL=$1
  if [ "${SOURCEVOL}x" = "x" ]
  then
    echo "One parameter (source_volume_name) is required."
    echo "Switching into interactive mode to collect the parameter value..."
    echo ""
    echo -n "What volume do you want to collect the data FROM?  "
    read -e SOURCEVOL
    if [ "{SOURCEVOL}x" = "x" ]
    then
      logToStdout "${ERRORMSG}No volume specified. Unable to proceed."
      exit 1
    else
      RESULT=$( ${OCI_AGENT} volume ls -q | grep -cx ${SOURCEVOL} )
      if [ ${RESULT} -eq 0 ]
      then
        logToStdout "${ERRORMSG}Specified volume '${SOURCEVOL}' does not exist. Unable to proceed."
        exit 1
      elif [ ${RESULT} -gt 1 ]
      then
        logToStdout "${ERRORMSG}More than one volume by the specified name found. Unable to proceed."
        exit 1
      else
        logToStdout "${INFOMSG}Volume '${SOURCEVOL}' found."
      fi
    fi
  else
    RESULT=$( ${OCI_AGENT} volume ls -q | grep -cx ${SOURCEVOL} )
    if [ ${RESULT} -eq 0 ]
    then
      logToStdout "${ERRORMSG}Origin volume '${SOURCEVOL}' does not exist. Unable to proceed."
      exit 1
    fi
  fi
  set -a
  . ${BASEDIR}/${NEW_CONFIG}
  . ${BASEDIR}/${IBM_CONFIG}
  set +a
  if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
  then
    export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
  fi
  ARCH=$( uname -m )
  ${OCI_AGENT} run --rm -d -t -u ${ZOA_UID}:0 --name zaiops-util-${UTILTSTAMP} --entrypoint /bin/bash -v ${SOURCEVOL}:/from:ro ibm-zaiops/zoa-service-discovery:${TAG}-${ARCH} > /dev/null
  ${OCI_AGENT} exec zaiops-util-${UTILTSTAMP} bash -c "cd /from ; rm -f /tmp/${SOURCEVOL}.tar.gz ; tar czf /tmp/${SOURCEVOL}.tar.gz * .[a-zA-Z0-9]* --ignore-failed-read"
  mkdir -p ${BASEDIR}/backups
  ${OCI_AGENT} cp zaiops-util-${UTILTSTAMP}:/tmp/${SOURCEVOL}.tar.gz ${BASEDIR}/backups
  ${OCI_AGENT} stop zaiops-util-${UTILTSTAMP} > /dev/null
}

restore-data() {
  SOURCEVOL=$1
  TARGETVOL=$2
  if [ "${SOURCEVOL}x" = "x" ] || [ "${TARGETVOL}x" = "x" ]
  then
    echo "Two parameters (source_volume_name, target_volume_name) are required."
    echo "Switching into interactive mode to collect the parameter values..."
    echo ""
    echo -n "What volume backup do you want to restore the data FROM?  "
    read -e SOURCEVOL
    if [ "{SOURCEVOL}x" = "x" ]
    then
      logToStdout "${ERRORMSG}No volume specified. Unable to proceed."
      exit 1
    else
      if [ ! -f ${BASEDIR}/backups/${SOURCEVOL}.tar.gz ]
      then
        logToStdout "${ERRORMSG}Backup for specified volume '${SOURCEVOL}' does not exist. Unable to proceed."
        exit 1
      else
        logToStdout "${INFOMSG}Backup for Volume '${SOURCEVOL}' found."
      fi
    fi
    echo ""
    echo -n "What volume do you want to restore the data TO?  "
    read -e TARGETVOL
    if [ "{TARGETVOL}x" = "x" ]
    then
      logToStdout "${ERRORMSG}No volume specified. Unable to proceed."
      exit 1
    else
      RESULT=$( ${OCI_AGENT} volume ls -q | grep -cx ${TARGETVOL} )
      if [ ${RESULT} -eq 0 ]
      then
        echo -n "Specified volume '${TARGETVOL}' does not exist. Do you want to create it? (Y/n)  "
        read -e VOLCREATE
        VOLCREATE=${VOLCREATE:-"y"}
        VOLCREATE=$( echo ${VOLCREATE} | tr [:upper:] [:lower:] )
        if [ "${VOLCREATE}" = "y" ]
        then
          ${OCI_AGENT} volume create ${TARGETVOL}
        else
          logToStdout "${INFOMSG}You decided not to create the target volume. Unable to proceed."
          exit 0
        fi
      elif [ ${RESULT} -gt 1 ]
      then
        logToStdout "${ERRORMSG}More than one volume by the specified name found. Unable to proceed."
        exit 1
      else
        logToStdout "${INFOMSG}Volume '${TARGETVOL}' found."
      fi
    fi
  else
    if [ ! -f ${BASEDIR}/backups/${SOURCEVOL}.tar.gz ]
    then
      logToStdout "${ERRORMSG}Backup for specified volume '${SOURCEVOL}' does not exist. Unable to proceed."
      exit 1
    fi
    RESULT=$( ${OCI_AGENT} volume ls -q | grep -cx ${TARGETVOL} )
    if [ ${RESULT} -eq 0 ]
    then
      ${OCI_AGENT} volume create ${TARGETVOL}
    fi
  fi
  set -a
  . ${BASEDIR}/${NEW_CONFIG}
  . ${BASEDIR}/${IBM_CONFIG}
  set +a
  if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
  then
    export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
  fi
  ARCH=$( uname -m )
  echo "CAUTION: This is a DESTRUCTIVE action. All existing data in the target volume will be overwritten."
  echo -n "         Are you sure you want to continue? (y/N)  "
  read -e COPYVOL
  COPYVOL=${COPYVOL:-"n"}
  COPYVOL=$( echo ${COPYVOL} | tr [:upper:] [:lower:] )
  if [ "${COPYVOL}" == "y" ]
  then
    ${OCI_AGENT} run --rm -d -t -u ${ZOA_UID}:0 --name zaiops-util-${UTILTSTAMP} --entrypoint /bin/bash -v ${TARGETVOL}:/to ibm-zaiops/zoa-service-discovery:${TAG}-${ARCH} > /dev/null
    ${OCI_AGENT} cp ${BASEDIR}/backups/${SOURCEVOL}.tar.gz zaiops-util-${UTILTSTAMP}:/tmp/
    ${OCI_AGENT} exec -u root zaiops-util-${UTILTSTAMP} bash -c "tar xzf /tmp/${SOURCEVOL}.tar.gz --same-permissions --same-owner -C /to"
    ${OCI_AGENT} stop zaiops-util-${UTILTSTAMP} > /dev/null
    if [ "${TARGETVOL}" == "${COMPOSE_PROJECT_NAME}_zaiops_datastore" ] && [ "$( echo ${ZDAP_ENABLED}x | tr [:lower:] [:upper:] )" == "TRUEX" ] && [ -f ${BASEDIR}/bin/utils/installOSextensions.sh ]
    then
      logToStdout "${INFOMSG}Z Data Analytics Platform is enabled. Need to reload platform extensions."
      ${BASEDIR}/bin/utils/installOSextensions.sh dashboards import 2>&1
    fi
  else
    logToStdout "${INFOMSG}You decided not to copy data to the target volume."
    exit 0
  fi
}

selectPackage() {
  echo -n "Available packages: "
  echo "${package[@]}"
  while true; do
    read -p "Specify a package: " selection 
    if [[ "${package[@]}" =~ "$selection" ]]; then
        package="$selection"
        break
    fi
    echo "$selection not in list of packages."
  done 
}
