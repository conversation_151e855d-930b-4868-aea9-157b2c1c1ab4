#!/bin/bash

######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2024, 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            k8sManageZoa.sh
#
# Description:     IBM Z AIOps Common Services management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         <PERSON><PERSON>t used to manage IBM Z AIOps Kubernetes-based services
#
# Syntax:          k8sManageZoa.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------
SCRIPTNAME=$( basename ${0} )
SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
cd ${BASEDIR}
RUNDATE=$( date +"%Y-%m-%d_%H.%M.%S" )
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
PRODUCT_INVENTORY=${BASEDIR}/product.inventory
PICLI_DEBUG=""
ARCH=$( uname -m )

command -v host > /dev/null 2>&1
if [ ! $? = 0 ]
then
  hashost=false 
else
  hashost=true
fi

NAMESPACE_COMMON=zoa
COMMON_CONTAINER_PREFIX=${NAMESPACE_COMMON}-
SCRIPT_PREFIX=k8s

umask 0022

set -a
. ${NEW_CONFIG}
. ${IBM_CONFIG}
if [ "${SUBDOMAIN}x" == "x" ]
then
  AUTH_HOST=auth
  DATASTORE_HOST=datastore
  DISCOVERY_HOST=discovery
  GATEWAY_HOST=gateway
  KAFKABROKER_HOST=kafkabroker
  KAFKACONTROLLER_HOST=kafkacontroller
else
  AUTH_HOST=auth.${SUBDOMAIN}
  DATASTORE_HOST=datastore.${SUBDOMAIN}
  DISCOVERY_HOST=discovery.${SUBDOMAIN}
  GATEWAY_HOST=gateway.${SUBDOMAIN}
  KAFKABROKER_HOST=kafkabroker.${SUBDOMAIN}
  KAFKACONTROLLER_HOST=kafkacontroller.${SUBDOMAIN}
fi
if [ -f ${PRODUCT_INVENTORY} ]
then
  . ${PRODUCT_INVENTORY}
fi
set +a
if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
then
  export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
fi

KEY_PASS=$( echo "${ZAIOPS_ZOASVC_PASS}" | base64 -d )
API_PASS=$( echo "${API_PWD}" | base64 -d )
API_USER=$( echo "${API_USR}" | base64 -d )
# Set service and command lists
COMMON_SVC_LIST='@(gateway|auth|discovery|kafkacontroller|kafkabroker)'
COMMON_CMD_LIST='@(up|down|logs|ps|help|start|stop|restart|purge|gather|inspect-images|kafka-topics|kafka-console-consumer|kafka-consumer-groups|kafka-prune|kafka-debug-logging|config-certificates|move-data|backup-data|restore-data|seed-ldap|get-log-level|set-log-level|check-kc-readiness|wait-for-service|setup-k8s-reqs)'

. ${SCRIPTDIR}/utils/common_functions_k8s.sh

if [ "${SCRIPTNAME}" == "k8sManageZoa.sh" ]
then
  OCI_AGENT=kubectl
  DISPLAY_NAME=Kubernetes
  SCRIPT_SUFFIX=k8s
elif [ "${SCRIPTNAME}" == "ocManageZoa.sh" ]
then
  OCI_AGENT=oc
  DISPLAY_NAME="OpenShift Container Platform"
  SCRIPT_SUFFIX=oc
else
  logToStdout "${ERRORMSG}Invalid invocation: ${SCRIPTNAME}. Unable to proceed."
  exit 1
fi

if [ "${ARCH}" != "s390x" ] && [ "${ARCH}" != "x86_64" ]
then
  logToStdout "${ERRORMSG}Unsupported architecture ${ARCH}. Unable to proceed."
  exit 1
fi

for FEATURE in ${INSTALLED_FEATURES}
do
  if [ -f ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh ]
  then
    . ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh
  fi
done

CMD_LIST_TEMP="${COMMON_CMD_LIST}"
for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
do
  eval TEMPVAR=\"\$${UFEATURE}_CMD_LIST\"
  CMD_LIST_TEMP+="|${TEMPVAR}"
done
CMD_LIST='@('$( echo ${CMD_LIST_TEMP} |tr -d '()@' )')'

SVC_LIST_TEMP="${COMMON_SVC_LIST}"
for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
do
  eval TEMPVAR=\"\$${UFEATURE}_SVC_LIST\"
  SVC_LIST_TEMP+="|${TEMPVAR}"
done
SVC_LIST='@('$( echo ${SVC_LIST_TEMP} |tr -d '()@' )')'
SVC_LIST_TRIMMED=$( echo ${SVC_LIST} | tr -d '@()' )
SVC_LIST_SPACES=$( echo ${SVC_LIST_TRIMMED} | tr '|' ' ' )

gatewayUp() {
  # Calculate effective gateway rate limit
  DAC=${DISCOVERY_AGENT_COUNT:-0}
  GRM=${ZAIOPS_GATEWAY_REQUESTS_PER_MIN:-80}
  ZAIOPS_TOTAL_RATE_LIMIT=$( awk -v dac=${DAC} -v grm=${GRM} 'BEGIN { rounded = sprintf("%.0f", (dac+1)*grm*125/100); print rounded }' )
  eval "echo \"$(<${BASEDIR}/k8s-config/gateway.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}gateway
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  eval "echo \"$(<${BASEDIR}/k8s-config/gateway-service.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  if [ "${SCRIPT_SUFFIX}" == "oc" ]
  then
    eval "echo \"$(<${BASEDIR}/k8s-config/zoa-route.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  fi
}

authUp() {
  eval "echo \"$(<${BASEDIR}/k8s-config/auth.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}auth
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
}

discoveryUp() {
  eval "echo \"$(<${BASEDIR}/k8s-config/service-discovery.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}service-discovery
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
}

kafkacontrollerUp() {
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkacontroller.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkacontroller
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
}

kafkabrokerUp() {
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkabroker.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkabroker
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkabroker-service.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  if [ "${SCRIPT_SUFFIX}" == "oc" ]
  then
    eval "echo \"$(<${BASEDIR}/k8s-config/zoakafka-route.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  fi
}

startupRemainder() {
  for SVC in ${SVC_LIST_SPACES}
  do
    # Strip dashes off service names
    SVC=$( echo ${SVC} | tr -d "-" )
    ${SVC}Up
  done
  wait
  for SVC in ${SVC_LIST_SPACES}
  do
    serviceToContainer ${SVC}
  done
}

setup-k8s-reqs() {
  if [ -f ${BASEDIR}/bin/utils/itcCustomizer.sh ]
  then
    . ${BASEDIR}/bin/utils/itcCustomizer.sh
    configRoutePorts
    setStrictPrivacy
  fi
  eval "echo \"$(<${BASEDIR}/k8s-config/zoa-service.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
}

checkHostStorage() {
  MISSING_VOLS=""
  HOST_VOL_LIST="$( grep -A1 hostPath k8s-config/pv.yml | grep path: | cut -f 2 -d '"' )"
  echo ""
  for HOST_VOL in $( echo ${HOST_VOL_LIST} )
  do
    echo "Checking for host volume mount point '${HOST_VOL}'..."
    FOUND_VOL=$( find / -path ${HOST_VOL} -type d -perm 777 2>/dev/null )
    if [ "${FOUND_VOL}x" == "x" ]
    then
      MISSING_VOLS="${MISSING_VOLS} ${HOST_VOL}"
    fi
  done
  if [ "${MISSING_VOLS}x" != "x" ]
  then
    echo ""
    logToStdout "${ERRORMSG}The following host volume mount points are missing or do not have 777 permissions:"
    for MISSING_VOL in $( echo ${MISSING_VOLS} )
    do
      echo "    ${MISSING_VOL}"
    done
    echo ""
    exit 1
  fi
  echo ""
}

up() {
  DO_KEYCLOAK_POSTCONFIG=false
  setup-k8s-reqs
  if [ $# == 0 ]
  then # bring up all services
    DO_KEYCLOAK_POSTCONFIG=true
    # Do two-step startup when all services are started at once to avoid timeouts / resource competition
    discoveryUp
    gatewayUp
    # Wait for gateway service to come up
    printf "%-50s %s" "Waiting for gateway service to come up" "..."
    COUNT=0
    while :
    do
      echo -n .
      ${OCI_AGENT} logs --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}gateway | grep -q "Started PiGatewayApplication" 2>/dev/null
      if [ $? -eq 0 ]
      then
        echo -n " "
        echo "successful after ${COUNT} seconds."
        break
      elif [ ${COUNT} -ge ${SERVICE_TIMEOUT} ]
      then
        echo -n " "
        echo "Gateway service still not ready after ${COUNT} seconds; giving up."
        logToStdout "${ERRORMSG}Service status:"
        echo "--------------------------------------------------------------------------------"
        ${SCRIPTDIR}/k8sManageZoa.sh ps
        echo "--------------------------------------------------------------------------------"
        logToStdout "${ERROR_MSG}Last ${TS_LOG_LINES} lines of service log output:"
        echo "--------------------------------------------------------------------------------"
        dimDisplay
        ${OCI_AGENT} logs --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}gateway | tail -n ${TS_LOG_LINES}
        resetDisplay
        echo "--------------------------------------------------------------------------------"
        exit 1
      else
        sleep 5
        COUNT=$(( ${COUNT} + 5 ))
      fi
    done
    authUp
    # Check for authentication service
    printf "%-50s %s " "Waiting for authentication service to be ready" "..."
    wait-for-service ${AUTH_HOST} 8443
    kafkacontrollerUp
    kafkabrokerUp
    # Check for Kafka service
    printf "%-50s %s " "Waiting for Kafka broker service to be ready" "..."
    wait-for-service ${KAFKABROKER_HOST} 19092
    # Bring up the rest of the services
    startupRemainder
  else # bring up requested services
    echo "$@" | grep -q auth
    if [ $? -eq 0 ]
    then
      DO_KEYCLOAK_POSTCONFIG=true
    fi
    serviceCheck $@
    for SVC in $@
    do
      # Strip dashes off service names
      SVC=$( echo ${SVC} | tr -d "-" )
      ${SVC}Up
    done
    wait
    for SVC in $@
    do
      serviceToContainer ${SVC}
    done
  fi
  # Handle post-startup actions as needed
  # A. Keycloak updates in case of port changes
  if [ "${DO_KEYCLOAK_POSTCONFIG}" == "true" ]
  then
    eval "echo \"$(<${BASEDIR}/k8s-config/auth.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
    waitForPod ${COMMON_CONTAINER_PREFIX}auth
    if [ "${PODREADY}" == "TRUE" ]
    then
      printf "%-50s %s " "Waiting for authentication service to be ready" "..."
      wait-for-service ${AUTH_HOST} 8443
      chmod 755 ${SCRIPTDIR}/utils/keycloak/*.sh
      POD=$( ${OCI_AGENT} get pods --namespace ${NAMESPACE} | grep ${COMMON_CONTAINER_PREFIX}auth | grep Running | awk '{ print $1 }' )
      dimDisplay
      ${OCI_AGENT} cp --namespace ${NAMESPACE} ${SCRIPTDIR}/utils/keycloak/kc_postUp.sh ${POD}:/realm/
      resetDisplay
      if [ "${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}x" == "truex" ]
      then
        KC_TEST_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN}
        KC_TEST_ADMIN_PWD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD}
      else
        KC_TEST_ADMIN=${ZAIOPS_KEYCLOAK_ADMIN}
        KC_TEST_ADMIN_PWD=${ZAIOPS_KEYCLOAK_ADMIN_PASS}
      fi
      ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}auth -- bash -c "/realm/kc_postUp.sh ${KC_TEST_ADMIN} ${EXTERNAL_GATEWAY_HOST} ${ZAIOPS_KEYCLOAK_IP} ${IZOA_GATEWAY_PORT} ${ZAIOPS_KEYCLOAK_PORT} ${KC_TEST_ADMIN_PWD} ${ZAIOPS_ZOASVC_PASS} ${ZAIOPS_KC_CONTEXT_ROOT}"
    fi
  fi
  # Allow features to run "post-up" functions to handle feature-specific needs
  for FEATURE in ${INSTALLED_FEATURES}
  do
    declare -F postUp_${FEATURE} >/dev/null && postUp_${FEATURE} $@
  done
}

down() {
  if [ $# -eq 0 ]
  then # bring down all services
    echo "Shutting down services..."
    PREFIXES="zoa-"
    for FEATURE in ${INSTALLED_FEATURES}
    do
      PREFIXES="${PREFIXES}|${FEATURE}-"
    done
    SERVICES=$( ${OCI_AGENT} get services --namespace ${NAMESPACE} | grep -E '('"${PREFIXES}"')' | awk '{ print $1 }' )
    for SERVICE in ${SERVICES}
    do
      dimDisplay
      ${OCI_AGENT} delete --namespace ${NAMESPACE} services/${SERVICE}
      resetDisplay
    done
    dimDisplay
    ${OCI_AGENT} scale deploy --namespace ${NAMESPACE} --replicas=0 --all
    ${OCI_AGENT} delete deploy --namespace ${NAMESPACE} --all
    ${OCI_AGENT} delete service --namespace ${NAMESPACE} zoa-gateway zoa-kafkabroker
    resetDisplay
  else # bring down requested services
    serviceCheck $@
    echo "Shutting down services..."
    for SVC in $@
    do
      serviceToContainer ${SVC}
      dimDisplay
      ${OCI_AGENT} delete --namespace ${NAMESPACE} services/${containerName} 2>/dev/null
      ${OCI_AGENT} scale deploy --namespace ${NAMESPACE} ${containerName} --replicas=0
      ${OCI_AGENT} delete deploy --namespace ${NAMESPACE} ${containerName}
      if [ "${SVC}" == "gateway" ] || [ "${SVC}" == "kafkabroker" ]
      then
        ${OCI_AGENT} delete service --namespace ${NAMESPACE} zoa-${SVC}
      fi
      resetDisplay
    done
    wait
  fi
}

logs() {
  mkdir -p logs
  if [ $# -eq 0 ]
  then
    for SVC in ${SVC_LIST_SPACES}
    do
      serviceToContainer ${SVC}
      dimDisplay
      ${OCI_AGENT} logs --namespace ${NAMESPACE} deployment/${containerName} | tee -a logs/all-services.stdout
      resetDisplay
    done
  elif [ $# -eq 1 ]
  then
    if [ $1 = "-f" ]
    then
      for SVC in ${SVC_LIST_SPACES}
      do
        serviceToContainer ${SVC}
        ALL_CONTAINERS="${ALL_CONTAINERS} ${containerName}"
      done
      # VBS TODO: Not sure how this will work in K8s
      dimDisplay
      ${OCI_AGENT} logs -f ${ALL_IMAGES} | tee -a logs/all-services.stdout
      resetDisplay
    else
      serviceCheck $1
      serviceToContainer $1
      dimDisplay
      ${OCI_AGENT} logs --namespace ${NAMESPACE} deployment/${containerName} | tee -a logs/${1}.stdout
      resetDisplay
    fi
  elif [ $# -eq 2 ] && [ $1 = "-f" ]
  then
    serviceCheck $2
    serviceToContainer $2
    dimDisplay
    ${OCI_AGENT} logs -f --namespace ${NAMESPACE} deployment/${containerName} | tee -a logs/${2}.stdout
    resetDisplay
  elif [ $# -gt 1 ]
  then
    logToStdout "${ERRORMSG}Cannot specify multiple services to log. Please specify one or none."
    exit
  fi
}

ps() {
  logToStdout "${INFMSG}Pod status:"
  if [ $# -eq 0 ]
  then
    ${OCI_AGENT} get pods --namespace ${NAMESPACE}
  else
    serviceCheck $@
    for SVC in $@
    do
      serviceToContainer ${SVC}
      ${OCI_AGENT} get pods --namespace ${NAMESPACE} | grep ${containerName}
    done
  fi
  echo ""
  logToStdout "${INFMSG}Deployment status:"
  if [ $# -eq 0 ]
  then
    ${OCI_AGENT} get deploy --namespace ${NAMESPACE}
  else
    serviceCheck $@
    for SVC in $@
    do
      serviceToContainer ${SVC}
      ${OCI_AGENT} get deploy --namespace ${NAMESPACE} | grep ${containerName}
    done
  fi
  echo ""
  logToStdout "${INFOMSG}Service status:"
  ${OCI_AGENT} get service --namespace ${NAMESPACE}
  echo ""
  logToStdout "${INFOMSG}Route status:"
  ${OCI_AGENT} get route --namespace ${NAMESPACE}
}

serviceCheck() {
  shopt -s extglob
  for service in "$@"
  do
    case "$service" in
    ${SVC_LIST} ) : ;;
    *)
    logToStdout "${ERRORMSG}No such service: $service"
    echo "List of available services:"
    for ENTRY in ${SVC_LIST_SPACES}
    do
      if [ "${ENTRY}x" != "x" ]
      then
        echo "  ${ENTRY}"
      fi
    done
    exit 1
    ;;
    esac
  done
  shopt -u extglob
}

gather() {
  echo ""
  echo "'gather' function currently not fully supported for Kubernetes."
  echo ""
  mkdir -p logs/support
  echo "Collecting service logs..."
  for ENTRY in ${SVC_LIST_SPACES}
  do
    if [ "${ENTRY}x" != "x" ]
    then
      serviceToContainer ${ENTRY}
      ${OCI_AGENT} logs --namespace ${NAMESPACE} deployment/${containerName} >> logs/${ENTRY}.stdout
    fi
  done

  POD=$( ${OCI_AGENT} get pods --namespace ${NAMESPACE} | grep ${COMMON_CONTAINER_PREFIX}kafkabroker | grep Running | awk '{ print $1 }' )
  if [ "${POD}x" == "x" ]
  then
    logToFileAndStdout "${WARNMSG}No running kafkabroker pod available. Unable to collect Kafka information."
  else
    echo "Collecting Kafka information..."
    kafka-topics --list | grep -v "Waiting for" > logs/support/kafka-topics.log
    kafka-consumer-groups --list | grep -v "Waiting for" > logs/support/kafka-consumer-groups.log
    for GROUP in $( kafka-consumer-groups --list | grep -v "Waiting for" )
    do
      kafka-consumer-groups --describe --group ${GROUP} | grep -v "Waiting for" >> logs/support/kafka-consumer-groups.log
    done
  fi

  #echo "Collecting OCI image information..."
  #inspect-images > logs/support/inspect-images.log
  #echo "Collecting environment information..."
  #free > logs/support/free.log
  #df -k > logs/support/df.log
  #echo "${DISPLAY_NAME} environment information" > logs/support/${OCI_AGENT}Info.log
  #echo "----------------------------------------" >> logs/support/${OCI_AGENT}Info.log
  #echo "'${OCI_AGENT} cluster-info' output:" >> logs/support/${OCI_AGENT}Info.log
  #${OCI_AGENT} cluster-info >> logs/support/${OCI_AGENT}Info.log
  #echo "----------------------------------------" >> logs/support/${OCI_AGENT}Info.log
  #echo "'${OCI_AGENT} version' output:" >> logs/support/${OCI_AGENT}Info.log
  #${OCI_AGENT} version >> logs/support/${OCI_AGENT}Info.log
  #echo "----------------------------------------" >> logs/support/${OCI_AGENT}Info.log
  #cat /etc/*-release > logs/support/linux.log
  #uname -a > logs/support/uname.log
  #ulimit -a > logs/support/ulimit.log
  #ps > logs/support/ps.log

  tar czf support_${RUNDATE}.tar.gz logs ${NEW_CONFIG} ${IBM_CONFIG}
}

purge() {
  if [ "${1}" == "SILENT" ]
  then
    PROCEED="Y"
  fi
  if [ "${PROCEED}" != "Y" ]
  then
    echo ""
    echo "This action will shut down and remove all running IBM Z AIOps containers"
    echo "     and delete the data volumes associated with them."
    echo ""
    echo "Are you sure you want to continue? (y/N)"
    read -e PROCEED
    PROCEED=${PROCEED:-"n"}   # accept no input as "NO"
  fi
  if [ "${PROCEED}" = "Y" ] || [ "${PROCEED}" = "y" ]
  then
    logToStdout "${INFOMSG}Shutting down IBM Z AIOps containers and removing configuration files..."
    down
    echo ""
  else
    logToStdout "${INFOMSG}Not purging IBM Z AIOps data volumes as requested."
  fi
  echo ""
}

kafka-topics() {
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkacontroller.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkacontroller
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkabroker.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkabroker
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}kafkabroker -- /opt/kafka/bin/kafka-topics.sh --bootstrap-server ${KAFKABROKER_HOST}:19092 $@
}

kafka-console-consumer() {
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkacontroller.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkacontroller
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkabroker.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkabroker
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}kafkabroker -- /opt/kafka/bin/kafka-console-consumer.sh --bootstrap-server ${KAFKABROKER_HOST}:19092 $@
}

kafka-consumer-groups() {
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkacontroller.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkacontroller
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkabroker.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkabroker
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${COMMON_CONTAINER_PREFIX}kafkabroker -- /opt/kafka/bin/kafka-consumer-groups.sh --bootstrap-server ${KAFKABROKER_HOST}:19092 $@
}

kafka-prune() {
  PRODUCT=$1
  if [ "${PRODUCT}x" = "x" ]
  then
    echo "Missing parameter: Either ZAA or ZLDA or ZDiscovery must be specified."
    exit 1
  fi
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkacontroller.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkacontroller
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  eval "echo \"$(<${BASEDIR}/k8s-config/kafkabroker.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${COMMON_CONTAINER_PREFIX}kafkabroker
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
  ${SCRIPTDIR}/utils/kafka/kafkaPrune_${SCRIPT_SUFFIX}.sh -p $@
}

seed-ldap() {
  ${SCRIPTDIR}/utils/keycloak/loadKCIds_${SCRIPT_SUFFIX}.sh ${1}
}

update-keycloak-password(){ 
  ${SCRIPTDIR}/utils/keycloak/updateKCpassword_${SCRIPT_SUFFIX}.sh ${1}
}

set-log-level() {
  SLL_ENABLED="gateway"
  for UFEATURE in $( echo ${INSTALLED_FEATURES} | tr [:lower:] [:upper:] )
  do
    eval TEMPVAR=\"\$${UFEATURE}_SETLOGLEVEL\"
    SLL_ENABLED+=" ${TEMPVAR}"
  done
  echo ""
  echo "Services supported for this operation are: ${SLL_ENABLED}"
  echo -n "Specify a service:  "
  read -e service
  serviceToContainer $service
  echo ""

  echo "Log levels supported for this operation are: error, warn, info, debug, trace"
  echo -n "Specify a log level:  "
  read -e logLevel
  if [ -z "$logLevel" -a "$logLevel" == "" ]; then
          echo "Specify the desired log level."
          exit 1
  fi
  levelCheck $logLevel

  if [[ "${#package[@]}" -gt 1 ]]; then
    echo ""
    selectPackage
  fi
  echo ""

  ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/$containerName -- $path $package $logLevel $xml

  echo ""
}

serviceToContainer() {
  if [ -z "$1" -a "$1" == "" ]; then
          echo "Specify the service to work with."
          exit 1
  fi
  serviceCheck $1
  path="/usr/local/bin/changeLogLevel.sh"
  if [[ "$1" == "kafkacontroller" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}kafkacontroller"
  elif [[ "$1" == "kafkabroker" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}kafkabroker"
  elif [[ "$1" == "gateway" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}gateway"
      package="com.ibm.zsystem.zmanaged.piGateway"
  elif [[ "$1" == "auth" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}auth"
  elif [[ "$1" == "discovery" ]]; then
      containerName="${COMMON_CONTAINER_PREFIX}service-discovery"
  else
    for FEATURE in ${INSTALLED_FEATURES}
    do 
      if [ -f ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh ]
      then
        . ${SCRIPTDIR}/utils/${SCRIPT_PREFIX}Manage-${FEATURE}.sh serviceToContainer $1
      fi
    done
  fi
}

# If no command provided, show help; otherwise, run command with arguments
if [ $# -eq 0 ]
then
  help
  exit 0
else
  shopt -s extglob
  case "$1" in
  ${CMD_LIST} ) : ;;
  *)
    logToStdout "${ERRORMSG}Unrecognized command: $1"
    help
    exit 1
    ;;
  esac
  shopt -u extglob
  "$@"
fi
