#!/bin/bash
########################################################## {COPYRIGHT-TOP} ###
## Licensed Materials - Property of IBM
## 5698-LDA (C) Copyright IBM Corp. 2024
## All rights reserved.
## US Government Users Restricted Rights - Use, duplication or
## disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
########################################################## {COPYRIGHT-END} ###

KEYCLOAK_ADMIN=$1
KEYCLOAK_PASS=$2
HOSTNAME=$3
KC_CONTEXT_ROOT=$4

KEYCLOAK_PASS_C=$(echo "${KEYCLOAK_PASS}" | base64 -d)

terminateCliSessions() {
  # Clear any active admin CLI sessions
  ADMIN_CLI_ID=$( /opt/keycloak/bin/kcadm.sh get clients -r master --fields id,clientId --format csv --noquotes  | grep admin-cli | cut -f 1 -d "," )
  SESSION_IDS=$( /opt/keycloak/bin/kcadm.sh get clients/${ADMIN_CLI_ID}/user-sessions -r master --fields id --format csv --noquotes )
  for SESSION_ID in ${SESSION_IDS}
  do
    /opt/keycloak/bin/kcadm.sh delete sessions/${SESSION_ID} -r master
  done
}

terminateCliSessions > /dev/null 2>&1

# Establish a fresh admin CLI session
/opt/keycloak/bin/kcadm.sh config credentials --server http://${HOSTNAME}:8080/${KC_CONTEXT_ROOT} --realm master --user ${KEYCLOAK_ADMIN} --password ${KEYCLOAK_PASS_C} > /dev/null 2>&1

# Check whether Keycloak is ready to return information
# Sleep for up to 120 seconds to allow requested process to complete
count=0
while :
do 
  REALM=$( /opt/keycloak/bin/kcadm.sh get realms/master --fields realm --format csv --noquotes 2>/dev/null )
  if [ "${REALM}x" == "masterx" ]
  then
    break
  elif [ ${count} -ge 120 ]
  then
    break
  else
    sleep 5
    count=$(( ${count} + 5 ))
  fi
done

terminateCliSessions > /dev/null 2>&1

if [ "${REALM}x" == "masterx" ]
then
  echo "PASSED"
else
  echo "FAILED"
fi
