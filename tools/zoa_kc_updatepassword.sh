#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM 
# 5698-LDA (C) Copyright IBM Corp. 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

KEYCLOAK_ADMIN=$1
ZAIOPS_KEYCLOAK_ADMIN_PASS=$2
REALM_ADMIN=$3
KC_CONTEXT_ROOT=$4
SERVER=http://${KC_CONTEXT_ROOT}:8080
KEYCLOAK_ADMIN_PASSWORD=$( echo "${ZAIOPS_KEYCLOAK_ADMIN_PASS}" | base64 -d )

KCUSERID=${5}
NEWPW=$(< /tmp/.pw)

declare -A userFedIds

ENDMSG="\x1b[0m"
logToStdout() {
  MSG=${1}
  echo -e "${MSG}${ENDMSG}"
}

disable_federations() {
  # Check if there are any enabled federations.  If so, disable them.
  userFeds=$( /opt/keycloak/bin/kcadm.sh get components -r IzoaKeycloak -q type="org.keycloak.storage.UserStorageProvider" --fields id,name,providerId --format csv --noquotes )
  for i in ${userFeds}
  do
    userFedId=$( echo "${i}" | cut -f 1 -d "," )
    userFedName=$( echo "${i}" | cut -f 2 -d "," )
    userFedProvider=$( echo "${i}" | cut -f 3 -d "," )
    if [ "${OSTYPE}" == "OS/390" ]
    then
      # We are under z/OS UNIX System Services; Keycloak returns JSON output as garbage ... and field positions in CSV output are not guaranteed
      #   need to attempt to get information via grep
      /opt/keycloak/bin/kcadm.sh get components/${userFedId} -r IzoaKeycloak --format csv | grep -q \"true\"
      if [ $? == 0 ]
      then
        fedEnabled=true
      else
        fedEnabled=false
      fi
    else
      fedEnabled=$( /opt/keycloak/bin/kcadm.sh get components/${userFedId} -r IzoaKeycloak 2>/dev/null | grep \"enabled\" | head -1 | cut -f 4 -d "\"" )
    fi
    if [ "${fedEnabled}" == "true" ]
    then
      logToStdout "${INFOMSG}Disabling the ${userFedName} user federation"
      userFedIds[${userFedName}]=${userFedId}
      /opt/keycloak/bin/kcadm.sh update components/${userFedId} -r IzoaKeycloak -s 'config.enabled=["false"]'
    fi
  done
}

enable_federations() {
  for key in "${!userFedIds[@]}"
  do
    logToStdout "${INFOMSG}Enabling ${key} user federation"
    /opt/keycloak/bin/kcadm.sh update components/${userFedIds[$key]} -r IzoaKeycloak -s 'config.enabled=["true"]'
  done
}


# Main process
/opt/keycloak/bin/kcadm.sh config credentials --server ${SERVER}/${KC_CONTEXT_ROOT} --realm master --user ${KEYCLOAK_ADMIN} --password ${KEYCLOAK_ADMIN_PASSWORD}
if [ $? -eq 0 ]
then
  if [ "${KCUSERID}" = "${KEYCLOAK_ADMIN}" ]
  then
    /opt/keycloak/bin/kcadm.sh set-password -r master --username ${KCUSERID} --new-password ${NEWPW}
  else
    # Check whether IzoaKeycloak realm exists
    REALMTEST=$(/opt/keycloak/bin/kcadm.sh get realms/IzoaKeycloak --fields realm --format csv --noquotes 2>/dev/null)
    if [ "${REALMTEST}x" == "x" ]
    then
      logToStdout "${ERRORMSG}realms/IzoaKeycloak does not exist.  Unable to proceed."
      exit 1
    fi

    if [ "${KCUSERID}" = "${REALM_ADMIN}" ]
    then
      # Temporarily disable any user federations so we can create some stand-alone ids
      disable_federations

      ID=$( /opt/keycloak/bin/kcadm.sh get users -r IzoaKeycloak -q username=${REALM_ADMIN} --fields id --format csv --noquotes )
      if [ "${ID}x" = "x" ]
      then
        # Create realm administrator ID
        logToStdout "${INFOMSG}Creating user ${REALM_ADMIN}..."
        ID=$( /opt/keycloak/bin/kcadm.sh create users -s username=${REALM_ADMIN} -s enabled=true -r IzoaKeycloak -i 2>&1)
        if [ $? != 0 ]
        then
          logToStdout "${ERRORMSG}Unable to create '${REALM_ADMIN}' in realm 'IzoaKeycloak'"
          exit 1
        fi
        /opt/keycloak/bin/kcadm.sh update users/${ID} -r IzoaKeycloak -s "email=${REALM_ADMIN}@zoa.biz" -s "firstName=${REALM_ADMIN}" -s "lastName=built-in"
        for ROLE in $( /opt/keycloak/bin/kcadm.sh get-roles -r IzoaKeycloak --cclientid realm-management --fields name --format csv --noquotes )
        do
          /opt/keycloak/bin/kcadm.sh remove-roles --uusername ${REALM_ADMIN} --cclientid realm-management --rolename ${ROLE} -r IzoaKeycloak
          /opt/keycloak/bin/kcadm.sh add-roles --uusername ${REALM_ADMIN} --cclientid realm-management --rolename ${ROLE} -r IzoaKeycloak
        done
      fi

      /opt/keycloak/bin/kcadm.sh set-password -r IzoaKeycloak --username ${REALM_ADMIN} --new-password ${NEWPW}
      
      # Re-enable any previously disabled user federations
      enable_federations
    else
      /opt/keycloak/bin/kcadm.sh set-password -r IzoaKeycloak --username ${KCUSERID} --new-password ${NEWPW}
    fi
  fi
else
  logToStdout "${ERRORMSG}Unable to access the master realm with provided credentials"
fi