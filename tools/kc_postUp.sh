#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2023
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

INFOMSG="\x1b[32;20mINFO    >> "
ERRORMSG="\x1b[31;20mERROR   >> "
WARNMSG="\x1b[33;20mWARNING >> "
SPACEMSG="           "
ENDMSG="\x1b[0m"

logToStdout() {
  MSG=${1}
  echo -e "${MSG}${ENDMSG}"
}

SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
KEYCLOAK_ADMIN=$1
ZAIOPS_KEYCLOAK_HOST=$2
ZAIOPS_KEYCLOAK_IP=$3
IZOA_GATEWAY_PORT=$4
KEYCLOAK_PORT=$5
ZAIOPS_KEYCLOAK_ADMIN_PASS=$6
TRUSTSTORE_PASS=$7
KEYCLOAK_ADMIN_PASSWORD=$( echo "${ZAIOPS_KEYCLOAK_ADMIN_PASS}" | base64 -d )
TRUSTSTORE_PASS_C=$( echo "${TRUSTSTORE_PASS}" | base64 -d )
TRUSTSTORE=/ssl/zoasvc.ts.p12
SERVER=http://auth:8080
KC_CONTEXT_ROOT=$8

terminateCliSessions() {
  # Clear any active admin CLI sessions
  ADMIN_CLI_ID=$( /opt/keycloak/bin/kcadm.sh get clients -r master --fields id,clientId --format csv --noquotes  | grep admin-cli | cut -f 1 -d "," )
  SESSION_IDS=$( /opt/keycloak/bin/kcadm.sh get clients/${ADMIN_CLI_ID}/user-sessions -r master --fields id --format csv --noquotes )
  for SESSION_ID in ${SESSION_IDS}
  do
    /opt/keycloak/bin/kcadm.sh delete sessions/${SESSION_ID} -r master
  done
}

# Make sure that Content-Security-Policy is updated in case of a port change
#/opt/keycloak/bin/kcadm.sh config truststore --trustpass "${TRUSTSTORE_PASS_C}" ${TRUSTSTORE}
/opt/keycloak/bin/kcadm.sh config credentials --server ${SERVER}/${KC_CONTEXT_ROOT} --realm master --user ${KEYCLOAK_ADMIN} --password ${KEYCLOAK_ADMIN_PASSWORD}
for REALM in master IzoaKeycloak
do
  REALMTEST=$(/opt/keycloak/bin/kcadm.sh get realms/${REALM} --fields realm --format csv --noquotes 2>/dev/null)
  if [ "${REALMTEST}x" == "x" ]
  then
    logToStdout "${INFOMSG}Realm '${REALM}' does not exist."
  else
    logToStdout "${INFOMSG}Validating Content-Security-Policy for realm '${REALM}'..."
    /opt/keycloak/bin/kcadm.sh update realms/${REALM} -s browserSecurityHeaders."contentSecurityPolicy"="frame-src 'self'; frame-ancestors https://${ZAIOPS_KEYCLOAK_HOST}:${IZOA_GATEWAY_PORT} https://${ZAIOPS_KEYCLOAK_HOST}:${KEYCLOAK_PORT}; object-src 'none'"
    if [ "${REALM}" == "IzoaKeycloak" ]
    then
      if [ "${IZOA_GATEWAY_PORT}" == "443" ]
      then
        WEB_ORIGINS="\"https://${ZAIOPS_KEYCLOAK_HOST}:${IZOA_GATEWAY_PORT}\",\
                     \"https://${ZAIOPS_KEYCLOAK_HOST}:${KEYCLOAK_PORT}\",\
                     \"https://${ZAIOPS_KEYCLOAK_IP}:${IZOA_GATEWAY_PORT}\",\
                     \"https://${ZAIOPS_KEYCLOAK_IP}:${KEYCLOAK_PORT}\",\
                     \"https://${ZAIOPS_KEYCLOAK_HOST}\",\
                     \"https://${ZAIOPS_KEYCLOAK_IP}\""
      else
        WEB_ORIGINS="\"https://${ZAIOPS_KEYCLOAK_HOST}:${IZOA_GATEWAY_PORT}\",\
                     \"https://${ZAIOPS_KEYCLOAK_HOST}:${KEYCLOAK_PORT}\",\
                     \"https://${ZAIOPS_KEYCLOAK_IP}:${IZOA_GATEWAY_PORT}\",\
                     \"https://${ZAIOPS_KEYCLOAK_IP}:${KEYCLOAK_PORT}\""
      fi
      WEB_ORIGINS=$(echo "${WEB_ORIGINS}" | tr -d " ")
      for CLIENT in zoa-client account account-console
      do
      logToStdout "${INFOMSG}Validating settings for client '${CLIENT}' in realm '${REALM}'..."
        CLIENTTEST=$(/opt/keycloak/bin/kcadm.sh get clients -r IzoaKeycloak -q clientId=${CLIENT} --fields id --format csv --noquotes)
        if [ "${CLIENTTEST}x" != "x" ]
        then
          /opt/keycloak/bin/kcadm.sh update clients/${CLIENTTEST} -r IzoaKeycloak -s 'webOrigins=['${WEB_ORIGINS}']'
          if [ "${CLIENT}" == "zoa-client" ]
          then
            /opt/keycloak/bin/kcadm.sh update clients/${CLIENTTEST} -r IzoaKeycloak -s "baseUrl=https://${ZAIOPS_KEYCLOAK_HOST}:${IZOA_GATEWAY_PORT}/piFramework"
          fi
        fi
      done
    fi
  fi
done

terminateCliSessions > /dev/null 2>&1
