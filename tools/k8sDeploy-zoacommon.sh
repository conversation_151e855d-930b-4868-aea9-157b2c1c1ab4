#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2024
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            k8sDeploy-zoacommon.sh
#
# Description:     IBM Z AIOps Common Services installation
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Installation script used to install or uninstall IBM Z AIOps
#                  Common Services feature
#
# Syntax:          Called from k8sDeployZoa.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

IMGFILE_ZOACOMMON=ZOACOMMON-k8s-resources.tar.gz

checkPrereqsInstallZOACOMMON() {
  logToFileAndStdout "${INFOMSG}There are no special installation prerequisites for the Z Operational Analytics common components."
}

checkPrereqsRuntimeZOACOMMON() {
  logToFileAndStdout "${INFOMSG}There are no special runtime prerequisites for the Z Operational Analytics common components."
} 

checkPrereqsRemoveZOACOMMON() {
  logToFileAndStdout "${INFOMSG}There are no special uninstallation prerequisites for the Z Operational Analytics common components."
} 

migrateConfigZOACOMMON() {
  DCPATH=${1}
  if [ -f ${DCPATH}/${LEGACY_CONFIG}.old ]
  then
    . ${DCPATH}/${LEGACY_CONFIG}.old
  fi
  if [ -f ${DCPATH}/${OLD_CONFIG} ]
  then
    . ${DCPATH}/${OLD_CONFIG}
  fi
  # List of ZOACOMMON variables
  VARLIST_ZOACOMMON="CEM_PASSWORD \
    CEM_URL \
    CEM_USER \
    EVENTAPI_PASSWORD \
    EVENTAPI_URL \
    EVENTAPI_USER \
    EVENTS_ENABLED \
    FORWARD_MESSAGES \
    NOTIFICATION_EMAIL_FROM_ADDRESS \
    NOTIFICATION_EMAIL_FROM_NAME \
    NOTIFICATION_EMAIL_HOST \
    NOTIFICATION_EMAIL_PASSWORD \
    NOTIFICATION_EMAIL_PORT \
    NOTIFICATION_EMAIL_SSL_ENABLED \
    NOTIFICATION_EMAIL_SUBJECT_PREFIX \
    NOTIFICATION_EMAIL_TO_ADDRESS \
    NOTIFICATION_EMAIL_USERNAME \
    PI_CONSOLE_LOG_FORMAT \
    PI_CONSOLE_LOG_LEVEL \
    PI_TRACE_LOG_LEVEL \
    ZAA_KPI_ANOMALY_AR_ENABLED \
    ZAA_LOG_ANOMALY_ALERTS_ENABLED \
    ZAIOPS_DATASTORE_HEAP \
    ZAIOPS_GATEWAY_REQUESTS_PER_MIN \
    ZCHATOPS_AUTH_URL \
    ZCHATOPS_CHANNEL \
    ZCHATOPS_INCIDENT_URL \
    ZCHATOPS_PASSWORD \
    ZCHATOPS_USER"
  for VAR in ${VARLIST_ZOACOMMON}
  do
    eval VALUE=\"\$${VAR}\"
    if [ "${VALUE}x" = "x" ]
    then # do nothing; value is empty
      :
    elif [[ "${VALUE}" =~ .*\ .* ]]
    then # handle variable values with spaces
      sed -i -e "s%^${VAR}=.*$%${VAR}=\"${VALUE}\"%g" ${DCPATH}/${NEW_CONFIG}.all
    else # handle 'single-word' variable values
      sed -i -e "s%^${VAR}=.*$%${VAR}=${VALUE}%g" ${DCPATH}/${NEW_CONFIG}.all
    fi
  done
}

k8sUnpackZOACOMMON() {
  DCPATH=${1}
  cd ${SCRIPTDIR}/../zoacommon
  echo "Restoring Z Operational Analytics common services resources from archive..." | tee -a ${LOGFILE}
  echo "" | tee -a ${LOGFILE}
  tar xzf ${IMGFILE_ZOACOMMON} -C ${DCPATH} 2>&1 | tee -a ${LOGFILE}
  # Append Z Operational Analytics common services config files to overall config files
  echo "" >> ${DCPATH}/zoa_env.config.all
  cat ${DCPATH}/zoa_env.config >> ${DCPATH}/zoa_env.config.all
  rm -f ${DCPATH}/zoa_env.config
  echo "" >> ${DCPATH}/.zoa_factory.config.all
  cat ${DCPATH}/.zoa_factory.config >> ${DCPATH}/.zoa_factory.config.all
  rm -f ${DCPATH}/.zoa_factory.config
}

k8sInstallZOACOMMON() {
  # NOTE: For Kubernetes and OpenShift, images are assumed to have been loaded -- or to be accessed directly -- from originating repository prior
  #       to running deployment script
  DCPATH=${1}
  cd ${DCPATH}
  # Copy this script into install directory 
  mkdir -p ${DCPATH}/bin/utils
  cp ${SCRIPTDIR}/../zoacommon/utils/k8sDeploy-zoacommon.sh ${DCPATH}/bin/utils/
}

k8sConfigZOACOMMON() {
  DCPATH=${1}
  logToFileAndStdout "${INFOMSG}Applying basic security configuration..."
  ${DCPATH}/bin/k8sManageZoa.sh up datastore | tee -a ${LOGFILE}
  if [ "$( echo "${RESET_DATASTORE_SECURITY_DURING_UPGRADE}" | tr [:upper:] [:lower:] )" == "true" ] || [ "${MIGRATE_CONFIG}x" != "TRUEx" ]
  then
    ${DCPATH}/bin/k8sManageZoa.sh reset-datastore-security | tee -a ${LOGFILE}
  else
    logToFileAndStdout "${INFOMSG}This is an upgrade installation, and a forced reset of the datastore security configuration is not required."
  fi
  ${DCPATH}/bin/k8sManageZoa.sh reset-datastore-api-password | tee -a ${LOGFILE}
  ${DCPATH}/bin/k8sManageZoa.sh down | tee -a ${LOGFILE}
}  

k8sRemoveZOACOMMON() {
  # NOTE: As with the installation, removal assumes that images are handled outside of the deployment script
  #       for Kubernetes and OpenShift
  DCPATH=${1}
  echo "" | tee -a ${LOGFILE}
  if [ "${DCPATH}x" == "x" ]
  then
    DCPATH="$( cd ${SCRIPTDIR}/.. && pwd )"
  fi
  if [ "${RMRESPONSE}x" == "x" ]
  then
    while [ "${RMRESPONSE}" != "Y" ] && [ "${RMRESPONSE}" != "y" ] &&
          [ "${RMRESPONSE}" != "N" ] && [ "${RMRESPONSE}" != "n" ]
    do
      echo "You are about to remove all Z Operational Analytics common services functions."
      echo "Are you sure you want to continue? (y/N)"
      read -e RMRESPONSE
      RMRESPONSE=${RMRESPONSE:-"n"}   # accept no input as "NO"
    done
  fi
  if [ "${RMRESPONSE}" = "Y" ] || [ "${RMRESPONSE}" = "y" ]
  then
    echo "No actions to perform for 'remove' command." | tee -a ${LOGFILE}
    echo "Done." | tee -a ${LOGFILE}
  fi
}

prereqsZOACOMMON() {
  # Anything to handle for ZOACOMMON prereqs
  echo "There are no unique prerequisites for the Z Operational Analytics common services." | tee -a ${LOGFILE}
}     
    
unpackZOACOMMON() {
  # Unpack ZOACOMMON package
  k8sUnpackZOACOMMON ${DCPATH}
}   
  
installZOACOMMON() {
  # Anything to handle for ZOACOMMON installation
  k8sInstallZOACOMMON ${DCPATH}
}

configZOACOMMON() {
  # Anything to handle for ZOACOMMON configuration
  k8sConfigZOACOMMON ${DCPATH}
}   
    
removeZOACOMMON() {
  # Anything to handle for ZOACOMMON removal
  k8sRemoveZOACOMMON ${DCPATH}
}   
    
cleanZOACOMMON() {
  # Anything to handle for ZOACOMMON cleanup
  echo "There are no unique cleanup actions for the Z Operational Analytics common services." | tee -a ${LOGFILE}
}     

## MAIN
ARG=${1}
case "${ARG}" in
  "prereqs" )
    prereqsZOACOMMON
    ;;
  "unpack" )
    unpackZOACOMMON
    ;;
  "install" )
    installZOACOMMON
    ;;
  "config" )
    configZOACOMMON
    ;;
  "remove" )
    removeZOACOMMON
    ;;
  "clean" )
    cleanZOACOMMON
    ;;
  "checkPrereqsInstall" )
    checkPrereqsInstallZOACOMMON
    ;;
  "checkPrereqsRuntime" )
    checkPrereqsRuntimeZOACOMMON
    ;;
  "checkPrereqsRemove" )
    checkPrereqsRuntimeZOACOMMON
    ;;
  "migrateConfig" )
    migrateConfigZOACOMMON ${2}
esac
