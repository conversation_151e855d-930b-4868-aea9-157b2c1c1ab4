#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2021, 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            dockerDeploy-zoacommon.sh
#
# Description:     IBM Z AIOps Common Services installation
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Installation script used to install or uninstall IBM Z AIOps
#                  Common Services feature
#
# Syntax:          Called from dockerDeployZoa.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

IMGFILE_ZOACOMMON=__IMGFILE__

checkPrereqsInstallZOACOMMON() {
  logToFileAndStdout "${INFOMSG}There are no special installation prerequisites for the Z Operational Analytics common components."
}

checkPrereqsRuntimeZOACOMMON() {
  logToFileAndStdout "${INFOMSG}There are no special runtime prerequisites for the Z Operational Analytics common components."
} 
      
checkPrereqsRemoveZOACOMMON() {
  logToFileAndStdout "${INFOMSG}There are no special uninstallation prerequisites for the Z Operational Analytics common components."
} 

migrateConfigZOACOMMON() {
  DCPATH=${1}
  if [ -f ${DCPATH}/${LEGACY_CONFIG}.old ]
  then
    . ${DCPATH}/${LEGACY_CONFIG}.old
  fi
  if [ -f ${DCPATH}/${OLD_CONFIG} ]
  then
    . ${DCPATH}/${OLD_CONFIG}
  fi
  # List of ZOACOMMON variables
  VARLIST_ZOACOMMON="CEM_PASSWORD \
    CEM_URL \
    CEM_USER \
    EVENTAPI_PASSWORD \
    EVENTAPI_URL \
    EVENTAPI_USER \
    EVENTS_ENABLED \
    FORWARD_MESSAGES \
    NOTIFICATION_EMAIL_FROM_ADDRESS \
    NOTIFICATION_EMAIL_FROM_NAME \
    NOTIFICATION_EMAIL_HOST \
    NOTIFICATION_EMAIL_PASSWORD \
    NOTIFICATION_EMAIL_PORT \
    NOTIFICATION_EMAIL_SSL_ENABLED \
    NOTIFICATION_EMAIL_SUBJECT_PREFIX \
    NOTIFICATION_EMAIL_TO_ADDRESS \
    NOTIFICATION_EMAIL_USERNAME \
    PI_CONSOLE_LOG_FORMAT \
    PI_CONSOLE_LOG_LEVEL \
    PI_TRACE_LOG_LEVEL \
    ZAA_KPI_ANOMALY_AR_ENABLED \
    ZAA_LOG_ANOMALY_ALERTS_ENABLED \
    ZAIOPS_DATASTORE_HEAP \
    ZAIOPS_GATEWAY_REQUESTS_PER_MIN \
    ZCHATOPS_AUTH_URL \
    ZCHATOPS_CHANNEL \
    ZCHATOPS_INCIDENT_URL \
    ZCHATOPS_PASSWORD \
    ZCHATOPS_USER"
  for VAR in ${VARLIST_ZOACOMMON}
  do
    eval VALUE=\"\$${VAR}\"
    if [ "${VALUE}x" = "x" ]
    then # do nothing; value is empty
      :
    elif [[ "${VALUE}" =~ .*\ .* ]]
    then # handle variable values with spaces
      sed -i -e "s%^${VAR}=.*$%${VAR}=\"${VALUE}\"%g" ${DCPATH}/${NEW_CONFIG}.all
    else # handle 'single-word' variable values
      sed -i -e "s%^${VAR}=.*$%${VAR}=${VALUE}%g" ${DCPATH}/${NEW_CONFIG}.all
    fi
  done
}

processPIextensions() {
  cd ${DCPATH}
  if [ -d ${SCRIPTDIR}/../piserver/images ]
  then
    IMGDIR="$( cd ${SCRIPTDIR}/../piserver/images && pwd )"
    ls -1 ${IMGDIR}/*tar > /dev/null 2>&1
    if [ $? -eq 0 ]
    then
      echo "" | tee -a ${LOGFILE}
      echo "Populating Problem Insights service with extensions..." | tee -a ${LOGFILE}
      echo "" | tee -a ${LOGFILE}
      ./bin/dockerManageZoa.sh up piserver
      for FILE in `find ${IMGDIR} -name "*.tar"`
      do
        BASENAME=`basename ${FILE}`
        if [ "${ENABLED_PI_EXTENSIONS}x" != "x" ]; then
          if ! echo "${ENABLED_PI_EXTENSIONS}" | grep -q "${BASENAME}"; then
            echo "Excluding ${BASENAME}" | tee -a ${LOGFILE}
            continue
          fi
        fi
        echo "Processing extension ${BASENAME}..." | tee -a ${LOGFILE}
        docker cp ${FILE} zoa-piserver:/opt/ibm/staging
        docker exec -u root zoa-piserver bash -c "chmod 644 /opt/ibm/staging/*.tar"
        docker exec zoa-piserver bash -c "tar -C /opt/ibm/piserver -xf /opt/ibm/staging/${BASENAME}"
      done
      ./bin/dockerManageZoa.sh reconfig-insight-packs NIA
      echo -n "Stopping "
      docker stop zoa-piserver
      echo -n "Removing "
      docker rm zoa-piserver
      echo "Done." | tee -a ${LOGFILE}
      echo "" | tee -a ${LOGFILE}
    fi
  fi
}

dockerUnpackZOACOMMON() {
  DCPATH=${1}
  cd ${SCRIPTDIR}/../zoacommon
  echo "Restoring Z Operational Analytics common services images from archive..." | tee -a ${LOGFILE}
  echo "" | tee -a ${LOGFILE}
  tar xzf ${IMGFILE_ZOACOMMON} -C ${DCPATH} 2>&1 | tee -a ${LOGFILE}
  # Append Z Operational Analytics common services config files to overall config files
  echo "" >> ${DCPATH}/zoa_env.config.all
  cat ${DCPATH}/zoa_env.config >> ${DCPATH}/zoa_env.config.all
  rm -f ${DCPATH}/zoa_env.config
  echo "" >> ${DCPATH}/.zoa_factory.config.all
  cat ${DCPATH}/.zoa_factory.config >> ${DCPATH}/.zoa_factory.config.all
  rm -f ${DCPATH}/.zoa_factory.config
}

dockerInstallZOACOMMON() {
  DCPATH=${1}
  cd ${SCRIPTDIR}/../zoacommon

  # Remove any old OCI images
  echo "Removing old Z Operational Analytics common services images from local repository..." | tee -a ${LOGFILE}
  export RMRESPONSE=Y
  dockerRemoveZOACOMMON ${DCPATH}
  unset RMRESPONSE
  echo ""
  # Restore OCI images
  cd ${DCPATH}
  echo "Loading new Z Operational Analytics common services images into local repository..." | tee -a ${LOGFILE}
  STATUS=SUCCESS
  docker load -q -i zoacommon_images.tar >> ${LOGFILE} 2>&1
  RC=$?
  if [ ${RC} -eq 0 ]
  then
    echo "Successfully loaded images." | tee -a ${LOGFILE}
    rm -f zoacommon_images.tar
    for IMG in $(docker images -f "label=feature=IBM Z AIOps - Common Services - Z Operational Analytics" -q)
    do
      TAGS=`docker inspect --format '{{ index .RepoTags }}' ${IMG} | tr -d "[]"`
      IMG=`echo ${TAGS} | cut -f 3 -d "/"`
      IMGNAME=`echo ${IMG} | cut -f 1 -d ":"`
      IMGTAG=`echo ${IMG} | cut -f 2 -d ":"`
      # Re-tag images for a more user-friendly naming convention
      docker tag icr.io/zoa-oci/${IMGNAME}:${IMGTAG} ibm-zaiops/${IMGNAME}:${IMGTAG} >> ${LOGFILE} 2>&1
      docker rmi icr.io/zoa-oci/${IMGNAME}:${IMGTAG} >> ${LOGFILE} 2>&1
    done
  else
    logToFileAndStdout "${ERRORMSG}Failed to load images. See ${LOGFILE} for details."
    STATUS=FAILURE
  fi
  # Update docker-compose.yml
  sed -i -e "s%icr.io/zoa-oci%ibm-zaiops%g" ${DCPATH}/zoacommon-docker-compose.yml
  # Copy this script into install directory
  mkdir -p ${DCPATH}/bin/utils
  cp ${SCRIPTDIR}/../zoacommon/utils/dockerDeploy-zoacommon.sh ${DCPATH}/bin/utils/
  # Summarize status
  if [ "${STATUS}" == "SUCCESS" ]
  then
    echo "" | tee -a ${LOGFILE}
    echo "All Z Operational Analytics common services images were loaded successfully." | tee -a ${LOGFILE}
  else
    echo "" | tee -a ${LOGFILE}
    echo "Loading of at least one image failed." | tee -a ${LOGFILE}
    echo "The Z Operational Analytics common services were not successfully installed." | tee -a ${LOGFILE}
    echo "Please investigate and resolve the issue and re-run this script before attempting to use the feature." | tee -a ${LOGFILE}
    echo "" | tee -a ${LOGFILE}
    exit 1
  fi
  ENABLED_PI_EXTENSIONS=""
  for FEATURE in ${FEATURES}
  do
    . ${SCRIPTDIR}/../${FEATURE}/utils/dockerDeploy-${FEATURE}.sh getPiExtensions
    if [ "${PI_EXTENSIONS}x" != "x" ]
    then
      ENABLED_PI_EXTENSIONS+="${PI_EXTENSIONS} "
    fi
  done
}

dockerConfigZOACOMMON() {
  DCPATH=${1}
  logToFileAndStdout "${INFOMSG}Applying basic security configuration..."
  ${DCPATH}/bin/dockerManageZoa.sh up datastore | tee -a ${LOGFILE}
  if [ "$( echo "${RESET_DATASTORE_SECURITY_DURING_UPGRADE}" | tr [:upper:] [:lower:] )" == "true" ] || [ "${MIGRATE_CONFIG}x" != "TRUEx" ]
  then
    ${DCPATH}/bin/dockerManageZoa.sh reset-datastore-security | tee -a ${LOGFILE}
  else
    logToFileAndStdout "${INFOMSG}This is an upgrade installation, and a forced reset of the datastore security configuration is not required."
  fi
  ${DCPATH}/bin/dockerManageZoa.sh reset-datastore-api-password | tee -a ${LOGFILE}
  ${DCPATH}/bin/dockerManageZoa.sh down | tee -a ${LOGFILE}
  # processPIextensions
}

configPISERVER() {
  processPIextensions
}

dockerRemoveZOACOMMON() {
  DCPATH=${1}
  echo "" | tee -a ${LOGFILE}
  if [ "${DCPATH}x" == "x" ]
  then
    DCPATH="$( cd ${SCRIPTDIR}/.. && pwd )"
  fi
  if [ "${RMRESPONSE}x" == "x" ]
  then
    while [ "${RMRESPONSE}" != "Y" ] && [ "${RMRESPONSE}" != "y" ] &&
          [ "${RMRESPONSE}" != "N" ] && [ "${RMRESPONSE}" != "n" ]
    do
      echo "You are about to remove all Z Operational Analytics common services images from the local Docker repository."
      echo "Are you sure you want to continue? (y/N)"
      read -e RMRESPONSE
      RMRESPONSE=${RMRESPONSE:-"n"}   # accept no input as "NO"
    done
  fi
  if [ "${RMRESPONSE}" = "Y" ] || [ "${RMRESPONSE}" = "y" ]
  then
    echo "Removing OCI images..." | tee -a ${LOGFILE}
    IMGLIST=$(docker images -f "label=feature=IBM Z AIOps - Common Services - Z Operational Analytics" -q)
    if [ "${IMGLIST}" = "" ]
    then
      echo "No images to remove." | tee -a ${LOGFILE}
    else
      docker rmi -f $(docker images -f "label=feature=IBM Z AIOps - Common Services - Z Operational Analytics" -q) 2>&1 | tee -a ${LOGFILE}
    fi
    echo "Done." | tee -a ${LOGFILE}
  fi
}

prereqsZOACOMMON() {
  # Anything to handle for ZOACOMMON prereqs
  echo "There are no unique prerequisites for the Z Operational Analytics common services." | tee -a ${LOGFILE}
}     
      
unpackZOACOMMON() {
  # Unpack ZOACOMMON package
  dockerUnpackZOACOMMON ${DCPATH}
}

installZOACOMMON() {
  # Anything to handle for ZOACOMMON installation
  dockerInstallZOACOMMON ${DCPATH}
}

configZOACOMMON() {
  # Anything to handle for ZOACOMMON configuration
  dockerConfigZOACOMMON ${DCPATH}
}

removeZOACOMMON() {
  # Anything to handle for ZOACOMMON removal
  dockerRemoveZOACOMMON ${DCPATH}
}

cleanZOACOMMON() {
  # Anything to handle for ZOACOMMON cleanup
  echo "There are no unique cleanup actions for the Z Operational Analytics common services." | tee -a ${LOGFILE}
}

getPiExtensionsZOACOMMON() {
  PI_EXTENSIONS=""
}


## MAIN
ARG=${1}
case "${ARG}" in
  "prereqs" )
    prereqsZOACOMMON
    ;;
  "unpack" )
    unpackZOACOMMON
    ;;
  "install" )
    installZOACOMMON
    ;;
  "getPiExtensions" )
    getPiExtensionsZOACOMMON
    ;;
  "config" )
    configZOACOMMON
    ;;
  "piserver" )
    configPISERVER
    ;;
  "remove" )
    removeZOACOMMON
    ;;
  "clean" )
    cleanZOACOMMON
    ;;
  "checkPrereqsInstall" )
    checkPrereqsInstallZOACOMMON
    ;;
  "checkPrereqsRuntime" )
    checkPrereqsRuntimeZOACOMMON
    ;;
  "checkPrereqsRemove" )
    checkPrereqsRuntimeZOACOMMON
    ;;
  "migrateConfig" )
    migrateConfigZOACOMMON ${2}
esac
