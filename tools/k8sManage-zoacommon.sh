#!/bin/bash

######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2024
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            k8sManageZoa.sh
#
# Description:     IBM Z AIOps Common Services management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         <PERSON><PERSON>t used to manage IBM Z AIOps Kubernetes-based services
#
# Syntax:          k8sManageZoa.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

NAMESPACE_COMMON=zoacommon

ZOACOMMON_CONTAINER_PREFIX=zoa-

set -a
. ${BASEDIR}/${NEW_CONFIG}
. ${BASEDIR}/${IBM_CONFIG}
set +a
if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
then
  export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
fi

KEY_PASS=$( echo "${ZAIOPS_ZOASVC_PASS}" | base64 -d )
API_PASS=$( echo "${API_PWD}" | base64 -d )
API_USER=$( echo "${API_USR}" | base64 -d )

. ${SCRIPTDIR}/utils/common_functions_k8s.sh

datastoreUp() {
  eval "echo \"$(<${BASEDIR}/k8s-config/datastore.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  waitForPod ${ZOACOMMON_CONTAINER_PREFIX}datastore
  if [ "${PODREADY}" == "FALSE" ]
  then
    exit 1
  fi
}

helpZOACOMMON() {
  echo -e "Commands available for Z Operational Analytics common components:"
  echo -e "  datastore [ list-indices | delete-indices | show-locked-indices | unlock-index |"
  echo -e "              list-datastreams | delete-datastreams ]"
  echo -e "                             Work with indices and datastreams in the datastore service"
  echo -e "  datastore-debug-logging [ enable | disable ]"
  echo -e "                             Enable or disable debug logging for the datastore service"
  echo -e "  reset-datastore-security   Initialize IBM-provided default security configuration for the datastore"
  echo -e "  reset-datastore-api-password"
  echo -e "                             Create a new randomly generated password for datastore API access"
} 

listDatastreams() {
  PATTERN="${1}"
  DS_RETURN="NONE"
  echo ""
  if [ "${PATTERN}" == "<EMPTY>" ]
  then
    DS_LIST=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} https://${DATASTORE_HOST}:9200/_data_stream?pretty 2>/dev/null" | grep \"name\"\ :.*\,$ | cut -f 2 -d ":" | tr -d ' ,"' )
  else
    DS_LIST=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} https://${DATASTORE_HOST}:9200/_data_stream?pretty 2>/dev/null" | grep \"name\"\ :.*\,$ | grep -E '('${PATTERN}')' | cut -f 2 -d ":" | tr -d ' ,"' )
  fi
  if [ "$( echo "${DS_LIST}" )x" == "x" ]
  then
    echo "No data streams matching Pattern '${PATTERN}' were found."
  else
    echo "${DS_LIST}" | more
    DS_RETURN="${DS_LIST}"
  fi
}

listIndices() {
  PATTERN="${1}"
  EXCLUSION_LIST="security-auditlog|\.kib|\.opendistro|\.opensearch|\.ql-datasources|\.replication-metadata-store|\.geospatial-ip2geo-data"
  IDX_RETURN="NONE"
  echo ""
  if [ "${PATTERN}" == "<EMPTY>" ]
  then
    IDX_LIST=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} https://${DATASTORE_HOST}:9200/_cat/indices?v 2>/dev/null | grep -vE '(${EXCLUSION_LIST})'" )
  else
    IDX_LIST=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} https://${DATASTORE_HOST}:9200/_cat/indices?v 2>/dev/null | grep -E '(docs.count|${PATTERN})' | grep -vE '(${EXCLUSION_LIST})'" )
  fi
  if [ "$( echo "${IDX_LIST}" | grep -v docs.count )x" == "x" ]
  then
    echo "No indices matching Pattern '${PATTERN}' were found."
  else
    echo "${IDX_LIST}" | more
    IDX_RETURN="${IDX_LIST}"
  fi
  echo ""
}

deleteDatastreams() {
  DS_LIST="${1}"
  echo ""
  for DS in ${DS_LIST}
  do
    DEL_RESULT="$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} -X DELETE https://${DATASTORE_HOST}:9200/_data_stream/${DS}" 2>/dev/null )"
    if [ "$( echo "${DEL_RESULT}" | grep "acknowledged:true" )x" == "x" ]
    then
      echo "  Deletion acknowledged for Data Stream '${DS}'."
    else
      echo "  Deletion of Data Stream '${DS}' not successful."
    fi
  done
  echo ""
}

deleteIndices() {
  INDEX_LIST="${1}"
  INDEX_LIST="$( echo "${INDEX_LIST}" | grep -v docs.count | awk '{ print $3 }' )"
  echo ""
  for INDEX in ${INDEX_LIST}
  do
    DEL_RESULT="$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} -X DELETE https://${DATASTORE_HOST}:9200/${INDEX}" 2>/dev/null )"
    if [ "$( echo "${DEL_RESULT}" | grep "acknowledged:true" )x" == "x" ]
    then
      echo "  Deletion acknowledged for Index '${INDEX}'."
    else
      echo "  Deletion of Index '${INDEX}' not successful."
    fi
  done
  echo ""
}

unlockIndex() {
  IDX_NAME=${1}
  UNLOCK_RESULT="$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "cd /usr/share/opensearch/config/tls ; curl -s -k --cert ./secadmin.crt --cacert ./internalCA.crt --key ./secadmin.key --pass ${KEY_PASS} -X PUT https://${DATASTORE_HOST}:9200/${IDX_NAME}/_settings -H 'Content-Type: application/json' -d'{ \"index.blocks.read_only_allow_delete\" : null, \"index.blocks.read_only\" : false }'" )"
  if [ "$( echo "${UNLOCK_RESULT}" | grep "acknowledged:true" )x" == "x" ]
  then
    echo "  Unlocking acknowledged for Index '${IDX_NAME}'."
  else
    echo "  Unlocking of Index '${IDX_NAME}' not successful."
  fi
}

datastore() {
  echo ""
  if [ "$1" == "show-locked-indices" ]
  then
    FULL_LIST=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} https://${DATSTORE_HOST}:9200/_cluster/state/blocks?pretty" )
    WHITESPACE=$( echo "${FULL_LIST}" | grep -A1 \"indices\" | grep -v \"indices\" | cut -f 1 -d "\"" )
    SHORT_LIST=$( echo "${FULL_LIST}" | grep ^"$WHITESPACE"\" | cut -f 2 -d "\"" )
    for INDEX in ${SHORT_LIST}
    do  
      echo "  ${INDEX}"
    done
    echo ""
  elif [ "$1" == "unlock-index" ]
  then
    echo "Provide the name of the index to unlock."
    read -e -p '>>> ' IDX_TO_UNLOCK
    if [ "${IDX_TO_UNLOCK}x" != "x" ]
    then
      unlockIndex ${IDX_TO_UNLOCK}
      echo ""
    else 
      logToStdout "${ERRORMSG}No index name provided. Unable to proceed.\n"
      exit 1
    fi
  elif [ "$1" == "list-datastreams" ]
  then
    PATTERN='<EMPTY>'
    echo "Provide a pattern of data stream names to list, or simply press <ENTER> to list all data streams."
    echo "A pattern can be a full or partial data stream name, or a regular expression containing parts of a"
    echo "  data stream name and wildcards in the format '.*'"
    read -e -p '>>> ' LIST_PATTERN
    if [ "${LIST_PATTERN}x" != "x" ]
    then
      PATTERN="${LIST_PATTERN}"
    fi
    listDatastreams "${PATTERN}"
    echo ""
  elif [ "$1" == "list-indices" ]
  then
    PATTERN='<EMPTY>'
    echo "Provide a pattern of index names to list, or simply press <ENTER> to list all data indices."
    echo "A pattern can be a full or partial index name, or a regular expression containing parts of an"
    echo "  index name and wildcards in the format '.*'"
    read -e -p '>>> ' LIST_PATTERN
    if [ "${LIST_PATTERN}x" != "x" ]
    then
      PATTERN="${LIST_PATTERN}"
    fi
    listIndices "${PATTERN}"
    echo ""
  elif [ "$1" == "delete-datastreams" ]
  then
    if [ "${DEL_PATTERN_EXT}x" == "x" ]
    then
      echo "Provide a pattern of data stream names to delete."
      echo "A pattern can be a full or partial data stream name, or a regular expression containing parts of a"
      echo "  data stream name and wildcards in the format '.*'"
      read -e -p '>>> ' DEL_PATTERN
      if [ "${DEL_PATTERN}x" == "x" ]
      then 
        logToStdout "\n\n${INFOMSG}No pattern provided. No datastore data streams will be deleted.\n"
        exit 0
      else 
        echo ""
        echo ""
        echo "CAUTION: The following datastore data streams will be deleted:"
        echo ""
        listDatastreams "${DEL_PATTERN}"
        if [ "${DS_RETURN}" == "NONE" ]
        then 
          echo ""
          exit 0
        fi
        echo ""
        echo "Are you sure you want to proceed? (y/N)"
        read -e DS_DEL
        DS_DEL=${DS_DEL:-"n"}
        DS_DEL=$( echo ${DS_DEL} | tr [:upper:] [:lower:] )
      fi
    else
      DEL_PATTERN="${DEL_PATTERN_EXT}"
      # Perform deletion without confirmation if pattern was externally provided
      DS_DEL=y
    fi
    if [ "${DS_DEL}" == "y" ]
    then
      if [ "${DEL_PATTERN_EXT}x" != "x" ]
      then
        listDatastreams "${DEL_PATTERN}"
      fi
      deleteDatastreams "${DS_RETURN}"
      echo ""
    else
      logToStdout "\n${INFOMSG}You decided not to delete the datastore data streams matching the provided pattern.\n"
      exit 0
    fi
  elif [ "$1" == "delete-indices" ]
  then
    if [ "${DEL_PATTERN_EXT}x" == "x" ]
    then
      echo "Provide a pattern of index names to delete."
      echo "A pattern can be a full or partial index name, or a regular expression containing parts of an"
      echo "  index name and wildcards in the format '.*'"
      read -e -p '>>> ' DEL_PATTERN
      if [ "${DEL_PATTERN}x" == "x" ]
      then
        logToStdout "\n\n${INFOMSG}No pattern provided. No datastore indices will be deleted.\n"
        exit 0
      else
        echo ""
        echo ""
        echo "CAUTION: The following datastore indices will be deleted:"
        echo ""
        listIndices "${DEL_PATTERN}"
        if [ "${IDX_RETURN}" == "NONE" ]
        then
          echo ""
          exit 0
        fi
        echo ""
        echo "Are you sure you want to proceed? (y/N)"
        read -e IDX_DEL
        IDX_DEL=${IDX_DEL:-"n"}
        IDX_DEL=$( echo ${IDX_DEL} | tr [:upper:] [:lower:] )
      fi
    else
      DEL_PATTERN="${DEL_PATTERN_EXT}"
      # Perform deletion without confirmation if pattern was externally provided
      IDX_DEL=y
    fi
    if [ "${IDX_DEL}" == "y" ]
    then
      if [ "${DEL_PATTERN_EXT}x" != "x" ]
      then
        listIndices "${DEL_PATTERN}"
      fi
      deleteIndices "${IDX_RETURN}"
      echo ""
    else
      logToStdout "\n${INFOMSG}You decided not to delete the datastore indices matching the provided pattern.\n"
      exit 0
    fi
  else
    logToStdout "${ERRORMSG}Missing or unsupported subcommand '${1}'.\n"
    echo "Supported subcommands:"
    echo "  list-indices | delete-indices | show-locked-indices | unlock-index | list-datastreams | delete-datastreams"
    echo ""
    exit 1
  fi
}

checkDatastoreStatus() {
  # Sleep for up to ${SERVICE_TIMEOUT} seconds to give datastore a chance to come up
  DSREADY="FALSE"
  waitForPod ${ZOACOMMON_CONTAINER_PREFIX}datastore
  if [ "${PODREADY}" == "TRUE" ]
  then
    echo -n "Waiting for datastore service to be ready "
    count=5
    while :
    do
      echo -n .
      ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "wait-for-it.sh --quiet --progress --strict --timeout=5 ${DATASTORE_HOST}:9200" | grep -v "command terminated"
      if [ ${PIPESTATUS[0]} -eq 0 ]
      then
        echo -n " "
        echo "successful after ${count} seconds."
        DSREADY="TRUE"
        break
      elif [ ${count} -ge ${SERVICE_TIMEOUT} ]
      then
        echo -n " "
        echo "Datastore service still not ready after ${count} seconds; giving up."
        logToStdout "${ERRORMSG}Service status:"
        echo "--------------------------------------------------------------------------------"
        ${SCRIPTDIR}/k8sManageZoa.sh ps
        echo "--------------------------------------------------------------------------------"
        logToStdout "${ERRORMSG}Last ${TS_LOG_LINES} lines of service log output:"
        echo "--------------------------------------------------------------------------------"
        dimDisplay
        ${OCI_AGENT} logs --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore | tail -n ${TS_LOG_LINES}
        resetDisplay
        echo "--------------------------------------------------------------------------------"
        break
      else
        count=$(( ${count} + 5 ))
      fi
    done
  fi
}

copy-os-users() {
  ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "if [ -f /usr/share/os_misc/internal_users.yml ] ; then rm -f /usr/share/os_misc/internal_users.yml ; fi ; cp /usr/share/opensearch/config/opensearch-security/internal_users.yml /usr/share/os_misc/"
}

reset-datastore-security() {
  echo ""
  eval "echo \"$(<${BASEDIR}/k8s-config/datastore.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  checkDatastoreStatus
  if [ "${DSREADY}" == "TRUE" ]
  then
    echo "Initializing Z Operational Analytics datastore default security configuration..."
    SECUPDATE=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "cd /usr/share/opensearch/config/opensearch-security ; /usr/share/opensearch/plugins/opensearch-security/tools/securityadmin.sh -cert ../tls/secadmin.crt -cacert ../tls/internalCA.crt -key ../tls/secadmin.key -keypass ${KEY_PASS}" )
    echo "${SECUPDATE}" | grep -q ERR
    if [ $? -eq 0 ]
    then
      echo ""
      logToStdout "${ERRORMSG}Security configuration update was not successful. Details:"
      echo "-----------------------------------------------------------------"
      echo "${SECUPDATE}"
      echo "-----------------------------------------------------------------"
      echo ""
      exit 1
    else
      echo "${SECUPDATE}" | grep -q "Done with success"
      if [ $? -eq 0 ]
      then
        echo ""
        logToStdout "${INFOMSG}Security configuration update completed successfully."
        echo ""
      else
        echo ""
        logToStdout "${WARNMSG}Unexpected security configuration update result. Details:"
        echo "------------------------------------------------------------------"
        echo "${SECUPDATE}"
        echo "------------------------------------------------------------------"
        echo ""
      fi
    fi
  fi
  echo ""
}

reset-datastore-api-password() {
  echo ""
  eval "echo \"$(<${BASEDIR}/k8s-config/datastore.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  checkDatastoreStatus
  if [ "${DSREADY}" == "TRUE" ]
  then
    echo "Generating new API password..."
    NEW_API_PWD_PREFIX=$( < /dev/urandom tr -dc A-Za-z 2>/dev/null | head -c${1:-1};echo; )
    NEW_API_PWD_MAIN=$( < /dev/urandom tr -dc A-Za-z0-9@?^-_ 2>/dev/null | head -c${1:-15};echo; )
    NEW_API_PWD=${NEW_API_PWD_PREFIX}${NEW_API_PWD_MAIN}
    NEW_API_PWD_B=$( echo "${NEW_API_PWD}" | base64 )
    NEW_API_PWD_HASH=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "/usr/share/opensearch/plugins/opensearch-security/tools/hash.sh -p ${NEW_API_PWD} 2>/dev/null" | sed -e "s%\/%\\\\/%g" )
    SECUPDATE=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "cd /usr/share/opensearch/config/opensearch-security ; sed -i '/^${API_USER}:/,+1s/hash:.*/hash: \"${NEW_API_PWD_HASH}\"/' internal_users.yml ; sed -i '/^zdapui:/,+1s/hash:.*/hash: \"${NEW_API_PWD_HASH}\"/' internal_users.yml ; /usr/share/opensearch/plugins/opensearch-security/tools/securityadmin.sh -cert ../tls/secadmin.crt -cacert ../tls/internalCA.crt -key ../tls/secadmin.key -keypass ${KEY_PASS} -f internal_users.yml" )
    echo "${SECUPDATE}" | grep -q ERR
    if [ $? -eq 0 ]
    then
      echo ""
      logToStdout "${ERRORMSG}Security configuration update was not successful. Details:"
      echo "-----------------------------------------------------------------"
      echo "${SECUPDATE}"
      echo "-----------------------------------------------------------------"
      echo ""
      exit 1
    else
      echo "${SECUPDATE}" | grep -q "Done with success"
      if [ $? -eq 0 ]
      then
        echo ""
        copy-os-users
        logToStdout "${INFOMSG}Security configuration update completed successfully."
        sed -i -e "s%^API_PWD=.*$%API_PWD=${NEW_API_PWD_B}%g" ${BASEDIR}/.zoa_factory.config
        echo ""
        echo "IMPORTANT: You must shut down and restart all containers for the API password change to be fully implemented."
        echo "           Failure to do so will disrupt communication between the ZOA application components."
        echo ""
      else
        echo ""
        logToStdout "${WARNMSG}Unexpected security configuration update result. Details:"
        echo "------------------------------------------------------------------"
        echo "${SECUPDATE}"
        echo "------------------------------------------------------------------"
        echo ""
      fi
    fi
  fi
  echo ""
}

datastore-debug-logging() {
  ACTION=$1
  eval "echo \"$(<${BASEDIR}/k8s-config/datastore.yml)\"" | ${OCI_AGENT} apply -f - > /dev/null
  checkDatastoreStatus
  if [ "${DSREADY}" == "TRUE" ]
  then
    if [ "${ACTION}x" == "x" ]
    then
      logToStdout "${ERRORMSG}No action specified. Unable to proceed."
      exit 1
    fi
    if [ "$( echo ${ACTION} | tr [:upper:] [:lower:] )" == "enable" ]
    then
      ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} -X PUT https://${DATASTORE_HOST}:9200/_cluster/settings -H 'Content-Type: application/json' -d'{ \"transient\" : { \"logger.org.opensearch\" : \"${ZOA_DEBUG_LEVEL}\" } }'" > /dev/null
    elif [ "$( echo ${ACTION} | tr [:upper:] [:lower:] )" == "disable" ]
    then
      ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} -X PUT https://${DATASTORE_HOST}:9200/_cluster/settings -H 'Content-Type: application/json' -d'{ \"transient\" : { \"logger.org.opensearch\" : null } }'" > /dev/null
    else
      logToStdout "${ERRORMSG}Invalid action specified. Only 'enable' and 'disable' are supported."
      exit 1
    fi
  else
    logToStdout "${ERRORMSG}Datastore service must be started before running this command."
    exit 1
  fi
}

getCurBucketCap() { 
  CURLIMIT=$( ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -s -k -u ${API_USER}:${API_PASS} https://${DATASTORE_HOST}:9200/_cluster/settings?pretty 2>/dev/null | grep max_buckets | awk '{ print $3 }' | tr -d '\"'" )       
  if [ "${CURLIMIT}x" = "x" ]
  then  
    CURLIMIT="not explicitly set; default value: 65535"
  fi    
  echo "Current setting of max_buckets: ${CURLIMIT}"
  echo ""
}

postUp_zoacommon() {
  DO_DATASTORE_SCALE=false
  if [ $# == 0 ]
  then 
    DO_DATASTORE_SCALE=true
  else 
    for SVC in $@
    do
      if [ "${SVC}" == "datastore" ]
      then
        DO_DATASTORE_SCALE=true
      fi
    done 
  fi
  # Handle post-startup actions as needed
  if [ "${DO_DATASTORE_SCALE}" == "true" ]
  then
    # Apply datastore scalability settings:
    #   ZOA_DATASTORE_MAX_SEARCH_BUCKETS
    valNatNum ZOA_DATASTORE_MAX_SEARCH_BUCKETS ${ZOA_DATASTORE_MAX_SEARCH_BUCKETS} 1
    valLow ZOA_DATASTORE_MAX_SEARCH_BUCKETS ${ZOA_DATASTORE_MAX_SEARCH_BUCKETS} 65535
    valHigh ZOA_DATASTORE_MAX_SEARCH_BUCKETS ${ZOA_DATASTORE_MAX_SEARCH_BUCKETS} 99999
    # Set search bucket cap
    echo "Search bucket limit before update:"
    getCurBucketCap
    ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -X PUT -s -k -u ${API_USER}:${API_PASS} https://${DATASTORE_HOST}:9200/_cluster/settings?pretty -H 'Content-Type: application/json' -d '{ \"persistent\": { \"search.max_buckets\": \"'${ZOA_DATASTORE_MAX_SEARCH_BUCKETS}'\" } }'" >/dev/null 2>&1
    ${OCI_AGENT} exec --namespace ${NAMESPACE} deployment/${ZOACOMMON_CONTAINER_PREFIX}datastore -- bash -c "curl -X PUT -s -k -u ${API_USER}:${API_PASS} https://${DATASTORE_HOST}:9200/_cluster/settings?pretty -H 'Content-Type: application/json' -d '{ \"persistent\": { \"cluster.default_number_of_replicas\": '${ZOA_DATASTORE_DEFAULT_REPLICA_COUNT}' } }'" >/dev/null 2>&1
    echo "Search bucket limit after update:"
    getCurBucketCap
  fi
}

serviceToContainerZOACOMMON() {
  if [[ "$1" == "datastore" ]]; then
    containerName="${ZOACOMMON_CONTAINER_PREFIX}datastore"
  fi
}

##MAIN
ZOACOMMON_SVC_LIST='@(datastore)'
ZOACOMMON_CMD_LIST='@(datastore|datastore-debug-logging|reset-datastore-security|reset-datastore-api-password)'
  
ARG=${1}
case "${ARG}" in
  "help" )
    helpZOACOMMON
    ;;
  "serviceToContainer" )
    serviceToContainerZOACOMMON ${2}
    ;;
esac
