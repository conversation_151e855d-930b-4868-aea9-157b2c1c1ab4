#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2023
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            migrateKC.sh
#
# Description:     IBM Z Data Analytics platform installation
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         <PERSON><PERSON><PERSON> used to migrate IBM Z Data Analytics
#                  platform integration into Keycloak authentication
#                  service from Keycloak v12.0.4 to v21.x or later
#
# Syntax:          migrateKC.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

INFOMSG="\x1b[32;20mINFO    >> "
ERRORMSG="\x1b[31;20mERROR   >> "
WARNMSG="\x1b[33;20mWARNING >> "
SPACEMSG="           "
ENDMSG="\x1b[0m"

logToStdout() {
  MSG=${1}
  echo -e "${MSG}${ENDMSG}"
}

SCRIPTDIR="$( cd "$( dirname "$0" )/../.." && pwd )"
SCRIPTNAME="$( basename "$0" )"
BASEDIR=$( dirname ${SCRIPTDIR} )
ARCH=$( uname -m )
LEGACY_CONFIG=.env
OLD_CONFIG=zoa_env.config.old
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
KCTAG=12.0.4
DELETE_NETWORK=false

set -a
if [ -f ${BASEDIR}/${NEW_CONFIG} ]
then
  . ${BASEDIR}/${NEW_CONFIG}
  . ${BASEDIR}/${IBM_CONFIG}
elif [ -f ${BASEDIR}/${NEW_CONFIG}.all ]
then
  . ${BASEDIR}/${NEW_CONFIG}.all
  . ${BASEDIR}/${IBM_CONFIG}.all
elif [ -f ${BASEDIR}/${OLD_CONFIG} ]
then
  . ${BASEDIR}/${OLD_CONFIG}
elif [ -f ${BASEDIR}/${LEGACY_CONFIG}.old ]
then
  . ${BASEDIR}/${LEGACY_CONFIG}.old
fi  
set +a
if [ "${OCI_AGENT}" = "docker" ]
then
  if [ ${OCI_VERSION} -ge 20 ]
  then
    COMMON_CONTAINER_PREFIX=zoa-
  else
    COMMON_CONTAINER_PREFIX=""
  fi
elif [ "${OCI_AGENT}" = "podman" ]
then
  COMMON_CONTAINER_PREFIX=""
fi
if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
then
  export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
fi

usage() {
  echo ""
  echo "Usage:"
  echo "  migrateKC.sh --export | -e               to export realm and user data from an authentication service implementation prior to Fix Level 13"
  echo "  migrateKC.sh --import | -i               to import realm and user data into an authentication service implementation at Fix Level 13 or later"
  echo ""
}

checkKcImpl() {
  ${OCI_AGENT} images | grep ibm-zaiops | grep -q zoa-auth
  if [ $? -eq 0 ]
  then
    KC_CURRENT_EXISTS=true
  else
    KC_CURRENT_EXISTS=false
  fi
  ${OCI_AGENT} images | grep ibm-zaiops | grep -q keycloak
  if [ $? -eq 0 ]
  then
    KC_LEGACY_EXISTS=true
  else
    KC_LEGACY_EXISTS=false
  fi
}

exportKcData() {
  # Check for legacy zdap Keycloak volume
  ZDAP_LEGACY_VOL=$( ${OCI_AGENT} volume ls -q | grep zdap_keycloak$ )
  if [ "${ZDAP_LEGACY_VOL}x" == "x" ]
  then
    logToStdout "${INFOMSG}Legacy ZDAP Keycloak volume not found. Will not perform realm user migration."
  else
    logToStdout "${INFOMSG}Legacy ZDAP Keycloak volume found. Will attempt realm user migration."
    mkdir -p ${BASEDIR}/realm_migration
    rm -Rf ${BASEDIR}/realm_migration/*
    # Bring up Keycloak in export mode using legacy ZDAP volume
    echo "Starting authentication service in export mode with legacy ZDAP realm..."
    echo -n "Host time: " ; date
    prepKeycloak
    ${OCI_AGENT} run -u ${ZOA_UID}:0 --name keycloak -d \
      --security-opt no-new-privileges \
      -e DB_VENDOR=h2 \
      -e PROXY_ADDRESS_FORWARDING=true \
      -e ZAIOPS_KC_PASS=${ZAIOPS_ZOASVC_PASS} \
      -e ZAIOPS_KEYCLOAK_HOST=${ZAIOPS_KEYCLOAK_HOST} \
      -e ZAIOPS_KEYCLOAK_IP=${ZAIOPS_KEYCLOAK_IP} \
      -e ZDAP_GATEWAY_PORT=${ZDAP_GATEWAY_PORT} \
      -e EXTERNAL_GATEWAY_HOST=${EXTERNAL_GATEWAY_HOST} \
      -e IZOA_GATEWAY_PORT=${IZOA_GATEWAY_PORT} \
      -e SSL_DEBUG=${SSL_DEBUG} \
      -e "JAVA_OPTS_APPEND=-Djdk.tls.client.protocols=${ZAIOPS_TLS_VERSION} -Djboss.as.management.blocking.timeout=${ZAIOPS_KEYCLOAK_TIMEOUT} -Dkeycloak.migration.action=export -Dkeycloak.migration.usersExportStrategy=SAME_FILE -Dkeycloak.migration.provider=dir -Dkeycloak.migration.dir=/realm_migration" \
      -v ${ZDAP_LEGACY_VOL}:/opt/jboss/keycloak/standalone/data/ \
      -v ${BASEDIR}/ssl:/ssl \
      -p ${ZAIOPS_KEYCLOAK_PORT}:8443 \
      -h keycloak --network ${COMPOSE_PROJECT_NAME}_zaiops \
      ibm-zaiops/zoa-keycloak:${KCTAG}-${ARCH} > /dev/null
    keycloakStatus export
    if [ ${RC} -eq 1 ]
    then
      dumpKeycloakLogs export
      shutdownKeycloak export
      exit 1
    fi
    migrationStatus Export
    if [ ${RC} -eq 1 ]
    then
      dumpKeycloakLogs export
      shutdownKeycloak export
      exit 1
    else
      ${OCI_AGENT} exec keycloak bash -c "cd /realm_migration ; tar cf export_ZDAP.tar *.json"
      RC=$?
      if [ ${RC} -ne 0 ]
      then
        dumpKeycloakLogs export
        shutdownKeycloak export
        exit 1
      else
        ${OCI_AGENT} cp keycloak:/realm_migration/export_ZDAP.tar ${BASEDIR}/realm_migration/
        RC=$?
        if [ ${RC} -ne 0 ]
        then
          dumpKeycloakLogs export
          shutdownKeycloak export
          exit 1 
        else
          shutdownKeycloak export
        fi
      fi
    fi
  fi

  # Bring up Keycloak in export mode using current volume
  echo "Starting authentication service in export mode with legacy IzoaKeycloak realm..."
  echo -n "  Host time: " ; date
  ${OCI_AGENT} run -u ${ZOA_UID}:0 --name keycloak -d \
    --security-opt no-new-privileges \
    -e DB_VENDOR=h2 \
    -e PROXY_ADDRESS_FORWARDING=true \
    -e ZAIOPS_KC_PASS=${ZAIOPS_ZOASVC_PASS} \
    -e ZAIOPS_KEYCLOAK_HOST=${ZAIOPS_KEYCLOAK_HOST} \
    -e ZAIOPS_KEYCLOAK_IP=${ZAIOPS_KEYCLOAK_IP} \
    -e ZDAP_GATEWAY_PORT=${ZDAP_GATEWAY_PORT} \
    -e EXTERNAL_GATEWAY_HOST=${EXTERNAL_GATEWAY_HOST} \
    -e IZOA_GATEWAY_PORT=${IZOA_GATEWAY_PORT} \
    -e SSL_DEBUG=${SSL_DEBUG} \
    -e "JAVA_OPTS_APPEND=-Djdk.tls.client.protocols=${ZAIOPS_TLS_VERSION} -Djboss.as.management.blocking.timeout=${ZAIOPS_KEYCLOAK_TIMEOUT} -Dkeycloak.migration.action=export -Dkeycloak.migration.usersExportStrategy=SAME_FILE -Dkeycloak.migration.provider=dir -Dkeycloak.migration.dir=/realm_migration" \
    -v ${COMPOSE_PROJECT_NAME}_zaiops_keycloak:/opt/jboss/keycloak/standalone/data/ \
    -v ${BASEDIR}/ssl:/ssl \
    -p ${ZAIOPS_KEYCLOAK_PORT}:8443 \
    -h keycloak --network ${COMPOSE_PROJECT_NAME}_zaiops \
    ibm-zaiops/zoa-keycloak:${KCTAG}-${ARCH} > /dev/null
  keycloakStatus export
  if [ ${RC} -eq 1 ]
  then
    dumpKeycloakLogs export
    shutdownKeycloak export
    exit 1
  fi
  migrationStatus Export
  if [ ${RC} -eq 1 ]
  then
    dumpKeycloakLogs export
    shutdownKeycloak export
    exit 1
  else
    ${OCI_AGENT} exec keycloak bash -c "cd /realm_migration ; tar cf export_IzoaKeycloak.tar *.json"
    RC=$?
    if [ ${RC} -ne 0 ]
    then
      dumpKeycloakLogs export
      shutdownKeycloak export
      exit 1
    else
      ${OCI_AGENT} cp keycloak:/realm_migration/export_IzoaKeycloak.tar ${BASEDIR}/realm_migration/
      RC=$?
      if [ ${RC} -ne 0 ]
      then
        dumpKeycloakLogs export
        shutdownKeycloak export
        exit 1
      else
        shutdownKeycloak export
        if [ "${ZDAP_LEGACY_VOL}x" != "x" ]
        then
          ${OCI_AGENT} volume rm ${ZDAP_LEGACY_VOL}
        fi
        ${OCI_AGENT} volume rm ${COMPOSE_PROJECT_NAME}_zaiops_keycloak
      fi
    fi
  fi
}

importKcData() {
  # Process export files
  if [ -f ${BASEDIR}/realm_migration/export_ZDAP.tar ]
  then
    tar xf ${BASEDIR}/realm_migration/export_ZDAP.tar -C ${BASEDIR}/realm_migration && rm -f ${BASEDIR}/realm_migration/export_ZDAP.tar
  fi
  tar xf ${BASEDIR}/realm_migration/export_IzoaKeycloak.tar -C ${BASEDIR}/realm_migration && rm -f ${BASEDIR}/realm_migration/export_IzoaKeycloak.tar
  if [ -f ${BASEDIR}/realm_migration/zdap-users-0.json ]
  then
    # zdap realm existed and was exported
    mv ${BASEDIR}/realm_migration/zdap-users-0.json ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json
    find ${BASEDIR}/realm_migration/ -type f -name "zdap*" | xargs rm
    sed -i -e "s%\"zdap\"%\"IzoaKeycloak\"%g" ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json
    for USER in $( grep \"username\" ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json | awk '{ print $3 }' | tr -d \", )
    do
      grep -q \"${USER}\" ${BASEDIR}/realm_migration/IzoaKeycloak-users-0.json
      if [ $? -eq 0 ]
      then
        sed -i -e "s%\"${USER}\"%\"duplicate.${USER}\"%g" ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json
      fi
    done
    for EMAIL in $( grep \"email\" ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json | awk '{ print $3 }' | tr -d \", )
    do
      grep -q \"${EMAIL}\" ${BASEDIR}/realm_migration/IzoaKeycloak-users-0.json
      if [ $? -eq 0 ]
      then
        sed -i -e "s%\"${EMAIL}\"%\"duplicate.${EMAIL}\"%g" ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json
      fi
    done
    grep -v \"id\" ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json | grep -v \"createdDate\" | grep -v \"createdTimestamp\" > ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json.tmp
    mv -f ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json.tmp ${BASEDIR}/realm_migration/IzoaKeycloak-users-1.json
  fi
  # Bring Keycloak up in import mode
  echo "Starting authentication service in import mode..."
  echo -n "Host time: " ; date
  prepKeycloak
  ${OCI_AGENT} run -u ${ZOA_UID}:0 --name ${COMMON_CONTAINER_PREFIX}auth -d \
    --security-opt no-new-privileges \
    -e EXTERNAL_GATEWAY_HOST=${EXTERNAL_GATEWAY_HOST} \
    -e HOSTNAME_STRICT=${HOSTNAME_STRICT} \
    -e IZOA_GATEWAY_PORT=${IZOA_GATEWAY_PORT} \
    -e SSL_DEBUG=${SSL_DEBUG} \
    -e ZAIOPS_KC_BOOTSTRAP_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN} \
    -e ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED} \
    -e ZAIOPS_KC_BOOTSTRAP_PASSWORD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD} \
    -e ZAIOPS_KC_CACHE_MODE=${ZAIOPS_KC_CACHE_MODE} \
    -e ZAIOPS_KC_DB=${ZAIOPS_KC_DB} \
    -e ZAIOPS_KC_DB_PWD=${ZAIOPS_KC_DB_PWD} \
    -e ZAIOPS_KC_DB_URL=${ZAIOPS_KC_DB_URL} \
    -e ZAIOPS_KC_DB_USER=${ZAIOPS_KC_DB_USER} \
    -e ZAIOPS_KC_HTTPS_PORT=${ZAIOPS_KC_HTTPS_PORT} \
    -e ZAIOPS_KC_KEYSTORE_FILE=${ZAIOPS_KC_KEYSTORE_FILE} \
    -e ZAIOPS_KC_KEYSTORE_TYPE=${ZAIOPS_KC_KEYSTORE_TYPE} \
    -e ZAIOPS_KC_PASS=${ZAIOPS_ZOASVC_PASS} \
    -e ZAIOPS_KC_TRUSTSTORE_FILE=${ZAIOPS_KC_TRUSTSTORE_FILE} \
    -e ZAIOPS_KC_TRUSTSTORE_TYPE=${ZAIOPS_KC_TRUSTSTORE_TYPE} \
    -e ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED=${ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED:-false} \
    -e ZAIOPS_KEYCLOAK_IP=${ZAIOPS_KEYCLOAK_IP} \
    -e ZAIOPS_KEYCLOAK_LOGLEVEL=${ZAIOPS_KEYCLOAK_LOGLEVEL:-INFO} \
    -e ZAIOPS_KEYCLOAK_METRICAPI_ENABLED=${ZAIOPS_KEYCLOAK_METRICAPI_ENABLED:-false} \
    -e ZAIOPS_KEYCLOAK_PORT=${ZAIOPS_KEYCLOAK_PORT} \
    -e ZAIOPS_PROXY_HEADERS=${ZAIOPS_PROXY_HEADERS} \
    -e ZAIOPS_TLS_VERSION=${ZAIOPS_TLS_VERSION} \
    # -p ${ZAIOPS_KEYCLOAK_PORT}:8443 \
    -v ${COMPOSE_PROJECT_NAME}_zaiops_keycloak:/opt/keycloak/data/h2/ \
    -v ${BASEDIR}/realm_migration:/realm_migration \
    -v zaiops_shared:/shared:ro \
    -h auth --network ${COMPOSE_PROJECT_NAME}_zaiops \
    ibm-zaiops/zoa-auth:${TAG}-${ARCH} > /dev/null
  keycloakStatus import
  if [ ${RC} -eq 1 ]
  then
    dumpKeycloakLogs import
    shutdownKeycloak import
    exit 1
  fi
  ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}auth bash -c "cd /opt/keycloak/bin ; ./kc.sh import --dir /realm_migration"
  RC=$?
  if [ ${RC} -ne 0 ]
  then
    dumpKeycloakLogs import
    shutdownKeycloak import
    exit 1
  else
    # Apparently, Keycloak v21 shuts down on its own after completed import; therefore, log output is not available
    # Disable migration status check for now
    # migrationStatus Import
    shutdownKeycloak import
  fi
}

dumpKeycloakLogs() {
  MODE=${1}
  if [ "${MODE}" == "export" ]
  then
    AUTHSVC=keycloak
    AUTHHOST=keycloak
  else
    AUTHSVC=${COMMON_CONTAINER_PREFIX}auth
    AUTHHOST=auth
  fi
  echo ""
  logToStdout "${ERRORMSG}Error encountered during ${MODE} phase. Attempting to collect authentication service logs..."
  echo "***************************** BEGIN AUTHENTICATION SERVICE LOG *****************************"
  ${OCI_AGENT} logs ${AUTHSVC} 2>&1
  echo "*****************************  END AUTHENTICATION SERVICE LOG  *****************************"
  echo ""
 }

keycloakStatus() {
  MODE=${1}
  if [ "${MODE}" == "export" ]
  then
    AUTHSVC=keycloak
    AUTHHOST=keycloak
  else
    AUTHSVC=${COMMON_CONTAINER_PREFIX}auth
    AUTHHOST=auth
  fi
  RC=0
  echo -n "Waiting for authentication service to be ready ... "
  START=$( date +"%s" )
  ${OCI_AGENT} exec ${AUTHSVC} bash -c "wait-for-it.sh --quiet --progress --strict --timeout=${SERVICE_TIMEOUT} ${AUTHHOST}:8443"
  if [ $? -eq 0 ]
  then
    END=$( date +"%s" )
    echo "successful after $(( ${END} - ${START} )) seconds."
  else
    echo "service still not ready after ${SERVICE_TIMEOUT} seconds; giving up."
    logToStdout "${ERRORMSG}Service status:"
    echo "--------------------------------------------------------------------------------"
    ${SCRIPTDIR}/${OCI_AGENT}ManageZoa.sh ps
    echo "--------------------------------------------------------------------------------"
    logToStdout "${ERRORMSG}Last ${TS_LOG_LINES} lines of service log output:"
    echo "--------------------------------------------------------------------------------"
    ${SCRIPTDIR}/${OCI_AGENT}ManageZoa.sh logs auth | tail -n ${TS_LOG_LINES}
    echo "--------------------------------------------------------------------------------"
    RC=1
  fi
}

migrationStatus() {
  RC=0
  PROCESS=${1}
  PROCESS_DISPLAY=$( echo ${PROCESS} | tr [:upper:] [:lower:] )
  if [ "${PROCESS_DISPLAY}" == "export" ]
  then
    AUTHSVC=keycloak
  else
    AUTHSVC=${COMMON_CONTAINER_PREFIX}auth
  fi
  # Sleep for up to 120 seconds to allow requested process to complete
  echo -n "Waiting for ${PROCESS_DISPLAY} to complete "
  count=0
  while :
  do
    echo -n .
    ${OCI_AGENT} logs ${AUTHSVC} | grep "${PROCESS} finished successfully" >/dev/null 2>&1
    if [ $? -eq 0 ]
    then
      echo -n " "
      echo "successful after ${count} seconds."
      break
    elif [ ${count} -ge ${SERVICE_TIMEOUT} ]
    then
      echo -n " "
      echo "${PROCESS_DISPLAY} process still not completed after ${count} seconds; giving up."
      RC=1
      break
    else
      sleep 5
      count=$(( ${count} + 5 ))
    fi
  done
}

shutdownKeycloak() {
  MODE=${1}
  if [ "${MODE}" == "export" ]
  then
    # The container name produced by this script is 'keycloak'
    AUTHSVC=keycloak
  else
    AUTHSVC=${COMMON_CONTAINER_PREFIX}auth
  fi
  echo "Shutting down authentication service (${MODE} mode)..."
  echo -n "  Host time: " ; date
  echo -n "  Stopping container "
  ${OCI_AGENT} stop ${AUTHSVC}
  echo -n "  Removing container "
  ${OCI_AGENT} rm ${AUTHSVC}
  if [ "${DELETE_NETWORK}" == "true" ]
  then
    echo -n "  Removing network "
    ${OCI_AGENT} network rm ${COMPOSE_PROJECT_NAME}_zaiops
  fi
}

prepKeycloak() {
  AUTHSVC=$( ${OCI_AGENT} ps -a | grep -E '(keycloak$|auth$)' | awk '{ print $NF }' )
  # If AUTHSVC is not empty, some Keycloak instance is running; shut it down
  if [ "${AUTHSVC}x" != "x" ]
  then
    logToStdout "${INFOMSG}Authentication service '${AUTHSVC}' is running and will be shut down..."
    echo -n "  Stopping "
    ${OCI_AGENT} stop ${AUTHSVC}
    echo -n "  Removing "
    ${OCI_AGENT} rm ${AUTHSVC}
  else
    logToStdout "${INFOMSG}No running authentication service found."
  fi
  cd ${BASEDIR}
  ${OCI_AGENT} network ls | awk '{ print $2 }' | grep -q ${COMPOSE_PROJECT_NAME}_zaiops
  if [ $? -ne 0 ]
  then
    DELETE_NETWORK=true
    ${OCI_AGENT} network create ${COMPOSE_PROJECT_NAME}_zaiops
  fi
}

cleanupExportData() {
  # Clean up
  rm -Rf ${BASEDIR}/realm_migration
}

prepExportData() {
  # Create export directory
  mkdir -p ${BASEDIR}/realm_migration
}

## MAIN
ARG=${1}
KC_CURRENT_EXISTS=false
KC_LEGACY_EXISTS=false
case "${ARG}" in
  "--export" | "-e" )
    checkKcImpl
    if [ "${KC_LEGACY_EXISTS}" == "true" ] && [ "${KC_CURRENT_EXISTS}" == "false" ]
    then
      cleanupExportData
      prepExportData
      exportKcData
    else
      echo ""
      logToStdout "${INFOMSG}No legacy authentication service found. Will not proceed with data export."
      echo ""
      exit
    fi
    ;;
  "--import" | "-i" )
    checkKcImpl
    if [ "${KC_CURRENT_EXISTS}" == "true" ] && [ "${KC_LEGACY_EXISTS}" == "false" ]
    then
     if [ -f ${BASEDIR}/realm_migration/export_IzoaKeycloak.tar ]
      then
        importKcData
        cleanupExportData
      else
        echo ""
        logToStdout "${INFOMSG}No exported migration data from legacy authentication service found. Will not proceed with data import."
        echo ""
        exit
     fi
    else
      echo ""
      logToStdout "${INFOMSG}No current authentication service found. Will not proceed with data import."
      echo ""
      exit
    fi
    ;;
  * )
    usage
    ;;
esac
