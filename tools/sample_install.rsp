##############################################################
# GENERAL SETTINGS:
# Path to install message anomaly detection feature into
DCPATH=${HOME}/IBM/ZAA
# Whether or not to create the target directory if it does not exist
# # Y = Yes, do create
# # N = No, do not create
CREATEDIR=Y
# FEATURES is a space-separated list of features to install.
# Valid options for an installation including ZAA Ensemble are 'ensemble' and 'zdap'.
# Valid options for an installation including ZAA Classic are 'logmsgml', 'metricml' and 'zdap'.
FEATURES="ensemble zdap"
# User ID used by the Rules Engine to communicate with the Problem Insights server
PIUSER=piuser
# Password for the user ID defined in PIUSER
PIPWD=changeme
# On multi-homed systems, accept the first listed IP address for configuration purposes
USEFIRSTIP=Y
# Whether or not to install ZOA common services when no features are selected
BASEONLY=N
