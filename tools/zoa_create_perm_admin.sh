#!/bin/bash
########################################################## {COPYRIGHT-TOP} ###
## Licensed Materials - Property of IBM
## 5698-LDA (C) Copyright IBM Corp. 2023
## All rights reserved.
## US Government Users Restricted Rights - Use, duplication or
## disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
########################################################## {COPYRIGHT-END} ###

SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
INFOMSG="\x1b[32;20mINFO    >> "
ERRORMSG="\x1b[31;20mERROR   >> "
WARNMSG="\x1b[33;20mWARNING >> "
SPACEMSG="           "
ENDMSG="\x1b[0m"

TEMP_ADMIN=$1
TEMP_ADMIN_PASSWORD=$2
TEMP_ADMIN_PASSWORD_CLEAR=$( echo "${TEMP_ADMIN_PASSWORD}" | base64 -d )
PERM_ADMIN=$3
PERM_ADMIN_PASSWORD=$4
PERM_ADMIN_PASSWORD_CLEAR=$( echo "${PERM_ADMIN_PASSWORD}" | base64 -d )
KC_CONTEXT_ROOT=$5
ZAIOPS_KEYCLOAK_HOST=$6
KEYCLOAK_PORT=$7
TRUSTSTORE_PASS=$8
TRUSTSTORE_PASS_C=$( echo "${TRUSTSTORE_PASS}" | base64 -d )
TRUSTSTORE=/ssl/zoasvc.ts.p12
SERVER=http://auth:8080

logToStdout() {
  MSG=${1}
  echo -e "${MSG}${ENDMSG}"
}


# Main process
# Create permanent admin ID in master realm
#/opt/keycloak/bin/kcadm.sh config truststore --trustpass "${TRUSTSTORE_PASS_C}" ${TRUSTSTORE}
/opt/keycloak/bin/kcadm.sh config credentials --server ${SERVER}/${KC_CONTEXT_ROOT} --realm master --user ${TEMP_ADMIN} --password ${TEMP_ADMIN_PASSWORD_CLEAR}
if [ $? -eq 0 ]
then
  TUID=$( /opt/keycloak/bin/kcadm.sh get users -r master -q username=${TEMP_ADMIN} --fields id --format csv --noquotes )
  PUID=$( /opt/keycloak/bin/kcadm.sh get users -r master -q username=${PERM_ADMIN} --fields id --format csv --noquotes )
  if [ "${PUID}x" == "x" ]
  then
    logToStdout "${INFOMSG}User ID '${PERM_ADMIN}' does not exist. It will be created."
    /opt/keycloak/bin/kcadm.sh create users -s username=${PERM_ADMIN} -s enabled=true -r master -i
    /opt/keycloak/bin/kcadm.sh set-password -r master --username ${PERM_ADMIN} --new-password ${PERM_ADMIN_PASSWORD_CLEAR}
    /opt/keycloak/bin/kcadm.sh add-roles --uusername ${PERM_ADMIN} --rolename admin
  else
    logToStdout "${INFOMSG}User ID '${PERM_ADMIN}' already exists."
  fi
  /opt/keycloak/bin/kcadm.sh delete users/${TUID} -r master
fi
