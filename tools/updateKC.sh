#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-LDA (C) Copyright IBM Corp. 2021, 2023
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            updateKC.sh
#
# Description:     IBM Z Operational Analytics common services installation
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Script used to the Keycloak authentication service installed
#                  as part of the IBM Z Operational Analytics common services
#
# Syntax:          updateKC.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

SCRIPTDIR="$( cd "$( dirname "$0" )/../.." && pwd )"
SCRIPTNAME="$( basename "$0" )"
not_just_zdap=$1

. ${SCRIPTDIR}/utils/common_functions.sh

BASEDIR=`dirname ${SCRIPTDIR}`
ARCH=`uname -m`
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
DELETE_NETWORK=false
COMPOSE="docker compose --env-file ${NEW_CONFIG} -f zoacommon-docker-compose.yml"

usage() {
  echo ""
  echo "Usage:"
  echo "  updateKC.sh                to update authentication service configuraton for use with"
  echo "                             Z Operational Analytics components"
  echo ""
}

checkService() {
  SERVICE_NAME=${1}
  STATUS=`${OCI_AGENT} inspect --format '{{ index .State.Status }}' ${SERVICE_NAME} 2>/dev/null`
  RC=$?
  if [ ${RC} -ne 0 ] || ( [ ${RC} -eq 0 ] && [ "${STATUS}x" == "x" ] )
  then
    echo "Container ${SERVICE_NAME} does not exist."
    SERVICE_STATUS=DOWN
  elif [ "${STATUS}" = "running" ]
  then
    echo "Container ${SERVICE_NAME} is running."
    SERVICE_STATUS=UP
  else
    echo "Container ${SERVICE_NAME} is not in a steady state."
    SERVICE_STATUS=UNSTABLE
  fi
}

echo "Configuring authentication service"
echo ""
set -a
. ${BASEDIR}/${NEW_CONFIG}
. ${BASEDIR}/${IBM_CONFIG}
set +a
if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
then
  export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
fi
if [ "${OCI_AGENT}" = "docker" ]
then
  if [ ${OCI_VERSION} -ge 20 ]
  then
    COMMON_CONTAINER_PREFIX=zoa-
  else
    COMMON_CONTAINER_PREFIX=""
  fi
elif [ "${OCI_AGENT}" = "podman" ]
then
  COMMON_CONTAINER_PREFIX=""
fi

# Check Keycloak status; shut it down if needed
checkService ${COMMON_CONTAINER_PREFIX}auth
if [ "${SERVICE_STATUS}" = "UNSTABLE" ]
then
  logToStdout "${WARNMSG}Service ${COMMON_CONTAINER_PREFIX}auth is not ready. Unable to proceed."
  exit 1
elif [ "${SERVICE_STATUS}" = "DOWN" ]
then
  cd ${BASEDIR}
  # Bring up Keycloak in standard mode
  echo "Starting authentication service in standard mode..."
  ${SCRIPTDIR}/${OCI_AGENT}ManageZoa.sh up auth
  # Apply ZDAp-specific configuration to Keycloak
  cd ${BASEDIR}
  printf "%-50s %s " "Waiting for authentication service to be ready" "..."
  ${SCRIPTDIR}/${OCI_AGENT}ManageZoa.sh wait-for-service auth 8443
  if [ $? -eq 1 ]
  then
    shutdownKeycloak standard
    exit 1
  fi
fi

chmod 755 ${SCRIPTDIR}/utils/keycloak/*.sh
if [ "${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}x" == "truex" ]
then
  tar -C ${SCRIPTDIR}/utils/keycloak --numeric-owner --owner=0 --group=0 -cf - zoa_create_perm_admin.sh | ${OCI_AGENT} cp - ${COMMON_CONTAINER_PREFIX}auth:/realm/
  ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}auth bash -c "/realm/zoa_create_perm_admin.sh ${ZAIOPS_KC_BOOTSTRAP_ADMIN} ${ZAIOPS_KC_BOOTSTRAP_PASSWORD} ${ZAIOPS_KEYCLOAK_ADMIN} ${ZAIOPS_KEYCLOAK_ADMIN_PASS} ${ZAIOPS_KC_CONTEXT_ROOT} ${ZAIOPS_KEYCLOAK_HOST} ${ZAIOPS_KEYCLOAK_PORT} ${ZAIOPS_ZOASVC_PASS}"
  sed -i -e "s%^ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=.*$%ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=false%g" ${BASEDIR}/${IBM_CONFIG}
  export ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=false
fi
tar -C ${SCRIPTDIR}/utils/keycloak --numeric-owner --owner=0 --group=0 -cf - zoa_kc_update.sh | ${OCI_AGENT} cp - ${COMMON_CONTAINER_PREFIX}auth:/realm/
${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}auth bash -c "/realm/zoa_kc_update.sh ${ZAIOPS_KEYCLOAK_ADMIN} ${ZAIOPS_KEYCLOAK_ADMIN_PASS} ${ZAIOPS_KC_REALM_ADMIN} ${ZAIOPS_ZOAREALM_ADMIN_PASS} ${ZAIOPS_ZOASVC_PASS} ${ZAIOPS_KEYCLOAK_HOST} ${ZAIOPS_KEYCLOAK_PORT} ${IZOA_GATEWAY_PORT} ${ZAIOPS_KC_CONTEXT_ROOT} ${not_just_zdap}"

# If the service was down before, bring it back down
if [ "${SERVICE_STATUS}" = "DOWN" ]
then
  shutdownKeycloak standard
fi
