# If there are many ids that need to be defined to Keycloak, they can be loaded with the config-ldap option in the dockerManageZoa tool.
# This option will ask for a file that contains a list of ids that should be added to Keycloak and optionally assigned a group.
#
# If there are any defined user federations, Keycloak will check each id against them in priority order.
# This will affect the result such that:
# �	If a federation returns a positive result, the id will be associated with that federation and further federations will be skipped.
# �	Any groups mapped by this federation will be assigned to the id.
# �	If all federations are processed and no positive result is returned, an error message will be displayed, and the id will not be added to Keycloak.
#
# Once ids are added to Keycloak, a Keycloak administrator can use the Users interface to assign groups and roles.
#
# Note: Because a RACF LDAP does not access the datastore until the user attempts to login,
# it will return a positive result for any id passed to it as part of this process.
# Therefore if you have other federations that connect to LDAP v3 datastores, it is important
# that they are assigned a lower priority than a RACF LDAP federation.
#
# Entries in this file needs to be in the format:
#<id>  <access group name>
#IBMUSER		admin
#USER1		adv_user
#USER2
#USER3		basic_user
#USER4
#USER1		basic_user
