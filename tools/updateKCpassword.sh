#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM 
# 5698-LDA (C) Copyright IBM Corp. 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            updateKCpasswords.sh
#
# Description:     IBM Z Operational Analytics common services installation
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Script used to update the Keycloak userid passwords
#
# Syntax:          updateKCpasswords.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

usage() {
  echo ""
  echo "Usage:"
  echo "  ${0} <userid>    to update the password for the specified userid"
  echo "                                  stored in Keycloak."
  echo ""
}

# Get password
getNewPwd() {
  unset NEW_PWD
  ATTEMPT=1
  echo ""
  echo "**Note: Passwords set with this command can not include ' or \\"
  while [ ${ATTEMPT} -lt 4 ]
  do
    unset GPWD
    unset TGPWD
    while [[ -z "$GPWD" ]]; do
      read -e -s -p "  Enter password:    " GPWD
      echo ""
    done
    while [[ -z "$TGPWD" ]]; do
      read -e -s -p "  Confirm password:  " TGPWD
      echo ""
    done

    if [ ! "${GPWD}" = "${TGPWD}" ]
    then
      REMAIN=$(( 3 - ATTEMPT ))
      if [ ${REMAIN} -gt 0 ]
      then
        echo "Passwords don't match. You have ${REMAIN} attempts remaining."
      else
        echo "Passwords don't match."
      fi
      ATTEMPT=$(( ATTEMPT + 1 ))
    else
      echo "Passwords match."
      NEW_PWD=${GPWD}
      break
    fi
  done
}

checkService() {
  SERVICE_NAME=${1}
  STATUS=`${OCI_AGENT} inspect --format '{{ index .State.Status }}' ${SERVICE_NAME} 2>/dev/null`
  RC=$?
  if [ ${RC} -ne 0 ] || ( [ ${RC} -eq 0 ] && [ "${STATUS}x" == "x" ] )
  then
    echo "Container ${SERVICE_NAME} does not exist."
    SERVICE_STATUS=DOWN
  elif [ "${STATUS}" = "running" ]
  then
    echo "Container ${SERVICE_NAME} is running."
    SERVICE_STATUS=UP
  else
    echo "Container ${SERVICE_NAME} is not in a steady state."
    SERVICE_STATUS=UNSTABLE
  fi
}

if [ $# -eq 0 ]; then
  usage
  exit
fi

# assume this script is running in ZDAP_HOME/bin/utils/keycloak
# get the ZDAP_HOME/bin directory
SCRIPTDIR="$( cd "$( dirname "$0" )/../.." && pwd )"
. ${SCRIPTDIR}/utils/common_functions.sh
BASEDIR=`dirname ${SCRIPTDIR}`
# config files in the ZDAP_HOME directory
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
KCUSERID="$1"

echo "Configuring authentication service"
echo ""
set -a
. ${BASEDIR}/${NEW_CONFIG}
. ${BASEDIR}/${IBM_CONFIG}
set +a

if [ "${SERVICE_TIMEOUT_OVERRIDE}x" != "x" ]
then
  export SERVICE_TIMEOUT=${SERVICE_TIMEOUT_OVERRIDE}
fi
if [ "${OCI_AGENT}" = "docker" ]
then
  if [ ${OCI_VERSION} -ge 20 ]
  then
    COMMON_CONTAINER_PREFIX=zoa-
  else
    COMMON_CONTAINER_PREFIX=""
  fi
elif [ "${OCI_AGENT}" = "podman" ]
then
  COMMON_CONTAINER_PREFIX=""
fi

# Check Keycloak status; shut it down if needed
checkService ${COMMON_CONTAINER_PREFIX}auth
if [ "${SERVICE_STATUS}" = "UNSTABLE" ]
then
  logToStdout "${WARNMSG}Service ${COMMON_CONTAINER_PREFIX}auth is not ready. Unable to proceed."
  exit 1
elif [ "${SERVICE_STATUS}" = "DOWN" ]
then
  cd ${BASEDIR}
  # Bring up Keycloak in standard mode
  echo "Starting authentication service in standard mode..."
  ${SCRIPTDIR}/${OCI_AGENT}ManageZoa.sh up auth
  # Apply ZDAp-specific configuration to Keycloak
  cd ${BASEDIR}
  printf "%-50s %s " "Waiting for authentication service to be ready" "..."
  ${SCRIPTDIR}/${OCI_AGENT}ManageZoa.sh wait-for-service auth 8443
  if [ $? -eq 1 ]
  then
    shutdownKeycloak standard
    exit 1
  fi
fi

echo ""
if [ "${KCUSERID}" = "${ZAIOPS_KEYCLOAK_ADMIN}" ]
then
  echo "Resetting the password for keycloakadmin requires the id's current password be correct in zoa_env.config"
  echo "If this is not the case, you will not be able to reset its password."
elif [ "${KCUSERID}" = "${ZAIOPS_KC_REALM_ADMIN}" ]
then
  echo "If this id does not currently exist, it will be created."
else
  echo "If there is a user federation enabled, user id passwords must be reset through the LDAP provider."
  echo "To create a new id, use the Keycloak administrator user interface."
fi

getNewPwd
if [ "${NEW_PWD}x" = "x" ]
then
  exit 1
fi

tar -C ${SCRIPTDIR}/utils/keycloak --numeric-owner --owner=0 --group=0 -cf - zoa_kc_updatepassword.sh | ${OCI_AGENT} cp - ${COMMON_CONTAINER_PREFIX}auth:/realm/ 
${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}auth bash -c "echo '${NEW_PWD}' > /tmp/.pw; /realm/zoa_kc_updatepassword.sh ${ZAIOPS_KEYCLOAK_ADMIN} ${ZAIOPS_KEYCLOAK_ADMIN_PASS} ${ZAIOPS_KC_REALM_ADMIN} ${ZAIOPS_KC_CONTEXT_ROOT} ${KCUSERID}; rm /tmp/.pw"

# for keycloakadmin and zoakcadmin ids, update corresponding env file with the new password
ENCODEDPW=$(echo "${NEW_PWD}" | base64)
if [ "${KCUSERID}" = "${ZAIOPS_KEYCLOAK_ADMIN}" ]
then
  sed -i -e "s%^ZAIOPS_KEYCLOAK_ADMIN_PASS=\S*$%ZAIOPS_KEYCLOAK_ADMIN_PASS=${ENCODEDPW}%" ${BASEDIR}/${NEW_CONFIG}
elif [ "${KCUSERID}" = "${ZAIOPS_KC_REALM_ADMIN}" ]
then
  sed -i -e "s%^ZAIOPS_ZOAREALM_ADMIN_PASS=\S*$%ZAIOPS_ZOAREALM_ADMIN_PASS=${ENCODEDPW}%" ${BASEDIR}/${NEW_CONFIG}
fi

# If the service was down before, bring it back down
if [ "${SERVICE_STATUS}" = "DOWN" ]
then
  shutdownKeycloak standard
fi