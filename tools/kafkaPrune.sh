#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2022
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            kafkaPrune.sh
#
# Description:     IBM Z AIOps Common Services management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
# 
# Purpose:         <PERSON><PERSON><PERSON> used to manage Apache Kafka topics
#
# Syntax:          kafkaPrune.sh
# 
# 
# MAINTENANCE:
# 
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------
SCRIPTDIR="$( cd "$( dirname "$0" )/../.." && pwd )"
LOGDIR="$( cd "${SCRIPTDIR}/../logs" && pwd )"
PRODUCT=${2}
PURGE="false"
UPDATE_RETENTION_ONLY="false"
if [ "${3}" == "--purge" ]
then
  PURGE="true"
fi
if [ "${3}" == "--batch-retention-change" ]
then
  UPDATE_RETENTION_ONLY="true"
fi
# Get configuration settings
. ${SCRIPTDIR}/../zoa_env.config
. ${SCRIPTDIR}/../.zoa_factory.config
if [ "${OCI_AGENT}" = "docker" ]
then   
  if [ ${OCI_VERSION} -ge 20 ]
  then
    COMMON_CONTAINER_PREFIX=zoa-
  else
    COMMON_CONTAINER_PREFIX=""
  fi
elif [ "${OCI_AGENT}" = "podman" ]
then
  COMMON_CONTAINER_PREFIX=""
fi
CONTAINER=${COMMON_CONTAINER_PREFIX}kafkabroker
mkdir -p ${LOGDIR}

usage() {
  echo ""
  echo "Usage:"
  echo "  ${0} -p <product_name>           where <product_name> can be 'ZAA' or 'ZLDA' or 'ZDiscovery'"
  echo ""
}

pruneTopic() {
  local PRUNE_TOPIC=${1}
  local RUN_MODE=${2}
  echo "Processing Topic ${PRUNE_TOPIC} in ${RUN_MODE} mode..."
  echo "---------------------------------------------------------------------------------"
  CURRENT_TOPIC_OFFSET=`echo "${KAFKA_RESULTS}" | grep " ${PRUNE_TOPIC} " | awk '{ print $4 }'`
  if [ "${CURRENT_TOPIC_OFFSET}" = "-" ]
  then
    CURRENT_TOPIC_OFFSET=0
  fi
  END_TOPIC=`${OCI_AGENT} exec ${CONTAINER} bash -c "/opt/kafka/bin/kafka-run-class.sh org.apache.kafka.tools.GetOffsetShell --broker-list kafkabroker:19092  --topic ${PRUNE_TOPIC} --time -1" | awk -F  ":" '{ sum += $3 } END { print sum }'`
  START_TOPIC=`${OCI_AGENT} exec ${CONTAINER} bash -c "/opt/kafka/bin/kafka-run-class.sh org.apache.kafka.tools.GetOffsetShell --broker-list kafkabroker:19092  --topic ${PRUNE_TOPIC} --time -2" | awk -F  ":" '{ sum += $3 } END { print sum }'`
  RECORDS_REMAINING=$(( ${END_TOPIC} - ${START_TOPIC} ))
  READ_RECORDS=$(( ${CURRENT_TOPIC_OFFSET} - ${START_TOPIC} ))
  UNREAD_RECORDS=$(( ${END_TOPIC} - ${CURRENT_TOPIC_OFFSET} ))
  echo "Records read from Topic ${PRUNE_TOPIC} since topic creation: ${CURRENT_TOPIC_OFFSET}"
  echo "Number of records remaining in Topic ${PRUNE_TOPIC}: ${RECORDS_REMAINING} (read: ${READ_RECORDS}; unread: ${UNREAD_RECORDS})"
  echo ""
  if [ "${RUN_MODE}" = "INTERACTIVE" ]
  then
    echo "How many records do you want to delete from Topic ${PRUNE_TOPIC}?"
    echo "(NOTE: Deletion of records will start from the beginning of the topic.)"
    echo "The value you specify should not exceed ${READ_RECORDS} to ensure that the consumer receives all unread records."
    read -p "Number of records to delete (default: ${READ_RECORDS}):  " PRUNE_COUNT
    PRUNE_COUNT=${PRUNE_COUNT:-${READ_RECORDS}}
    if [ "${PURGE}" == "true" ]
    then
      PRUNE_OFFSET=-1
    else
      PRUNE_OFFSET=$(( ${START_TOPIC} + ${PRUNE_COUNT} ))
    fi
    echo ""
    if [ ${PRUNE_OFFSET} -gt ${CURRENT_TOPIC_OFFSET} ]
    then
      echo "The value you specified will cause records to be deleted that the consumer has not yet consumed."
      while [ "${PROCEED}" != "Y" ] && [ "${PROCEED}" != "y" ] && \
            [ "${PROCEED}" != "N" ] && [ "${PROCEED}" != "n" ] 
      do
        echo -n "Are you sure you want to proceed? (y/N)"
        read PROCEED
        PROCEED=${PROCEED:-"n"}   # accept no input as 'no'
      done
      if [ "${PROCEED}" = "N" ] || [ "${PROCEED}" = "n" ]
      then
        echo ""
        exit 0
      fi
    fi
  elif [ "${RUN_MODE}" = "NON_INTERACTIVE" ]
  then
    PRUNE_COUNT=${READ_RECORDS}
    if [ "${PURGE}" == "true" ]
    then
      PRUNE_OFFSET=-1
    else
      PRUNE_OFFSET=$(( ${START_TOPIC} + ${PRUNE_COUNT} ))
    fi
  else
    echo "An error has occurred."
    exit 1
  fi
  (
  cat <<- END
          {
            "partitions": [
              {
                "topic": "${PRUNE_TOPIC}",
                "partition": 0,
                "offset": ${PRUNE_OFFSET}
              }
            ],
            "version": 1
          }
END
  ) > ${LOGDIR}/kafka_prune.json
  ${OCI_AGENT} cp ${LOGDIR}/kafka_prune.json ${CONTAINER}:/tmp/
  ${OCI_AGENT} exec -u root ${CONTAINER} bash -c "chmod 644 /tmp/kafka_prune.json"
  ${OCI_AGENT} exec ${CONTAINER} bash -c "/opt/kafka/bin/kafka-delete-records.sh --bootstrap-server kafkabroker:19092 --offset-json-file /tmp/kafka_prune.json"
  START_TOPIC_AFTER=`${OCI_AGENT} exec ${CONTAINER} bash -c "/opt/kafka/bin/kafka-run-class.sh org.apache.kafka.tools.GetOffsetShell --broker-list kafkabroker:19092  --topic ${PRUNE_TOPIC} --time -2" | awk -F  ":" '{ sum += $3 } END { print sum }'`
  echo "Number of records that were deleted: $(( ${START_TOPIC_AFTER} - ${START_TOPIC} ))"
}

getRetentionSettings() {
  echo "Specify the new retention period for ${PRUNE_TOPIC} in HOURS. (default: 1)"
  echo "Examples of valid values:"
  echo "  0.5       (for 30 minutes)"
  echo "  1         (for 1 hour)"
  echo "  4.0       (for 4 hours)"
  read RETHOURS
  RETHOURS=${RETHOURS:-1}
}

reconfigRetentionAll() {
  TOPICS="${1}"
  while [ "${CONFRET}" != "Y" ] && [ "${CONFRET}" != "y" ] && \
        [ "${CONFRET}" != "N" ] && [ "${CONFRET}" != "n" ] 
  do
    echo "Do you want to reconfigure the retention period for all topics? (y/N)"
    echo "This can be useful to free up consumed disk space faster."
    read CONFRET
    CONFRET=${CONFRET:-"n"}   # accept no input as 'no'
  done
  if [ "${CONFRET}" = "N" ] || [ "${CONFRET}" = "n" ]
  then
    echo ""
    exit 0
  fi
  
  getRetentionSettings
  echo ""
  echo "New retention duration for all topics: ${RETHOURS} hours"
  echo ""
  local RETMSEC=`awk -vp=${RETHOURS} -vq=3600000 'BEGIN { printf "%.0f", p * q }'`
  for PRUNE_TOPIC in $( echo ${TOPICS} )
  do
    ${OCI_AGENT} exec ${CONTAINER} bash -c "/opt/kafka/bin/kafka-configs.sh --bootstrap-server kafkabroker:19092 --alter --entity-type topics --entity-name ${PRUNE_TOPIC} --add-config retention.ms=${RETMSEC},retention.bytes=-1"
  done
}

reconfigRetention() {
  local PRUNE_TOPIC=${1}
  while [ "${CONFRET}" != "Y" ] && [ "${CONFRET}" != "y" ] && \
        [ "${CONFRET}" != "N" ] && [ "${CONFRET}" != "n" ] 
  do
    echo "Do you want to reconfigure the retention period for ${PRUNE_TOPIC}? (y/N)"
    echo "This can be useful to free up consumed disk space faster."
    read CONFRET
    CONFRET=${CONFRET:-"n"}   # accept no input as 'no'
  done
  if [ "${CONFRET}" = "N" ] || [ "${CONFRET}" = "n" ]
  then
    echo ""
    exit 0
  fi

  getRetentionSettings
  echo ""
  echo "New retention duration for ${PRUNE_TOPIC}: ${RETHOURS} hours"
  echo ""
  local RETMSEC=`awk -vp=${RETHOURS} -vq=3600000 'BEGIN { printf "%.0f", p * q }'`
  ${OCI_AGENT} exec ${CONTAINER} bash -c "/opt/kafka/bin/kafka-configs.sh --bootstrap-server kafkabroker:19092 --alter --entity-type topics --entity-name ${PRUNE_TOPIC} --add-config retention.ms=${RETMSEC},retention.bytes=-1"
}

case "${PRODUCT}" in
  ZAA )
    CONSUMER_GROUP="zAwareConsumerGroup01 IBM-ZAA-ZAIOPS-Metric-Consumer connect-elasticsearch-sink"
    KEYWORDS="CDP-|IBM-ZAA-ZAIOPS"
    ;;
  ZLDA )
    CONSUMER_GROUP=zdap-parser
    KEYWORDS="CDP-"
    ;;
  ZDiscovery )
    CONSUMER_GROUP="JCL_consumer_group CI_consumer_group"
    KEYWORDS="IBM-ZRDDS-SNOW"
    ;;
  * )
    usage
    exit
    ;;
esac

KAFKA_RESULTS=""
for GROUP in $( echo ${CONSUMER_GROUP} )
do
    KAFKA_RESULTS+=$( ${OCI_AGENT} exec ${CONTAINER} bash -c "/opt/kafka/bin/kafka-consumer-groups.sh --bootstrap-server kafkabroker:19092 --describe --group ${GROUP}" | grep -E "(OFFSET|${KEYWORDS})" | awk '$5 !~ /^0.*/' )
done

KAFKA_RESULTS=$( echo "${KAFKA_RESULTS}" | sed "s%-GROUP%\n\nGROUP%g" )

echo ""
echo "${PRODUCT} Kafka topics and their status (topics without records will not be listed):"
echo ""
echo "${KAFKA_RESULTS}"
echo ""

read -n 1 -s -r -p "Press any key to continue..."

echo ""

KAFKA_TOPICS=`echo "${KAFKA_RESULTS}" | grep -v OFFSET | awk '{ print $2 }'`
TOPIC_ARRAY=(`echo ${KAFKA_TOPICS} All None`)

echo ""
if [ "${UPDATE_RETENTION_ONLY}" == "true" ]
then
  echo "Which of the following topics would you like to reconfigure?"
else
  echo "Which of the following topics would you like to prune?"
fi
echo ""
for i in "${!TOPIC_ARRAY[@]}"; do
    printf "%s) %s\n" "$i" "${TOPIC_ARRAY[$i]}"
done

TOPIC_MAX=$(( ${#TOPIC_ARRAY[@]} - 2 ))
IFS= read -r OPT
if [[ ${OPT} =~ ^[0-9]+$ ]] && (( (OPT >= 0) && (OPT < ${TOPIC_MAX}) ))
then
    TOPIC_TO_PRUNE=${TOPIC_ARRAY[OPT]}
elif [[ ${OPT} =~ ^[0-9]+$ ]] && (( (OPT >= 0) && (OPT == ${TOPIC_MAX}) ))
then
    TOPIC_TO_PRUNE="ALL"
else
    echo "Exiting without action."
    exit 0
fi

echo ""

if [ "${TOPIC_TO_PRUNE}" == "ALL" ]
then
  if [ "${UPDATE_RETENTION_ONLY}" == "true" ]
  then
    echo "Topic to reconfigure: ${TOPIC_TO_PRUNE}"
    reconfigRetentionAll "${KAFKA_TOPICS}"
    echo ""
  else
    echo "Topic to prune: ${TOPIC_TO_PRUNE}"
    for TOPIC in ${KAFKA_TOPICS}
    do
      pruneTopic ${TOPIC} NON_INTERACTIVE
      echo ""
    done
  fi
else
  echo "Topic to prune: ${TOPIC_TO_PRUNE}"
  pruneTopic ${TOPIC_TO_PRUNE} INTERACTIVE
  echo ""
  reconfigRetention ${TOPIC_TO_PRUNE}
  echo ""
fi
