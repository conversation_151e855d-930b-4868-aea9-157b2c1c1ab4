#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2022
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            customizeIPs.sh
#
# Description:     IBM Z AIOps Common Services management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         <PERSON><PERSON><PERSON> used to update installed insight packs
#
# Syntax:          customizeIPs.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

INFOMSG="\x1b[32;20mINFO    >> "
ERRORMSG="\x1b[31;20mERROR   >> "
WARNMSG="\x1b[33;20mWARNING >> "
SPACEMSG="           "
ENDMSG="\x1b[0m"

logToStdout() {
  MSG=${1}
  echo -e "${MSG}${ENDMSG}"
}

GWHOST_LOWER=$1
GWPORT=$2

# Update URLs in the Insight Pack *.info files
customizeIPs() {
  HERE=`pwd`
  if [ -d /opt/ibm/piserver/wlp/usr/servers/piFrameworkServer/insightpacks ]
  then
    cd /opt/ibm/piserver/wlp/usr/servers/piFrameworkServer/insightpacks
    mkdir -p IPTMP
    SUBSYSLIST=`ls -1 *health-MF.zip 2>/dev/null`
    for ENTRY in ${SUBSYSLIST}
    do
      echo "Configuring ${ENTRY}..."
      SUBSYS=`echo ${ENTRY%%health-MF.zip}`
      rm -Rf IPTMP/*
      unzip -q ${SUBSYS}health-MF.zip -d IPTMP
      cd IPTMP
      sed -i -e "s%^url=.*$%url=https://${GWHOST_LOWER}:${GWPORT}/pi-${SUBSYS}mlssc/ui/remoteEntry.js%" ${SUBSYS}-ssc.info
      rm -f ../${SUBSYS}health-MF.zip
      zip -rmq ../${SUBSYS}health-MF.zip *
      cd ..
    done
    if [ -f logmsgml-MF.zip ]
    then
      echo "Configuring logmsgml-MF.zip..."
      rm -Rf IPTMP/*
      unzip -q logmsgml-MF.zip -d IPTMP
      cd IPTMP
      sed -i -e "s%^url=.*$%url=https://${GWHOST_LOWER}:${GWPORT}/logmsgml/ui/remoteEntry.js%" log-msg.info
      rm -f ../logmsgml-MF.zip
      zip -rmq ../logmsgml-MF.zip *
      cd ..
    fi
    if [ -f ztopology-MF.zip ]
    then
      echo "Configuring ztopology-MF.zip..."
      rm -Rf IPTMP/* 
      unzip -q ztopology-MF.zip -d IPTMP
      cd IPTMP
      sed -i -e "s%^url=.*$%url=https://${GWHOST_LOWER}:${GWPORT}/zrdds-ui/remoteEntry.js%" topology-msg.info
      rm -f ../ztopology-MF.zip
      zip -rmq ../ztopology-MF.zip *
      cd ..
    fi
    if [ -f ensemble-topology-MF.zip ]
    then
      echo "Configuring ensemble-topology-MF.zip..."
      rm -Rf IPTMP/* 
      unzip -q ensemble-topology-MF.zip -d IPTMP
      cd IPTMP
      sed -i -e "s%^url=.*$%url=https://${GWHOST_LOWER}:${GWPORT}/zrdds-ui/remoteEntry.js%" ensembletopo-msg.info
      rm -f ../ensemble-topology-MF.zip
      zip -rmq ../ensemble-topology-MF.zip *
      cd ..
    fi
    if [ -f scorecard-dashboard-MF.zip ]
    then
      echo "Configuring scorecard-dashboard-MF.zip..."
      rm -Rf IPTMP/*
      unzip -q scorecard-dashboard-MF.zip -d IPTMP
      cd IPTMP
      sed -i -e "s%^url=.*$%url=https://${GWHOST_LOWER}:${GWPORT}/pi-scorecard/ui/remoteEntry.js%" scorecard.info
      rm -f ../scorecard-dashboard-MF
      zip -rmq ../scorecard-dashboard-MF.zip *
      cd ..
    fi
    rm -Rf IPTMP
    cd ${HERE}
  else
    logToStdout "${INFOMSG}No insight pack directory present in Problem Insights server installation."
  fi
}

# MAIN
customizeIPs
