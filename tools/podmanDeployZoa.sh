#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2022, 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            podmanDeployZoa.sh
#
# Description:     IBM Z AIOps core services installation
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         Installation script used to install or uninstall IBM Z AIOps
#                  core services
#
# Syntax:          podmanDeployZoa.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

INFOMSG="\x1b[32;20mINFO    >> "
ERRORMSG="\x1b[31;20mERROR   >> "
WARNMSG="\x1b[33;20mWARNING >> "
SPACEMSG="           "
ENDMSG="\x1b[0m"

unset FEATURES

umask 0022

SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
SCRIPTNAME="$( basename "$0" )"
ARCH=$( uname -m )
if [ "${ARCH}" == "s390x" ]
then
  ANTIARCH=amd64
elif [ "${ARCH}" == "x86_64" ]
then
  ANTIARCH=s390x
  ALTARCH=amd64
fi
LEGACY_CONFIG=.env
OLD_CONFIG=zoa_env.config.old
NEW_CONFIG=zoa_env.config
IBM_CONFIG=.zoa_factory.config
MIGRATE_CONFIG=FALSE
PODMAN_V_REQ=4
PODMAN_R_REQ=0
declare -A PRODUCT_VARIANTS

# Determine which directory to write the log file to
if [ "${TMPDIR}x" != "x" ] && [ -w ${TMPDIR} ]
then
  # TMPDIR is set and is writable
  LOGDIR=${TMPDIR}
elif [ -w /tmp ]
then
  LOGDIR=/tmp
else
  LOGDIR=${HOME}
fi
LOGTSTAMP=$( date +"%Y-%m-%d_%H.%M.%S" )

logToStdout() {
  MSG=${1}
  echo -e "${MSG}${ENDMSG}"
}

logToFile() {
  MSG=${1}
  (
  echo -e "${MSG}${ENDMSG}"
  ) | sed "s%\x1b\[[0-9;]*m%%g" >> ${LOGFILE}
}

logToFileAndStdout() {
  MSG=${1}
  (
  echo -e "${MSG}${ENDMSG}"
  ) | tee >(sed "s%\x1b\[[0-9;]*m%%g" >> ${LOGFILE})
}

if [ "${SCRIPTNAME}" == "podmanDeployZoa.sh" ]
then
  OCI_AGENT=podman
  DISPLAY_NAME=Podman
elif [ "${SCRIPTNAME}" == "dncDeployZoa.sh" ]
then
  OCI_AGENT=docker
  DISPLAY_NAME=Docker
else
  logToStdout "${ERRORMSG}Unsupported deployment script."
  exit 1
fi
LOGFILE=${LOGDIR}/${OCI_AGENT}Deploy.${LOGTSTAMP}_${USER}.log

if [ "${ARCH}" == "s390x" ] || [ "${ARCH}" == "x86_64" ]
then
  IMGFILE_CORE=ZOA-Core-${ARCH}.tar.gz
else
  logToStdout "${ERRORMSG}Unsupported architecture ${ARCH}. Unable to proceed."
  exit 1
fi

setPodmanEnv() {
  ENVPATH=${1}
  sed -i -e "s%^OCI_AGENT=.*$%OCI_AGENT=${OCI_AGENT}%g" ${ENVPATH}
  if [ "${OCI_AGENT}" == "podman" ]
  then
    sed -i -e "s%^OCI_VERSION=.*$%OCI_VERSION=${PV}%g" ${ENVPATH}
  elif [ "${OCI_AGENT}" == "docker" ]
  then
    sed -i -e "s%^OCI_VERSION=.*$%OCI_VERSION=${DV}%g" ${ENVPATH}
  fi
}

checkUtil() {
  UTIL=${1}
  which ${UTIL} > /dev/null 2>&1
  if [ $? -ne 0 ]
  then
    logToFileAndStdout "${ERRORMSG}'${UTIL}' utility is needed, but could not be found in the system path. Unable to proceed."
    RC=1
  else
    logToFile "${INFOMSG}'${UTIL}' utility found in the system path."
  fi
}

checkUtilWarn() {
  UTIL=${1}
  which ${UTIL} > /dev/null 2>&1
  if [ $? -ne 0 ]
  then
    logToFileAndStdout "${WARNMSG}'${UTIL}' utility is recommended, but could not be found in the system path."
    eval has${UTIL}=false
  else
    logToFile "${INFOMSG}'${UTIL}' utility found in the system path."
  fi
}

checkPrereqsInstall() {
  RC=0
  echo ""
  echo "Checking for common installation prerequisites..." | tee -a ${LOGFILE}
  # Check for awk
  checkUtil awk
  # Check for sed
  checkUtil sed
  # Check for host
  # zCX does not have 'host' utility
  hashost=true
  checkUtilWarn host
  # Check for getent
  hasgetent=true
  checkUtilWarn getent
  if [ "${hashost}" == "false" ] && [ "${hasgetent}" == "false" ]
  then
    logToFileAndStdout "${ERRORMSG}Neither host nor getent utility present. Unable to proceed."
    RC=1
  fi
  # Check for OCI_AGENT
  checkUtil ${OCI_AGENT}
  for FEATURE in ${FEATURES}
  do
    echo "Checking installation prerequisites for Feature '${FEATURE}'..."
    FEATURE_UPPER=$( echo ${FEATURE} | tr [:lower:] [:upper:] )
    FEATURE_IMG=$( grep ^IMGFILE_${FEATURE_UPPER} ${SCRIPTDIR}/../${FEATURE}/utils/podmanDeploy-${FEATURE}.sh |  cut -f 2 -d "=" )
    . ${SCRIPTDIR}/../${FEATURE}/utils/podmanDeploy-${FEATURE}.sh checkPrereqsInstall
    FEATURE_IMG_SHORT=$( echo ${FEATURE_IMG} | awk -F "." '{ print $1 }' )
    FEATURE_IMG_ARCH=$( echo ${FEATURE_IMG_SHORT} | awk -F "-" '{ print $NF }' )
    if [ "${FEATURE_IMG_ARCH}" != "${ARCH}" ] && [ "${FEATURE_IMG_ARCH}" != "\${ARCH}" ]
    then
      logToFileAndStdout "\n${ERRORMSG}Installation image for Feature '${FEATURE}' does not match this hardware architecture. \
        Unable to proceed.\n${SPACEMSG}Installation image:    ${FEATURE_IMG}\n${SPACEMSG}Hardware architecture: ${ARCH}\n"
      RC=1
    fi
  done
  echo "" | tee -a ${LOGFILE}
  if [ "${RC}" == "1" ]
  then
    exit 1
  fi
}

checkPrereqsRuntime() {
  RC=0
  echo "Checking for common runtime prerequisites..." | tee -a ${LOGFILE}
  # Check whether SELinux is enabled
  which selinuxenabled  > /dev/null 2>&1
  SELRC=$?
  if [ ${SELRC} -eq 0 ]
  then
    # selinuxenabled exists; run it
    selinuxenabled
    SERC=$?
    if [ ${SERC} -eq 0 ]
    then
      # SELinux is enabled; issue warning
      logToStdout "${WARNMSG}SELinux is enabled; you must disable it before using this product."
      echo ""
    fi
  fi
  # Check for curl
  checkUtil curl
  # Check for OCI_AGENT
  checkUtil ${OCI_AGENT}
  # Check for podman version
  if [ "${RC}" == "0" ]
  then
    if [ "${OCI_AGENT}" == "podman" ]
    then
      PVF=$( podman -v | awk '{ print $3 }' | tr -d "," )
      PV=$( echo ${PVF} | cut -f 1 -d "." )
      PR=$( echo ${PVF} | cut -f 2 -d "." )
      logToFile "${INFOMSG}Found Podman v${PV}r${PR}."
      if [ ${PV} -lt ${PODMAN_V_REQ} ]
      then
        logToFileAndStdout "${ERRORMSG}Podman v${PODMAN_V_REQ}r${PODMAN_R_REQ} is the minimum required version. Version found: ${PVF}"
        RC=1
      elif [ ${PV} -eq ${PODMAN_V_REQ} ] && [ ${PR} -lt ${PODMAN_R_REQ} ]
      then
        logToFileAndStdout "${ERRORMSG}Podman v${PODMAN_V_REQ}r${PODMAN_R_REQ} is the minimum required version. Version found: ${PVF}"
        RC=1
      fi
    elif [ "${OCI_AGENT}" == "docker" ]
    then
      DVF=$( docker -v | awk '{ print $3 }' | tr -d "," )
      DV=$( echo ${DVF} | cut -f 1 -d "." )
      DR=$( echo ${DVF} | cut -f 2 -d "." )
      # Strip off leading 0's
      DR=$( echo $(( 10#${DR} )) )
      logToFile "${INFOMSG}Found Docker v${DV}r${DR}."
      if [ ${DV} -lt 20 ] && [ ${DV} -ge 18 ]
      then
        logToFileAndStdout "${WARNMSG}It is highly recommended that you use Docker v20r10 or higher. Version found: ${DVF}"
      fi
      # zCX comes with Docker v18.09.7
      if [ ${DV} -lt 18 ]
      then
        logToFileAndStdout "${ERRORMSG}Docker v18 is the minimum required version. Version found: ${DVF}"
        RC=1
      #elif [ ${DV} -eq 20 ] && [ ${DR} -lt 9 ]
      #then
      #  logToFileAndStdout "${ERRORMSG}Docker v20r10 is the minimum required version. Version found: ${DVF}"
      #  RC=1
      fi
    fi
  fi
  for FEATURE in ${FEATURES}
  do
    echo "Checking runtime prerequisites for Feature '${FEATURE}'..."
    . ${SCRIPTDIR}/../${FEATURE}/utils/podmanDeploy-${FEATURE}.sh checkPrereqsRuntime
  done
  echo "" | tee -a ${LOGFILE}
  if [ "${RC}" == "1" ]
  then
    exit 1
  fi
}

checkPrereqsRemove() {
  RC=0
  echo ""
  echo "Checking for common uninstallation prerequisites..." | tee -a ${LOGFILE}
  # Check for OCI_AGENT
  checkUtil ${OCI_AGENT}
  for FEATURE in ${INSTALLED_FEATURES}
  do
    echo "Checking uninstallation prerequisites for Feature '${FEATURE}'..."
    . ${SCRIPTDIR}/utils/podmanDeploy-${FEATURE}.sh checkPrereqsRemove
  done
  if [ "${RC}" == "1" ]
  then
    exit 1
  fi
}

checkPodmanAccess() {
  RC=0
  echo "Checking for ${DISPLAY_NAME} access..." | tee -a ${LOGFILE}
  ${OCI_AGENT} images >> ${LOGFILE} 2>&1
  RC=$?
  if [ "${RC}" != "0" ]
  then
    echo "This user ID is unable to successfully run ${DISPLAY_NAME} commands; RC=${RC}." | tee -a ${LOGFILE}
    echo "Unable to proceed." | tee -a ${LOGFILE}
    echo "See log file for more information."
    echo "" | tee -a ${LOGFILE}
    exit 1
  fi
  echo "" | tee -a ${LOGFILE}
}

migrateKcDb() {
  logToFileAndStdout "${INFOMSG}Found existing authentication service data volume. Checking whether a database upgrade is required..."
  mkdir -p ${DCPATH}/kcbackup
  NOW=$( date +"%Y%m%d%H%M%S" )
  ${OCI_AGENT} run -d -t -u ${ZOA_UID}:0 --rm --name kcdb-util --entrypoint /bin/bash -v ${COMPOSE_PROJECT_NAME}_zaiops_keycloak:/kcdb:rw ibm-zaiops/zoa-service-discovery:${TAG}-${ARCH} > /dev/null
  ${OCI_AGENT} cp ${DCPATH}/tools/kcdb_mig.tar.gz kcdb-util:/tmp
  ${OCI_AGENT} exec kcdb-util bash -c "cd /tmp ; tar xf kcdb_mig.tar.gz ; ./kcdbmig/migrate-db.sh /kcdb ; rm -Rf kcdbmig kcdb_mig.tar.gz"
  ${OCI_AGENT} cp kcdb-util:/tmp/kcdbdump.zip ${DCPATH}/kcbackup/ 2>/dev/null && mv ${DCPATH}/kcbackup/kcdbdump.zip ${DCPATH}/kcbackup/kcdbdump_${NOW}.zip
  ${OCI_AGENT} stop kcdb-util > /dev/null
}

# Generic password prompt
pwdPrompt() {
  unset GPWD
  unset TGPWD
  stty -echo
  while [[ -z "$GPWD" ]]; do
    echo "Enter password:"
    read -e GPWD
  done
  echo ""
  while [[ -z "$TGPWD" ]]; do
    echo "Confirm password:"
    read -e TGPWD
  done
  stty echo
}

createTarget() {
  DCPATH=${1}
  ROOTPATH=${2}
  echo ${DCPATH} | grep -q ^\/
  if [ $? != 0 ]
  then
    # DCPATH is not an absolute path
    DCPATH=${ROOTPATH}/${DCPATH}
  fi
  if [ -e ${DCPATH} ]
  then
    if [ ! -d ${DCPATH} ]
    then
      logToFileAndStdout "${ERRORMSG}${DCPATH} is not a valid directory. Unable to proceed.\n"
      exit 1
    elif [ ! -w ${DCPATH} ]
    then
      logToFileAndStdout "${ERRORMSG}Cannot write to ${DCPATH}. Unable to proceed.\n"
      exit 1
    fi
  else
    if [ "${CREATEDIR}x" == "x" ]
    then
      while [ "${CREATEDIR}" != "Y" ] && [ "${CREATEDIR}" != "y" ] &&
            [ "${CREATEDIR}" != "N" ] && [ "${CREATEDIR}" != "n" ]
      do
        echo "${DCPATH} does not exist. Do you want to create it? (Y/n)"
        read -e CREATEDIR
        CREATEDIR=${CREATEDIR:-"y"}   # accept no input as "YES"
      done
    fi
    if [ "${CREATEDIR}" == "Y" ] || [ "${CREATEDIR}" == "y" ]
    then
      mkdir -p "${DCPATH}" 2>/dev/null
      DIRRES=$?
      if [ ${DIRRES} -ne 0 ]
      then
        logToFileAndStdout "${ERRORMSG}Failed to create ${DCPATH}. Unable to proceed.\n"
        exit 1
      else
        echo "${DCPATH} created." | tee -a ${LOGFILE}
        echo "" | tee -a ${LOGFILE}
      fi
    else
      echo "Unable to proceed." | tee -a ${LOGFILE}
      echo "" | tee -a ${LOGFILE}
      exit 1
    fi
  fi
}

createPodmanFolders() {
  DCPATH=${1}
  # Path for gateway/kafka volumes
  mkdir -p ${DCPATH}/logs/install ${DCPATH}/config
}

migrateConfig() {
  DCPATH=${1}
  if [ -f ${DCPATH}/${LEGACY_CONFIG}.old ]
  then
    . ${DCPATH}/${LEGACY_CONFIG}.old
  fi
  if [ -f ${DCPATH}/${OLD_CONFIG} ]
  then
    . ${DCPATH}/${OLD_CONFIG}
  fi
  # List of ZOACOMMON variables
  VARLIST_COMMON="COUNTRY \
    CRYPT_ALGO \
    IZOA_GATEWAY_PORT \
    LOCATION \
    LOGGING_DRIVER \
    LOGGING_MODE \
    ORG_NAME \
    ORG_UNIT \
    STATE \
    ZAIOPS_GATEWAY_REQUESTS_PER_MIN \
    ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST \
    ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT \
    ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT \
    ZAIOPS_KAFKA_RETENTION_HOURS \
    ZAIOPS_KEYCLOAK_ADMIN_PASS \
    ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED \
    ZAIOPS_KEYCLOAK_LOGLEVEL \
    ZAIOPS_KEYCLOAK_METRICAPI_ENABLED \
    ZAIOPS_KEYCLOAK_PORT \
    ZAIOPS_KEYCLOAK_TIMEOUT \
    ZAIOPS_TLS_VERSION \
    ZAIOPS_ZOAREALM_ADMIN_PASS \
    ZAIOPS_ZOASVC_PASS"
  for VAR in ${VARLIST_COMMON}
  do
    eval VALUE=\"\$${VAR}\"
    if [ "${VALUE}x" == "x" ]
    then # do nothing; value is empty
      :
    elif [[ "${VALUE}" =~ .*\ .* ]]
    then # handle variable values with spaces
      sed -i -e "s%^${VAR}=.*$%${VAR}=\"${VALUE}\"%g" ${DCPATH}/${NEW_CONFIG}.all
    else # handle 'single-word' variable values
      sed -i -e "s%^${VAR}=.*$%${VAR}=${VALUE}%g" ${DCPATH}/${NEW_CONFIG}.all
    fi
  done
  for FEATURE in ${FEATURES}
  do
    echo "Migrating configuration for Feature '${FEATURE}'..."
    . ${SCRIPTDIR}/../${FEATURE}/utils/podmanDeploy-${FEATURE}.sh migrateConfig ${DCPATH}
  done
}

podmanUnpackCore() {
  cd ${SCRIPTDIR}
  # Expand OCI artifacts into user-selected directory
  if [ "${DCPATH}x" == "x" ]
  then
    echo "Please specify the absolute path into which you would like to place the ${DISPLAY_NAME} artifacts:"
    read -e DCPATH
  fi
  # Strip trailing slashes off DCPATH for cleaner display
  DCPATH=$( echo ${DCPATH} | sed -e "s%\/*$%%g" )
  if [ -f ${DCPATH}/bin/podmanManageZoa.sh ]
  then
    echo "Shutting down services."
    ${DCPATH}/bin/podmanManageZoa.sh down
  fi
  echo "" | tee -a ${LOGFILE}
  createTarget ${DCPATH} ${SCRIPTDIR}
  # Make sure DCPATH is an absolute path
  DCPATH="$( cd ${DCPATH} && pwd )"
  logToFileAndStdout "\n${INFOMSG}Absolute installation path is ${DCPATH}.\n"
  createPodmanFolders ${DCPATH}
  # See whether prior configuration files exist and source them for configuration migration
  echo "Checking for prior configuration files..." | tee -a ${LOGFILE}
  # If .env file is still around, make it writeable and rename it to .env.old
  if [ -f ${DCPATH}/${LEGACY_CONFIG} ]
  then
    mkdir -p ${DCPATH}/backup_legacy_${LOGTSTAMP}
    cd ${DCPATH}
    tar czf backup_legacy_${LOGTSTAMP}/backup.tar.gz *.yml ${LEGACY_CONFIG} bin
    cd - > /dev/null
    chmod u+w ${DCPATH}/${LEGACY_CONFIG} && mv -f ${DCPATH}/${LEGACY_CONFIG} ${DCPATH}/${LEGACY_CONFIG}.old 2>&1 | tee -a ${LOGFILE}
    MIGRATE_CONFIG=TRUE
  fi
  # If new customer configuration file is present, make it writeable and rename it to zoa_env.config.old
  if [ -f ${DCPATH}/${NEW_CONFIG} ]
  then
    mkdir -p ${DCPATH}/backup_${LOGTSTAMP}
    cd ${DCPATH}
    tar czf backup_${LOGTSTAMP}/backup.tar.gz *.yml ${NEW_CONFIG} ${IBM_CONFIG} bin
    cd - > /dev/null
    chmod u+w ${DCPATH}/${NEW_CONFIG} && mv -f ${DCPATH}/${NEW_CONFIG} ${DCPATH}/${OLD_CONFIG} 2>&1 | tee -a ${LOGFILE}
    MIGRATE_CONFIG=TRUE
  fi
  echo "" | tee -a ${LOGFILE}
  echo "Restoring core services images from archive..." | tee -a ${LOGFILE}
  echo "" | tee -a ${LOGFILE}
  tar xzf ${IMGFILE_CORE} -C ${DCPATH} 2>&1 | tee -a ${LOGFILE}
  # Make sure zoa_env.config and .zoa_factory.config don't get overwritten
  mv ${DCPATH}/zoa_env.config ${DCPATH}/zoa_env.config.all
  mv ${DCPATH}/.zoa_factory.config ${DCPATH}/.zoa_factory.config.all
  # Allow podmanManageZoa to be invoked as dncManageZoa
  cd ${DCPATH}/bin
  ln -sf podmanManageZoa.sh dncManageZoa.sh
  cd - > /dev/null 2>&1
}

findElement() {
  local e match="$1"
  shift
  for e; do [[ "$e" == "$match" ]] && return 0; done
  return 1
}

testRoute() {
  THISIP=${1}
  ROUTABLE=tbd
  while [ "${ROUTABLE}" != "true" ]
  do
    echo ${THISIP} | grep -qE '^(172\.1[6-9]\.|172\.2[0-9]\.|172\.3[0-1]\.|10\.|192\.168\.)'
    if [ $? -eq 0 ]
    then
      ROUTABLE=false
      echo "IP address '${THISIP}' is not in a routable address space and therefore not reachable from other networks."
      echo "Do you want to use it anyway? (y/N)"
      read -e USEIP
      USEIP=${USEIP:="n"}
      USEIP=$( echo ${USEIP} | tr [:upper:] [:lower:] )
      if [ "${USEIP}" == "y" ]
      then
        ROUTABLE=true
      else
        echo "Provide a routable IP address that can be used to reach this system:"
        read -e THISIP
        testIP ${THISIP}
      fi
    else
      ROUTABLE=true
    fi
  done
}

testIP() {
  THISIP=${1}
  VALID=tbd
  while [ "${VALID}" != "true" ]
  do
    echo ${THISIP} | grep -qE "^([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})$"
    if [ $? -eq 0 ]
    then
      VALID=true
    else
      VALID=false
      echo "IP address '${THISIP}' is not a well-formed address."
      echo "Provide a well-formed IP address:"
      read -e THISIP
    fi
  done
}

podmanInstall() {
  DCPATH=${1}
  SCENARIO=${2}
  cd ${SCRIPTDIR}
  # Determine hostname of the system
  THISHOST=$( hostname -f )
  THISHOST_LOWER=$( hostname -f | tr [:upper:] [:lower:] )
  echo ${THISHOST_LOWER} | grep -qE '(\.)'
  if [ $? -eq 0 ]
  then
    HASFQDN="TRUE"
  else
    HASFQDN="FALSE"
  fi
  if [ "${hashost}" == "false" ]
  then
    IPARRAY=( $(getent ahosts ${THISHOST_LOWER} | grep -v "::" | grep STREAM | awk '{ print $1 }') )
  else
    IPARRAY=( $(host ${THISHOST_LOWER} | grep -vi IPv6 | awk '{ print $NF }') )
    # Alphabetic characters mean invalid IP address(es)
    echo "${IPARRAY}" | grep -qE '(.*[a-zA-Z].*)'
    if [ $? -eq 0 ]
    then
      # 'host' command did not return any IP addresses; try getent instead
      IPARRAY=( $(getent ahosts ${THISHOST_LOWER} | grep -v "::" | grep STREAM | awk '{ print $1 }') )
    fi
  fi
  IPCOUNT=${#IPARRAY[@]}
  TOPIP=${IPARRAY[0]}
  if [ ${IPCOUNT} -eq 0 ]
  then
    logToStdout "${ERRORMSG}No IP addresses found. Unable to proceed."
    exit 1
  elif [ ${IPCOUNT} -eq 1 ]
  then
    THISIP=${TOPIP}
  else
    IPLIST="${TOPIP}"
    for (( n=1; n<${IPCOUNT}; n++ ))
    do
      IPLIST="${IPLIST}, ${IPARRAY[n]}"
    done
    if [ "${USEFIRSTIP}" == "Y" ] || [ "${USEFIRSTIP}" == "y" ]
    then
      THISIP=${TOPIP}
    else
      echo ""
      echo "This system reports multiple IPv4 addresses (${IPLIST})."
      echo "Which of these IP addresses can be accessed from the browser you will use"
      echo "to access this product's web user interface? (default: ${TOPIP})"
      read THISIP
      THISIP=${THISIP:-${TOPIP}}
      while :
      do
        findElement "${THISIP}" "${IPARRAY[@]}"
        if [ $? -eq 0 ]
        then
          break
        else
          echo ""
          echo "Invalid answer. The only recognized IP addresses are ${IPLIST}."
          echo "Please try again:"
          read THISIP
          THISIP=${THISIP:-${TOPIP}}
        fi
      done
    fi
    echo ""
    echo "The following IP address will be used for product configuration: ${THISIP}"
    echo ""
  fi
  testRoute ${THISIP}
  if [ "${MIGRATE_CONFIG}" == "TRUE" ]
  then
    # Migrate configuration settings from prior installations
    migrateConfig ${DCPATH}
  else
    # Update config file with system's hostname
    if [ "${HASFQDN}" == "TRUE" ]
    then
      sed -i -e "s%^ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${THISHOST_LOWER}%g" ${DCPATH}/zoa_env.config.all
      sed -i -e "s%^CDP_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%CDP_KAFKA_BOOTSTRAP_SERVER_HOST=${THISHOST_LOWER}%g" ${DCPATH}/zoa_env.config.all
    else
      sed -i -e "s%^ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${THISIP}%g" ${DCPATH}/zoa_env.config.all
      sed -i -e "s%^CDP_KAFKA_BOOTSTRAP_SERVER_HOST=.*$%CDP_KAFKA_BOOTSTRAP_SERVER_HOST=${THISIP}%g" ${DCPATH}/zoa_env.config.all
    fi
  fi
  # Update settings that always need to be (re-)set
  if [ "${HASFQDN}" == "TRUE" ]
  then
    sed -i -e "s%^ZAIOPS_KEYCLOAK_HOST=.*$%ZAIOPS_KEYCLOAK_HOST=${THISHOST_LOWER}%g" ${DCPATH}/.zoa_factory.config.all
    sed -i -e "s%^EXTERNAL_GATEWAY_HOST=.*$%EXTERNAL_GATEWAY_HOST=${THISHOST_LOWER}%g" ${DCPATH}/.zoa_factory.config.all
  else
    sed -i -e "s%^ZAIOPS_KEYCLOAK_HOST=.*$%ZAIOPS_KEYCLOAK_HOST=${THISIP}%g" ${DCPATH}/.zoa_factory.config.all
    sed -i -e "s%^EXTERNAL_GATEWAY_HOST=.*$%EXTERNAL_GATEWAY_HOST=${THISIP}%g" ${DCPATH}/.zoa_factory.config.all
  fi
  sed -i -e "s%^ZAIOPS_KEYCLOAK_IP=.*$%ZAIOPS_KEYCLOAK_IP=${THISIP}%g" ${DCPATH}/.zoa_factory.config.all
  # Indicate to downstream utilities that Docker Compose is not to be used even if present
  if [ -s "${DCPATH}/.zoa_factory.config.all" ] && \
   [ "$(tail -c1 "${DCPATH}/.zoa_factory.config.all")" != $'\n' ]; then
  echo >> "${DCPATH}/.zoa_factory.config.all"
  fi
  echo "HAS_COMPOSE=false" >> ${DCPATH}/.zoa_factory.config.all
  # Make sure correct OCI agent is set
  setPodmanEnv ${DCPATH}/.zoa_factory.config.all
  # Source zoa_env.config and .zoa_factory.config files to obtain variables needed for some checks
  . ${DCPATH}/zoa_env.config.all
  . ${DCPATH}/.zoa_factory.config.all
  if [ "${OCI_AGENT}" == "docker" ]
  then
    if [ ${OCI_VERSION} -ge 20 ]
    then
      COMMON_CONTAINER_PREFIX="zoa-"
    else
      COMMON_CONTAINER_PREFIX=""
    fi
  elif [ "${OCI_AGENT}" == "podman" ]
  then
    COMMON_CONTAINER_PREFIX=""
  fi
  ${DCPATH}/bin/utils/keycloak/migrateKC.sh -e 2>&1 | tee -a ${LOGFILE}
  RC=${PIPESTATUS[0]}
  if [ ${RC} -ne 0 ]
  then
    logToStdout "\n${ERRORMSG}Attempted export of user and configuration data from authentication service failed.\n${SPACEMSG}Shutting down deployment process to prevent data loss.\n"
    mv -f ${DCPATH}/zoa_env.config.all ${DCPATH}/zoa_env.config.upgrade
    mv -f ${DCPATH}/zoa_env.config.old ${DCPATH}/zoa_env.config
    mv -f ${DCPATH}/.zoa_factory.config.all ${DCPATH}/.zoa_factory.config
    exit 1
  fi
  # Remove any old OCI images
  echo "Removing old core services images from local repository..." | tee -a ${LOGFILE}
  export RMRESPONSE=Y
  podmanRemove ${DCPATH}
  unset RMRESPONSE
  echo ""
  # Check whether Podman project already exists
  ${OCI_AGENT} volume ls | grep -q ${COMPOSE_PROJECT_NAME}
  DPEXISTS=$?
  if [ ${DPEXISTS} -eq 0 ]
  then
    echo "Volumes associated with ${DISPLAY_NAME} project ${COMPOSE_PROJECT_NAME} already exist and will be re-used." | tee -a ${LOGFILE}
    echo "" | tee -a ${LOGFILE}
  fi
  # Restore OCI images
  cd ${DCPATH}
  echo "Loading new core services images into local repository..." | tee -a ${LOGFILE}
  STATUS=SUCCESS
  ${OCI_AGENT} load -q -i zoacore_images.tar >> ${LOGFILE} 2>&1
  RC=$?
  if [ ${RC} -eq 0 ]
  then
    echo "Successfully loaded images." | tee -a ${LOGFILE}
    rm -f zoacore_images.tar
    for IMG in $(${OCI_AGENT} images -f 'label=feature=IBM Z AIOps - Common Services - Core' -q)
    do
      TAGS=$( ${OCI_AGENT} inspect --format '{{ index .RepoTags }}' ${IMG} | tr -d "[]" )
      IMG=$( echo ${TAGS} | cut -f 3 -d "/" )
      IMGNAME=$( echo ${IMG} | cut -f 1 -d ":" )
      IMGTAG=$( echo ${IMG} | cut -f 2 -d ":" )
      # Re-tag images for a more user-friendly naming convention
      ${OCI_AGENT} tag icr.io/zoa-oci/${IMGNAME}:${IMGTAG} ibm-zaiops/${IMGNAME}:${IMGTAG} >> ${LOGFILE} 2>&1
      ${OCI_AGENT} rmi icr.io/zoa-oci/${IMGNAME}:${IMGTAG} >> ${LOGFILE} 2>&1
    done
  else
    logToFileAndStdout "${ERRORMSG}Failed to load images. See ${LOGFILE} for details."
    STATUS=FAILURE
  fi
  # Copy this script into install directory
  mkdir -p ${DCPATH}/bin
  cp ${SCRIPTDIR}/${SCRIPTNAME} ${DCPATH}/bin
  cat ${SCRIPTDIR}/sample_remove.rsp > ${DCPATH}/bin/sample_remove.rsp
  # Summarize status
  if [ "${STATUS}" == "SUCCESS" ]
  then
    echo "" | tee -a ${LOGFILE}
    echo "All core services images were loaded successfully." | tee -a ${LOGFILE}
  else
    echo "" | tee -a ${LOGFILE}
    echo "Loading of at least one image failed." | tee -a ${LOGFILE}
    echo "The IBM Z AIOps core  services have not been successfully installed." | tee -a ${LOGFILE}
    echo "Please investigate and resolve the issue and re-run this script before attempting to use these services." | tee -a ${LOGFILE}
    echo "" | tee -a ${LOGFILE}
    exit 1
  fi
}

podmanRemove() {
  DCPATH=${1}
  echo "" | tee -a ${LOGFILE}
  if [ "${DCPATH}x" == "x" ]
  then
    DCPATH="$( cd ${SCRIPTDIR}/.. && pwd )"
  fi
  if [ "${RMRESPONSE}x" == "x" ]
  then
    while [ "${RMRESPONSE}" != "Y" ] && [ "${RMRESPONSE}" != "y" ] &&
          [ "${RMRESPONSE}" != "N" ] && [ "${RMRESPONSE}" != "n" ]
    do
      echo "You are about to remove all Z AIOps images from the local ${DISPLAY_NAME} repository."
      echo "Are you sure you want to continue? (y/N)"
      read -e RMRESPONSE
      RMRESPONSE=${RMRESPONSE:-"n"}   # accept no input as "NO"
    done
  fi
  if [ "${RMRESPONSE}" == "Y" ] || [ "${RMRESPONSE}" == "y" ]
  then
    if [ -f ${DCPATH}/bin/podmanManageZoa.sh ]
    then
      echo "Shutting down running containers..." | tee -a ${LOGFILE}
      if [ ! -f ${DCPATH}/zoa_env.config ] || [ -f ${DCPATH}/zoa_env.config.all ]
      then
        # We are in install mode and don't have a zoa_env.config or .zoa_factory.config file yet
        ln -sf ${DCPATH}/zoa_env.config.all ${DCPATH}/zoa_env.config
        ln -sf ${DCPATH}/.zoa_factory.config.all ${DCPATH}/.zoa_factory.config
      fi
      ${DCPATH}/bin/podmanManageZoa.sh down 2>/dev/null | tee -a ${LOGFILE}
      if [ -h ${DCPATH}/zoa_env.config ]
      then
        # We are in install mode and zoa_env.config is just a symbolic link
        rm ${DCPATH}/zoa_env.config
      fi
      if [ -h ${DCPATH}/.zoa_factory.config ]
      then
        # We are in install mode and .zoa_factory.config is just a symbolic link
        rm ${DCPATH}/.zoa_factory.config
      fi
    fi
    echo "Removing OCI images..." | tee -a ${LOGFILE}
    IMGLIST="$(${OCI_AGENT} images -f 'label=feature=IBM Z AIOps - Common Services - Core' -q) $(${OCI_AGENT} images -f 'label=feature=IBM Z AIOps - Common Services' -q)"
    if [ "$( echo ${IMGLIST} | tr -d " " )x" == "x" ]
    then
      echo "No images to remove." | tee -a ${LOGFILE}
    else
      ${OCI_AGENT} rmi -f ${IMGLIST} 2>&1 | tee -a ${LOGFILE}
    fi
    ${OCI_AGENT} network rm -f ${COMPOSE_PROJECT_NAME}_zaiops
    echo "Done." | tee -a ${LOGFILE}
  fi
  # Clean
}

podmanPurge() {
  if [ "${DCPATH}x" == "x" ]
  then
    DCPATH="$( cd ${SCRIPTDIR}/.. && pwd )"
  fi
  if [ "${PURGERESPONSE}x" == "x" ]
  then
    while [ "${PURGERESPONSE}" != "Y" ] && [ "${PURGERESPONSE}" != "y" ] &&
          [ "${PURGERESPONSE}" != "N" ] && [ "${PURGERESPONSE}" != "n" ]
    do
      echo "You are about to remove all Z AIOps images from the local ${DISPLAY_NAME} repository AND"
      echo "     and delete the data volumes associated with them."
      echo ""
      echo "Are you sure you want to continue? (y/N)"
      read -e PURGERESPONSE
      PURGERESPONSE=${PURGERESPONSE:-"n"}   # accept no input as "NO"
    done
  fi
  if [ "${PURGERESPONSE}" == "Y" ] || [ "${PURGERESPONSE}" == "y" ]
  then
    RMRESPONSE="Y"
    mkdir -p ${DCPATH}/logs/install
    cp ${LOGFILE} ${DCPATH}/logs/install
    echo "Log output was written to ${LOGFILE}."
    cp ${LOGFILE} ${DCPATH}/logs/install
    if [ -f ${DCPATH}/bin/podmanManageZoa.sh ]
    then
      ${DCPATH}/bin/podmanManageZoa.sh gather
      ${DCPATH}/bin/podmanManageZoa.sh purge SILENT
    fi
    podmanRemove
    for FEATURE in ${INSTALLED_FEATURES}
    do
      clear -x
      echo "Cleaning up Feature '${FEATURE}'..." | tee -a ${LOGFILE}
      if [ -f ${SCRIPTDIR}/utils/podmanDeploy-${FEATURE}.sh ]
      then
        . ${SCRIPTDIR}/utils/podmanDeploy-${FEATURE}.sh remove
      else
        echo "Deployment management script for Feature '${FEATURE}' is not installed." | tee -a ${LOGFILE}
      fi
    done
    rm -Rf ${DCPATH}/zoa_env.config ${DCPATH}/.zoa_factory.config ${DCPATH}/bin ${DCPATH}/config ${DCPATH}/samples ${DCPATH}/*docker-compose*.yml ${DCPATH}/logs ${DCPATH}/*.unmanaged ${DCPATH}/*.fragment ${DCPATH}/zoasvc.tls ${DCPATH}/product.inventory
  fi
}

writeInventory() {
  (
  cat <<- END
# List of installed primary and dependent features
INSTALLED_FEATURES="${FEATURES}"

END
  ) > ${DCPATH}/product.inventory
  RECORD=1
  for PF in ${PRODUCT_LIST}
  do
    PROD=$( echo ${PF} | cut -f 1 -d "_" )
    FEAT=$( echo ${PF} | cut -f 2 -d "_" )
    VAR=${PRODUCT_VARIANTS[${PROD}]}
    (
    cat <<- END
# Details for primary feature ${RECORD}
FEATURE_${RECORD}=${FEAT}
PRODUCT_${RECORD}=${PROD}
VARIANT_${RECORD}=${VAR}

END
) >> ${DCPATH}/product.inventory
  ((RECORD++))
  done
}

askFeatures() {
  DO_PROMPT=true
  echo "" | tee -a ${LOGFILE}
  if [ "${FEATURES}x" == "x" ]
  then
    SPECLIST=$( find ${SCRIPTDIR}/../ -maxdepth 2 -name .featurespec -type f )
  else
    DO_PROMPT=false
    SPECLIST=$( for ITEM in ${FEATURES} ; do if [ -d ${SCRIPTDIR}/../${ITEM}/ ] ; then find ${SCRIPTDIR}/../${ITEM}/ -maxdepth 1 -name .featurespec -type f ; fi ; done )
  fi
  for SPEC in ${SPECLIST}
  do
    FEATURE_VALUE=$( basename $( dirname ${SPEC} ) )
    . ${SPEC}
    OPTIONS_VALUES+=("${FEATURE_VALUE}")
    OPTIONS_LABELS+=("${DISPLAY_NAME}")
    PRODUCT_FEATURES+=("${PRODUCT}_${FEATURE_VALUE}")
    if [ "${DEPENDENT_FEATURES}x" != "x" ]
    then
      OPTIONS_DEPENDENTS+=("${DEPENDENT_FEATURES}")
    else
      OPTIONS_DEPENDENTS+=("none")
    fi
    if [ "${VARIANT}x" == "x" ]
    then
      VARIANT="none"
    fi
    if [ "${PRODUCT_VARIANTS[${PRODUCT}]}" != "" ] && [ "${PRODUCT_VARIANTS[${PRODUCT}]}" != "${VARIANT}" ]
    then
      logToStdout "${ERRORMSG}'${BASEDIR}' contains features from multiple product variants for Product '${PRODUCT}'. \
        \n${SPACEMSG}Variants found: \
        \n${SPACEMSG}- ${PRODUCT_VARIANTS[${PRODUCT}]} \
        \n${SPACEMSG}- ${VARIANT} \
        \n${SPACEMSG}Unable to proceed.\n"
      exit 1
    else
      if [ "${VARIANT}x" != "x" ]
      then
        PRODUCT_VARIANTS[${PRODUCT}]=${VARIANT}
      else
        PRODUCT_VARIANTS[${PRODUCT}]="none"
      fi
    fi
  done
  for i in "${!OPTIONS_DEPENDENTS[@]}"
  do
    if [ "${OPTIONS_DEPENDENTS[$i]}" != "none" ]
    then
      for DEPFEAT in ${OPTIONS_DEPENDENTS[$i]}
      do
        for j in "${!OPTIONS_VALUES[@]}"
        do
          if [ "${DEPFEAT}" == "${OPTIONS_VALUES[$j]}" ]
          then
            OPTIONS_VALUES=( "${OPTIONS_VALUES[@]:0:$j}" "${OPTIONS_VALUES[@]:$((j+1))}" )
            OPTIONS_LABELS=( "${OPTIONS_LABELS[@]:0:$j}" "${OPTIONS_LABELS[@]:$((j+1))}" )
            OPTIONS_DEPENDENTS=( "${OPTIONS_DEPENDENTS[@]:0:$j}" "${OPTIONS_DEPENDENTS[@]:$((j+1))}" )
            PRODUCT_FEATURES=( "${PRODUCT_FEATURES[@]:0:$j}" "${PRODUCT_FEATURES[@]:$((j+1))}" )
          fi
        done
      done
    fi
  done
  for i in "${!OPTIONS_VALUES[@]}"
  do
    OPTIONS_STRING+="${OPTIONS_LABELS[$i]};"
  done
  if [ "${OPTIONS_STRING}x" != "x" ]
  then
    CHECKED=""
    DEPENDENTS=""
    PRODFEAT=""
    PIEXT=""
    if [ "${DO_PROMPT}" == "true" ]
      then
      echo "--------------------------------------------------------------------------------"
      echo ""
      echo "Which Z AIOps features would you like to deploy?"
      echo "  - Use the UP and DOWN arrows to navigate between options."
      echo "  - Use the SPACE BAR to toggle selection of an option."
      echo "  - Use the ENTER/RETURN key to submit your completed selection."
      echo ""
      prompt_for_multiselect SELECTED "$OPTIONS_STRING"
      for i in "${!SELECTED[@]}"
      do
        if [ "${SELECTED[$i]}" == "true" ]
        then
          CHECKED+=("${OPTIONS_VALUES[$i]}")
          DEPENDENTS+=("${OPTIONS_DEPENDENTS[$i]}")
          PRODFEAT+=("${PRODUCT_FEATURES[$i]}")
          PIEXT+=("${PI_EXTENSIONS_LIST[$i]}")
        fi
      done
    else
      for i in "${!OPTIONS_VALUES[@]}"
      do
        CHECKED+=("${OPTIONS_VALUES[$i]}")
        DEPENDENTS+=("${OPTIONS_DEPENDENTS[$i]}")
        PRODFEAT+=("${PRODUCT_FEATURES[$i]}")
        PIEXT+=("${PI_EXTENSIONS_LIST[$i]}")
      done
    fi
    FEATURES="${CHECKED[@]}"
    FEATURE_DEPENDENCIES="${DEPENDENTS[@]}"
    PRODUCT_LIST="${PRODFEAT[@]}"
  else
    echo "--------------------------------------------------------------------------------"
    echo ""
    echo "No features available for installation." | tee -a ${LOGFILE}
    FEATURES=""
    FEATURE_DEPENDENCIES=""
    PRODUCT_LIST=""
    echo ""
  fi
  FEATURES_RAW="$( echo ${FEATURES[@]} ${FEATURE_DEPENDENCIES[@]/none} )"
  FEATURES=$( for ITEM in ${FEATURES_RAW} ; do echo ${ITEM} ; done | sort -u )
  FEATURES=$( echo ${FEATURES} )
}

usage() {
  echo ""
  echo "Usage:"
  echo "  ${SCRIPTNAME} --install | -i                to restore IBM Z Operational Analytics OCI images from archive and load them"
  echo "                                                   into the local ${DISPLAY_NAME} repository"
  echo "  ${SCRIPTNAME} --install | -i install.rsp    to install in silent mode; requires a response file named install.rsp"
  echo "  ${SCRIPTNAME} --remove  | -r                to remove all IBM Z Operational Analytics OCI images from the local ${DISPLAY_NAME}"
  echo "                                                   repository; leaves ${DISPLAY_NAME} volumes, configuration files and TLS certificates behind"
  echo "  ${SCRIPTNAME} --remove  | -r remove.rsp     to remove in silent mode; requires a response file named remove.rsp"
  echo "  ${SCRIPTNAME} --clean   | -c                to remove all IBM Z Operational Analytics OCI images from the local ${DISPLAY_NAME}"
  echo "                                                   repository AND purge ${DISPLAY_NAME} volumes, configuration files and TLS certificates"
  echo "  ${SCRIPTNAME} --clean   | -c remove.rsp     to clean in silent mode; requires a response file named remove.rsp"
  echo ""
}

# Adapted from StackOverflow: https://stackoverflow.com/a/54261882/317605 (via https://gist.github.com/sergiofbsilva/099172ea597657b0d0008dc367946953)
prompt_for_multiselect() {
    # Helpers for terminal print control and key input
    ESC=$( printf "\033")
    cursor_blink_on()   { printf "$ESC[?25h"; }
    cursor_blink_off()  { printf "$ESC[?25l"; }
    cursor_to()         { printf "$ESC[$1;${2:-1}H"; }
    print_inactive()    { printf "$2   $1 "; }
    print_active()      { printf "$2  $ESC[7m $1 $ESC[27m"; }
    get_cursor_row()    { IFS=';' read -sdR -p $'\E[6n' ROW COL; echo ${ROW#*[}; }
    key_input()         {
      local key
      IFS= read -rsn1 key 2>/dev/null >&2
      if [[ $key = ""      ]]; then echo enter; fi;
      if [[ $key = $'\x20' ]]; then echo space; fi;
      if [[ $key = $'\x1b' ]]; then
        read -rsn2 key
        if [[ $key = [A ]]; then echo up;    fi;
        if [[ $key = [B ]]; then echo down;  fi;
      fi
    }
    toggle_option() {
      local arr_name=$1
      eval "local arr=(\"\${${arr_name}[@]}\")"
      local option=$2
      if [[ ${arr[option]} == true ]]; then
        arr[option]=
      else
        arr[option]=true
      fi
      eval $arr_name='("${arr[@]}")'
    }
    local retval=$1
    local options
    local defaults
    IFS=';' read -r -a options <<< "$2"
    if [[ -z $3 ]]; then
      defaults=()
    else
      IFS=';' read -r -a defaults <<< "$3"
    fi
    local selected=()
    for ((i=0; i<${#options[@]}; i++)); do
      selected+=("${defaults[i]:-false}")
      printf "\n"
    done
    # Determine current screen position for overwriting the options
    local lastrow=`get_cursor_row`
    local startrow=$(($lastrow - ${#options[@]}))
    # Ensure that cursor and input echoing are re-enabled when CTRL+C is issued during 'read -s'
    trap "cursor_blink_on; stty echo; printf '\n'; exit" 2
    cursor_blink_off
    local active=0
    while true; do
        # Print options by overwriting the last lines
        local idx=0
        for option in "${options[@]}"; do
            local prefix="[ ]"
            if [[ ${selected[idx]} == true ]]; then
              prefix="[x]"
            fi

            cursor_to $(($startrow + $idx))
            if [ $idx -eq $active ]; then
                print_active "$option" "$prefix"
            else
                print_inactive "$option" "$prefix"
            fi
            ((idx++))
        done
        # User key control
        case `key_input` in
            space)  toggle_option selected $active;;
            enter)  break;;
            up)     ((active--));
                    if [ $active -lt 0 ]; then active=$((${#options[@]} - 1)); fi;;
            down)   ((active++));
                    if [ $active -ge ${#options[@]} ]; then active=0; fi;;
        esac
    done
    # Set cursor position back to normal
    cursor_to $lastrow
    printf "\n"
    cursor_blink_on
    eval $retval='("${selected[@]}")'
}

## MAIN
ARG=${1}
case "${ARG}" in
  "--install" | "-i" )
    export OCI_AGENT
   clear -x
    echo ""
    echo "Performing feature installation process." | tee -a ${LOGFILE}
    echo "  Submitting user ID: ${USER}" | tee -a ${LOGFILE}
    echo "  Process start time: ${LOGTSTAMP}" | tee -a ${LOGFILE}
    echo "  Feature release:    __PRODVER__" | tee -a ${LOGFILE}
    echo "  Build ID:           __BUILDID__" | tee -a ${LOGFILE}
    echo "  Build date:         __BUILDDATE__" | tee -a ${LOGFILE}
    echo "" | tee -a ${LOGFILE}
    echo "Log output will be written to ${LOGFILE}."
    if [ "${2}" = "install.rsp" ]
    then
      if [ -f ${SCRIPTDIR}/install.rsp ]
      then
        . ${SCRIPTDIR}/install.rsp
      else
        echo "" | tee -a ${LOGFILE}
        echo "Response file does not exist. Continuing in interactive mode." | tee -a ${LOGFILE}
        echo "" | tee -a ${LOGFILE}
      fi
    fi
    checkPodmanAccess
    askFeatures
    if [ "${FEATURES}x" != "x" ]
    then
      checkPrereqsInstall
      checkPrereqsRuntime
      podmanUnpackCore
      writeInventory
      for FEATURE in ${FEATURES}
      do
        echo "Unpacking Feature '${FEATURE}'..." | tee -a ${LOGFILE}
        . ${SCRIPTDIR}/../${FEATURE}/utils/podmanDeploy-${FEATURE}.sh unpack
      done
      podmanInstall ${DCPATH}
      for FEATURE in ${FEATURES}
      do
        clear -x
        echo "Installing Feature '${FEATURE}'..." | tee -a ${LOGFILE}
        . ${SCRIPTDIR}/../${FEATURE}/utils/podmanDeploy-${FEATURE}.sh install
      done
      rm -f ${DCPATH}/zoa_env.config ${DCPATH}/.zoa_factory.config
      mv ${DCPATH}/zoa_env.config.all ${DCPATH}/zoa_env.config
      mv ${DCPATH}/.zoa_factory.config.all ${DCPATH}/.zoa_factory.config
      # If Keycloak volume already exists, we may need to  migrate its format.
      # Also, we can't use a bootstrap user ID.
      ${OCI_AGENT} volume ls | grep -q ${COMPOSE_PROJECT_NAME}_zaiops_keycloak
      KCVOLEXISTS=$?
      if [ ${KCVOLEXISTS} -eq 0 ]
      then
        migrateKcDb
        sed -i -e "s%^ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=.*$%ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=false%g" ${DCPATH}/.zoa_factory.config
        export ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=false
      fi

      # Generate TLS artifacts
      ${DCPATH}/bin/podmanManageZoa.sh config-certificates deploy-generate ${THISIP} ${THISHOST_LOWER} 2>&1 | tee -a ${LOGFILE}
      chmod 444 ${DCPATH}/zoa_env.config ${DCPATH}/.zoa_factory.config
      ${DCPATH}/bin/utils/keycloak/migrateKC.sh -i 2>&1 | tee -a ${LOGFILE}

      # set realms/clients/ids
      not_just_zdap=$( echo "$FEATURES" | sed "s/zdap//g; s/zoacommon//g" | tr -d '[:space:]' )
      ${DCPATH}/bin/utils/keycloak/updateKC.sh ${not_just_zdap} 2>&1 | tee -a ${LOGFILE}
      # Need to re-read .zoa_factory.config as updateKC may modify it
      . ${DCPATH}/.zoa_factory.config
      # Check whether 'zoacommon' is one of the selected features
      echo ${FEATURES} | grep -q zoacommon
      if [ $? -eq 0 ]
      then
        echo "Configuring Z Operational Analytics common services..." | tee -a ${LOGFILE}
        . ${SCRIPTDIR}/../zoacommon/utils/podmanDeploy-zoacommon.sh config
        if [ "${not_just_zdap}x" != "x" ]
        then
          echo "Configuring piserver..." | tee -a ${LOGFILE}
          . ${SCRIPTDIR}/../zoacommon/utils/podmanDeploy-zoacommon.sh piserver
        fi
      fi
      for FEATURE in ${FEATURES}
      do
        if [ "${FEATURE}" == "zoacommon" ]
        then
          :
        else
          clear -x
          echo "Configuring Feature '${FEATURE}'..." | tee -a ${LOGFILE}
          . ${SCRIPTDIR}/../${FEATURE}/utils/podmanDeploy-${FEATURE}.sh config
        fi
      done
      # Message for stdout
      echo "You can now start the installed features by running the following commands:"
      # Message for log
      echo "Command to start the installed features:" >> ${LOGFILE}
      echo "    cd ${DCPATH}" | tee -a ${LOGFILE}
      echo "    ./bin/podmanManageZoa.sh up" | tee -a ${LOGFILE}
      echo "" | tee -a ${LOGFILE}
      echo "A copy of this script (${SCRIPTNAME}) can be found in ${DCPATH}/bin for future use." | tee -a ${LOGFILE}
      echo "" | tee -a ${LOGFILE}
    else
      echo "" | tee -a ${LOGFILE}
      echo "No feature was selected for installation." | tee -a ${LOGFILE}
      echo "Exiting without action." | tee -a ${LOGFILE}
      echo "" | tee -a ${LOGFILE}
      exit 0
    fi
    echo "Log output was written to ${LOGFILE}."
    cp ${LOGFILE} ${DCPATH}/logs/install
    ;;
  "--remove" | "-r" )
    clear -x
    export OCI_AGENT=${OCI_AGENT}
    echo ""
    echo "Performing feature removal process." | tee -a ${LOGFILE}
    SCRIPTDIR_SHORT=$( basename ${SCRIPTDIR} )
    SCRIPTDIR_PARENT=$( dirname ${SCRIPTDIR} )
    if [ "${SCRIPTDIR_SHORT}" != "bin" ]
    then
      logToFileAndStdout "\n${ERRORMSG}${SCRIPTDIR_PARENT} is not a valid Z AIOps runtime directory.\n${SPACEMSG}${SCRIPTNAME} must be run from a valid runtime directory. Unable to proceed.\n"
      exit 1
    fi
    echo "  Submitting user ID: ${USER}" | tee -a ${LOGFILE}
    echo "  Process start time: ${LOGTSTAMP}" | tee -a ${LOGFILE}
    echo "  Feature release:    __PRODVER__" | tee -a ${LOGFILE}
    echo "  Build ID:           __BUILDID__" | tee -a ${LOGFILE}
    echo "  Build date:         __BUILDDATE__" | tee -a ${LOGFILE}
    echo "" | tee -a ${LOGFILE}
    echo "Log output will be written to ${LOGFILE}."
    if [ "${2}" == "remove.rsp" ]
    then
      if [ -f ${SCRIPTDIR}/remove.rsp ]
      then
        . ${SCRIPTDIR}/remove.rsp
      else
        echo "" | tee -a ${LOGFILE}
        echo "Response file does not exist. Continuing in interactive mode." | tee -a ${LOGFILE}
        echo "" | tee -a ${LOGFILE}
      fi
    fi
    set -a
    . ${SCRIPTDIR}/../zoa_env.config
    . ${SCRIPTDIR}/../.zoa_factory.config
    if [ -f ${SCRIPTDIR}/../product.inventory ]
    then
      . ${SCRIPTDIR}/../product.inventory
    fi
    set +a
    checkPrereqsRemove
    checkPodmanAccess
    podmanRemove
    for FEATURE in ${INSTALLED_FEATURES}
    do
      clear -x
      echo "Removing Feature '${FEATURE}'..." | tee -a ${LOGFILE}
      if [ -f ${SCRIPTDIR}/utils/podmanDeploy-${FEATURE}.sh ]
      then
        . ${SCRIPTDIR}/utils/podmanDeploy-${FEATURE}.sh remove
      else
        echo "Deployment management script for Feature '${FEATURE}' is not installed." | tee -a ${LOGFILE}
      fi
    done
    mkdir -p ${DCPATH}/logs/install
    cp ${LOGFILE} ${DCPATH}/logs/install
    echo "Log output was written to ${LOGFILE}."
    ;;
  "--clean" | "-c" )
    clear -x
    export OCI_AGENT=${OCI_AGENT}
    echo ""
    echo "Performing feature cleanup process." | tee -a ${LOGFILE}
    SCRIPTDIR_SHORT=$( basename ${SCRIPTDIR} )
    SCRIPTDIR_PARENT=$( dirname ${SCRIPTDIR} )
    if [ "${SCRIPTDIR_SHORT}" != "bin" ]
    then
      logToFileAndStdout "\n${ERRORMSG}${SCRIPTDIR_PARENT} is not a valid Z AIOps runtime directory.\n${SPACEMSG}${SCRIPTNAME} must be run from a valid runtime directory. Unable to proceed.\n"
      exit 1
    fi
    echo "  Submitting user ID: ${USER}" | tee -a ${LOGFILE}
    echo "  Process start time: ${LOGTSTAMP}" | tee -a ${LOGFILE}
    echo "  Feature release:    __PRODVER__" | tee -a ${LOGFILE}
    echo "  Build ID:           __BUILDID__" | tee -a ${LOGFILE}
    echo "  Build date:         __BUILDDATE__" | tee -a ${LOGFILE}
    echo "" | tee -a ${LOGFILE}
    echo "Log output will be written to ${LOGFILE}."
    if [ "${2}" == "remove.rsp" ]
    then
      if [ -f ${SCRIPTDIR}/remove.rsp ]
      then
        . ${SCRIPTDIR}/remove.rsp
      else
        echo "" | tee -a ${LOGFILE}
        echo "Response file does not exist. Continuing in interactive mode." | tee -a ${LOGFILE}
        echo "" | tee -a ${LOGFILE}
      fi
    fi
    set -a
    . ${SCRIPTDIR}/../zoa_env.config
    . ${SCRIPTDIR}/../.zoa_factory.config
    if [ -f ${SCRIPTDIR}/../product.inventory ]
    then
      . ${SCRIPTDIR}/../product.inventory
    fi
    set +a
    checkPrereqsRemove
    checkPodmanAccess
    podmanPurge
    ;;
  * )
    usage
    ;;
esac
