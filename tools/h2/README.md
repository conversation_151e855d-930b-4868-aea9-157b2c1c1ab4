# Keycloak database migration utility (for H2 dev-file database)

## Package content
The file `kcdb_mig.tar.gz` contains the following artifacts:
* `kcdbmig/migrate-db.sh`:
  * A script that is used during container deployment to test whether the H2 needs to
    be updated, and to perform that update ie required.
* `kcdbmig/lib/com.h2database.h2-2.1.214.jar`:
  * The H2 library at the release level that shipped with Keycloak v21.1.2 (prior to ZOACOMMON Fix Pack 20)
* `kcdbmig/lib/com.h2database.h2-2.3.230.jar`:
  * The H2 library at the release level that ships with Keycloak v26.1.2 (ZOACOMMON Fix Pack 20)

These artifacts are stored as a gzipped TAR file to "hide" the older H2 library from Mend scans.
This older H2 library -- which has known vulnerabilities -- is required because of how the H2 database migration process works:
* First, export existing database into a SQL script _using the H2 level that created the database_.
* Then, create a new database from the SQL script in the target format _using the desired new H2 level_.

In other words, H2 database migration without the "original" H2 library is not possible.
And while that old library will -- in most cases -- still be available in the customer's environment in the form of the prior
zoa-auth container image, we cannot prevent customers from deleting that container image before database migration will take
place. Therefore, we need to ship the "original" H2 library.

## Package updates
Based on the documentation I was able to find, this utility should work for any H2 database migration from a "v2" format
(H2 v2.1.x / v2.2.x) to a "v3" format (H2 v2.3.x).

Should we ever ship a Keycloak version that uplifts H2 beyond the "v3" database format, then this utility will have to be updated
accordingly. The updates will have to consider the following:
* What is the earliest H2 database format version we are willing to support in an "under the covers" upgrade process?
* What is the latest H2 database format version we need to upgrade to "under the covers"?
* How many H2 database format versions in total will have to be handled?

Those considerations will dictate not only which of the H2 libraries need to be replaced, but also whether the script logic requires
changes.

## Future considerations
Long-term, it might be advisable to switch from the H2 dev-util database -- which is considered "unworthy for production" by the
Keycloak team -- to an external RDBMS such as PostgreSQL. Starting with ZOACOMMON Fix Pack 20, the type of database used for 
Keycloak is externally configurable, although no effort will be undertaken to test that configurability, nor is it something
we will document for our customers.
