// Using Scripted Pipeline for maximum flexibility

// This is a custom data structure we'll use to define our parallel builds:
List<StageDef> stageDefs = [
        new StageDef("s390x"),
        new StageDef("x86_64")]

// The 'branches' structure is a map from branch name to branch code. This is the
// argument we'll give to the 'parallel' build step later:
def branches = [:]
def s390xCommonTarImg = "zoacommon-s390x"
def s390xCoreTarImg = "zoacore-s390x"
def x86CommonTarImg = "zoacommon-x86_64"
def x86CoreTarImg = "zoacore-x86_64"

properties ([
  [ $class: 'BuildDiscarderProperty', strategy: 
    [ $class: 'LogRotator', 
        artifactDaysToKeepStr: '10', 
        artifactNumToKeepStr: '10',
        daysToKeepStr: '10',
        numToKeepStr: '10'
    ]
  ] 
])

// This build needs to run every time since the constituent pieces are spread over
//   many repos
// Loop through the stage definitions and define the parallel stages:
for (stageDef in stageDefs) {

    // Never inline this!
    String interp = stageDef.interp

    String gitOrg = "IZOA"
    String gitRepo = "zoa-common-docker-MF"
    String branchName = "Build ZOA common Docker images for ${interp}"
    String labelName = "docker-${interp}-backup"
    String gitBranch = "${env.BRANCH_NAME}"
    String fallbackBranch = "develop"
    String buildId = "${env.BUILD_NUMBER}"
    String shortBranch = gitBranch.replaceAll(/[^a-zA-Z0-9]/, '').toLowerCase()
    String version = "test-" + shortBranch.substring(0, (shortBranch.length() > 10) ? 10 : shortBranch.length())
    String compactVersion = "v511-dev"
    String vrmf = "5.1.1-dev"
    String forkBranch = gitBranch
    if (gitBranch.equals("develop")) {
      version = "latest"
      labelName = "docker-${interp}"
    }
    if (gitBranch ==~ /.*_release$/) {
        version = gitBranch.substring(0,4).replaceAll(/([0-9])([0-9])([0-9])([0-9])/, '$1.$2.$3-$4')
        compactVersion = gitBranch.substring(0,4).replaceAll(/([0-9])([0-9])([0-9])([0-9])/, 'v$1$2$3-0$4')
        labelName = "docker-${interp}"
    }
    if (gitBranch.equals("service_v5.1.0")) {
        version = "5.1.1-22"
        compactVersion = "v511-22"
        vrmf = "5.1.1-22"
        fallbackBranch = gitBranch
        labelName = "docker-${interp}"
    }
    def nowDate = new Date()
    final buildDate = nowDate.format('yyyyMMdd-HHmm')
    print("Label name is ${labelName}.")
    print("Git branch is ${gitBranch}.")
    print("Version is ${version}.")
    print("Compact version is ${compactVersion}.")
    print("Jenkins build ID is ${buildId}.")
    print("Build date is ${buildDate}.")

    branches[branchName] = {

        // Start the branch with a node definition. We explicitly exclude the
        // master node, so only the two slaves will do builds:
        node(labelName) {
            def workspace = env.WORKSPACE
            print("Current workspace is ${workspace}.")
            String targetDir = 'GITWORK/' + gitRepo
            sh(script: 'rm -Rf GITWORK && mkdir -p ' + targetDir, returnStdout: true)
            sh(script: 'cd GITWORK && <NAME_EMAIL>:' + gitOrg + '/' + gitRepo + '.git -b ' + gitBranch, returnStdout: true)
            def buildProps = readProperties file: workspace + '/GITWORK/' + gitRepo + '/.buildenv'
            withEnv(["GITBRANCH=" + gitBranch,
                    "FALLBACK_BRANCH=" + fallbackBranch,
                    "ARCH=" + interp,
                    "VER=" + version,
                    "VRMF=" + vrmf,
                    "COMPACT_VER=" + compactVersion,
                    "KAFKAVER=" + buildProps['KAFKA_VERSION'],
                    "CONNECT_SINK_VER=" + buildProps['CONNECT_SINK_VER'],
                    "KCVER=" + buildProps['KCVER'],
                    "OSVER=" + buildProps['OSTAG'],
                    "NETTYVER=" + buildProps['NETTYVER'],
                    "QUARKUSVER=" + buildProps['QUARKUSVER'],
                    "WILDFLYVER=" + buildProps['WILDFLYVER'],
                    "ZRDDSVER=" + buildProps['ZRDDS_VERSION'],
                    "PGKCVER=" + buildProps['PGKCTAG'],
                    "FORKBRANCH=" + forkBranch,
                    "BUILD_DATE=" + buildDate,
                    "BUILD_ID=" + buildId,
                    "S390X_CORE_TAR=" + s390xCoreTarImg,
                    "S390X_COMMON_TAR=" + s390xCommonTarImg,
                    "X86_CORE_TAR=" + x86CoreTarImg,
                    "X86_COMMON_TAR=" + x86CommonTarImg
            ]) {
                stage("Clone required GitHub repos on ${interp}") {
                    try {
                        // Clone all required GitHub projects
                        sh '''
                            rm -Rf DIST && mkdir -p DIST/core/tools DIST/common
                            REPFILE=GIT_REPORT.txt
                            cd GITWORK
                            echo "************************************************************************************************" >> ${REPFILE}
                            echo "GitHub Report for Build ${BUILD_ID} on ${BUILD_DATE}" >> ${REPFILE}
                            echo "************************************************************************************************" >> ${REPFILE}
                            printf "%-30s %-1s %-20s %-1s %-40s\n" "REPO" "|" "BRANCH" "|" "COMMIT HASH" >> ${REPFILE}
                            echo "************************************************************************************************" >> ${REPFILE}
                            for REPO in zoa-common-docker-MF zoa-spring-cloud-gateway-MF izoa-pi-discovery-server-MF izoa-pi-keycloak-MF izoa-pi-keycloak-encryption-MF izoa-keycloak-2fa-email-authenticator keycloak-racf-spi keycloak-filter-provider-users
                            do
                              RESULT=$( git ls-remote --heads ******************:IZOA/${REPO} ${GITBRANCH} | awk '{ print $2 }' )
                              if [ -z ${RESULT} ]
                              then
                                PULLBRANCH=${FALLBACK_BRANCH}
                              else
                                PULLBRANCH=${GITBRANCH}
                              fi
                              REPO_U=`echo ${REPO} | tr "-" "_" | tr [[:upper:]] [[:lower:]]`
                              echo "export ${REPO_U}_repo=${REPO}" >> git.props
                              echo "export ${REPO_U}_branch=${PULLBRANCH}" >> git.props
                              if [ "${REPO}" != "zoa-common-docker-MF" ]
                              then
                                <NAME_EMAIL>:IZOA/${REPO}.git -b ${PULLBRANCH}
                              fi
                              cd ${REPO}
                              COMMIT_HASH=`git rev-parse --verify ${PULLBRANCH}`
                              cd -
                              echo "export ${REPO_U}_commit=${COMMIT_HASH}" >> git.props
                              printf "%-30s %-1s %-20s %-1s %-40s\n" ${REPO} "|" ${PULLBRANCH} "|" ${COMMIT_HASH} >> ${REPFILE}
                            done
                            echo "************************************************************************************************" >> ${REPFILE}
                            find -name "*.sh" | xargs chmod 755
                            if [ "${ARCH}" = "s390x" ]
                            then
                              mkdir -p zoa-common-docker-MF/reports && mv -f ${REPFILE} zoa-common-docker-MF/reports/
                              cd zoa-common-docker-MF
                              git config user.email "<EMAIL>"
                              git config user.name "IZOA Build"
                              git add reports/${REPFILE}
                              git commit -m "Build ID: ${BUILD_ID}; date: ${BUILD_DATE}"
                              git fetch && git pull --commit --no-edit
                              git push
                            fi
                        '''
                        // For Linux on Z, update architecture suffixes for Docker image names
                        sh '''
                            if [ "${ARCH}" = "s390x" ]
                            then
                                echo "Building for s390x; need to update architecture suffixes for Docker image naemes."
                                cd GITWORK
                                for DFILE in `find -name Dockerfile*`
                                do
                                    sed -i -e "s%-x86_64$%-s390x%g" ${DFILE}
                                    sed -i -e "s%-x86_64\\ AS%-s390x\\ AS%g" ${DFILE}
                                    sed -i -e "s%/x86_64/%/s390x/%g" ${DFILE}
                                done
                                cd zoa-common-docker-MF
                                for DCFILE in $( find -name "*docker-compose*yml" )
                                do
                                    sed -i -e "s%-x86_64$%-s390x%g" ${DCFILE}
                                done
                            else
                                echo "Building for x86_64; no Dockerfile customization required."
                            fi
                        '''
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Obtain PI server package on ${interp}") {
                    try {
                        withCredentials([usernamePassword(credentialsId: 'IZOA_Functional_Artifactory',
                                passwordVariable: 'AFPWD',
                                usernameVariable: 'AFUSER')]) {
                            sh '''
                                cd GITWORK
                                RESULT=$( git ls-remote --heads ******************:IZOA/piframework-MF ${GITBRANCH} | awk '{ print $2 }' )
                                if [ -z ${RESULT} ]
                                then
                                  PULLBRANCH=${FALLBACK_BRANCH}
                                else
                                  PULLBRANCH=${GITBRANCH}
                                fi
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/zoacommon/components/${COMPACT_VER}/${PULLBRANCH}/piserver.zip
                                mkdir -p PI_TMP/piserver
                                unzip piserver.zip -d PI_TMP/piserver
                                rm -f piframework_latests.txt piserver.zip
                                cd PI_TMP
                                mkdir -p piserver/wlp/usr/servers/piFrameworkServer/derby
                                sed -i -e "s%traceFileName=.*$%traceFileName=\\"stdout\\"%g" piserver/wlp/usr/servers/piFrameworkServer/server.xml
                                cp piserver/wlp/usr/servers/piFrameworkServer/server.xml piserver/wlp/usr/servers/piFrameworkServer/server.xml_ssl
                                mv piserver/wlp/usr/servers/piFrameworkServer/server.xml piserver/wlp/usr/servers/piFrameworkServer/server.xml_nonssl
                                sed -i -e "s%httpsPort=.*$%httpsPort=\\"-1\\"%g" piserver/wlp/usr/servers/piFrameworkServer/server.xml_nonssl
                                sed -i -e "s%httpPort=.*$%httpPort=\\"\\${default.https.port}\\"%g" piserver/wlp/usr/servers/piFrameworkServer/server.xml_nonssl
                                sed -i -e "s%<ssl\\ id=\\"defaultSSLConfig\\"%<\\!--\\ <ssl\\ id=\\"defaultSSLConfig\\"%g" piserver/wlp/usr/servers/piFrameworkServer/server.xml_nonssl
                                sed -i -e "s%enabledCiphers=\\"\\${ciphers}\\"\\/>%enabledCiphers=\\"\\${ciphers}\\"\\/> -->%g" piserver/wlp/usr/servers/piFrameworkServer/server.xml_nonssl
                                echo "-Xdump:java:none" >> piserver/wlp/usr/servers/piFrameworkServer/jvm.options
                                echo "-Xdump:java:events=gpf+abort+traceassert+corruptcache" >> piserver/wlp/usr/servers/piFrameworkServer/jvm.options
                                tar cvzf ../piserver.tar.gz piserver
                                cd ..
                                rm -Rf PI_TMP
                            '''
                        }
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Obtain OpenSearch plugins on ${interp}") {
                    try {
                        withCredentials([usernamePassword(credentialsId: 'IZOA_Functional_Artifactory',
                                passwordVariable: 'AFPWD',
                                usernameVariable: 'AFUSER')]) {
                            sh '''
                                mkdir -p GITWORK/zoa-common-docker-MF/opensearch/${OSVER}
                                cd GITWORK/zoa-common-docker-MF/opensearch/${OSVER}
                                REPO=zoa-os_plugin-customizations
                                RESULT=$( git ls-remote --heads ******************:IZOA/${REPO} ${GITBRANCH} | awk '{ print $2 }' )
                                if [ -z ${RESULT} ]
                                then
                                  PULLBRANCH=${FALLBACK_BRANCH}
                                else
                                  PULLBRANCH=${GITBRANCH}
                                fi
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/built/${PULLBRANCH}/common-utils-${OSVER}.0.jar
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/built/${PULLBRANCH}/opensearch-job-scheduler-${OSVER}.0.zip
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/built/${PULLBRANCH}/opensearch-index-management-${OSVER}.0.zip
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/built/${PULLBRANCH}/opensearch-reports-scheduler-${OSVER}.0.zip
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/built/${PULLBRANCH}/opensearch-alerting-${OSVER}.0.zip
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/built/${PULLBRANCH}/opensearch-notifications-${OSVER}.0.zip
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/built/${PULLBRANCH}/opensearch-notifications-core-${OSVER}.0.zip
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/built/${PULLBRANCH}/opensearch-security-${OSVER}.0.zip
                            '''
                        }
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Obtain pre-built distributions on ${interp}") {
                    try {
                        withCredentials([usernamePassword(credentialsId: 'IZOA_Functional_Artifactory',
                                passwordVariable: 'AFPWD',
                                usernameVariable: 'AFUSER')]) {
                            sh '''
                                ROOT=${PWD}
                                cd GITWORK/zoa-common-docker-MF
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/kafka_out.tar
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/netty_jars.tar
                                cd ${ROOT}/GITWORK/izoa-pi-keycloak-MF
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/keycloak_out.tar
                            '''
                        }
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Build datastore migration tool on ${interp}") {
                    try {
                        sh '''
                           WORKROOT=`pwd`
                           if [ "${ARCH}" = "x86_64" ]
                           then
                             export UBU_ARCH="amd64"
                           else
                             export UBU_ARCH=${ARCH}
                           fi
                           cd GITWORK/zoa-common-docker-MF/zaa-datastore-migration
                           echo "Building ZAA data migration tool..."
                           export DOCKER_BUILDKIT_OLD=${DOCKER_BUILDKIT} DOCKER_BUILDKIT=1
                           docker build --build-arg BUILDER=${BUILDER} --build-arg VRMF=${VRMF} --output type=tar,dest=zaa-datastore-migration.tar --file Dockerfile ..
                           echo ""
                           mkdir -p ${WORKROOT}/DIST/common/utils/lib
                           tar xvf zaa-datastore-migration.tar -C ${WORKROOT}/DIST/common/utils/lib/
                           cp utils/*.sh ${WORKROOT}/DIST/common/utils/
                           cd ${WORKROOT}/DIST/common/utils
                           chmod 755 *.sh
                           tar --numeric-owner --owner=0 --group=0 -cvzf zaa-datastore-migration.tar.gz run.sh lib/
                           cat unpack.sh zaa-datastore-migration.tar.gz > ../zaa-datastore-migration.run
                           chmod 755 ../zaa-datastore-migration.run
                           cd .. && rm -Rf utils
                        '''
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Build kafka-connect sink on ${interp}") {
                    try {
                        sh '''
                            WORKROOT=`pwd`
                            if [ "${ARCH}" = "x86_64" ]
                            then
                                export UBU_ARCH="amd64"
                            else
                                export UBU_ARCH=${ARCH}
                            fi
                            cd GITWORK/zoa-common-docker-MF/devsrc/ibm-kafka-connect-elastic-sink-master
                            JAVA_HOME=/usr/lib/jvm/java-17-openjdk-${UBU_ARCH}
                            export PATH=${HOME}/tools/apache-maven-3.6.3/bin:${JAVA_HOME}/bin:${PATH}
                            mvn clean package
                            cd ${WORKROOT}
                            mv GITWORK/zoa-common-docker-MF/devsrc/ibm-kafka-connect-elastic-sink-master/target/ibm-kafka-connect-elastic-sink-${CONNECT_SINK_VER}-jar-with-dependencies.jar DIST/ibm-kafka-connect-elastic-sink-${CONNECT_SINK_VER}.jar
                        '''
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Create Docker images on ${interp}") {
                    try {
                        withCredentials([usernamePassword(credentialsId: 'IZOA_Functional_Artifactory',
                                passwordVariable: 'AFPWD',
                                usernameVariable: 'AFUSER'),
                            usernamePassword(credentialsId: 'IZOA_Build_GHtoken',
                                passwordVariable: 'GHTOKEN',
                                usernameVariable: 'GHUSER'),
                            string(credentialsId: 'zldazaadevfunc',
                                variable: 'IBMCLOUD_API_KEY')]) {
                            // Log into IBM Cloud
                            sh '''
                                ibmcloud login -r us-east
                                ibmcloud cr login --client docker
                            '''
                            // Clean up any containers left behind from prior work
                            sh '''
                                docker rm -vf $(docker ps -a -q) || docker ps -a
                            '''
                            // Clean up any images left behind from prior work
                            sh '''
                                docker rmi -f $(docker images -a -q) || docker images -a
                                docker rmi -f $(docker images -a -q) || docker images -a
                            '''
                            // Navigate into master project and launch docker compose build process
                            sh '''
                                set +x
                                set -a
                                . ${HOME}/.af
                                set +a
                                export PATH=/usr/local/bin:/usr/local/sbin:${PATH}
                                set -x
                                cd GITWORK
                                THISDIR=`pwd`
                                . ./git.props
                                export AFUSER AFPWD
                                export TAG=${VER}
                                export OSTAG=${OSVER}
                                export ZRDDS_VERSION=${ZRDDSVER}
                                export KAFKA_VERSION=${KAFKAVER}
                                export PGKCTAG=${PGKCVER}
                                export NETTYVER
                                export VRMF
                                export FORKBRANCH FALLBACK_BRANCH
                                export BUILDER=${GHUSER}
                                export TOKEN=${GHTOKEN}
                                if [ "${ARCH}" = "x86_64" ]
                                then
                                  export UBU_ARCH="amd64"
                                  export OPENJDK_ARCH="x64"
                                else
                                  export UBU_ARCH=${ARCH}
                                  export OPENJDK_ARCH=${ARCH}
                                fi
                                cd zoa-common-docker-MF
                                echo "Creating product images..."
                                sed -i -e "s%^TAG=.*$%TAG=${VER}%g" .zoa_factory.config.core
                                sed -i -e "s%^ZOACOMMON_TAG=.*$%ZOACOMMON_TAG=${VER}%g" .zoa_factory.config.common
                                sed -i -e "s%^OSTAG=.*$%OSTAG=${OSVER}%g" .zoa_factory.config.common
                                cat .zoa_factory.config.co* > .zoa_factory.config
                                cat zoa_env.config.co* > zoa_env.config
                                set -a
                                . ./.zoa_factory.config
                                set +a
                                docker compose --env-file zoa_env.config -f docker-compose-dev.yml build --no-cache
                                if [ "${ARCH}" = "x86_64" ]
                                then
                                  mkdir -p TMP/lib && chmod 777 TMP/lib
                                  mkdir -p TMP/logs
                                  for SERVICE in zoa-gateway zoa-service-discovery
                                  do
                                    docker run --name tempcont -u root  --entrypoint /bin/bash -v ${THISDIR}/zoa-common-docker-MF/TMP/lib:/tmp/lib icr.io/zoa-oci/${SERVICE}:${TAG}-x86_64 -c "cp /*-app/*.*ar /tmp/lib/ && chmod 666 /tmp/lib/*.*"
                                    docker rm tempcont
                                  done
                                  echo "{
    \\"vrmf\\": \\"${VRMF}\\",
    \\"apar\\": \\"None\\",
    \\"gateway\\": {
        \\"repo\\": \\"${zoa_spring_cloud_gateway_mf_repo}\\",
        \\"commit\\": \\"${zoa_spring_cloud_gateway_mf_commit}\\"
    },
    \\"keycloak\\": {
        \\"repo\\": \\"${izoa_pi_keycloak_mf_repo}\\",
        \\"commit\\": \\"${izoa_pi_keycloak_mf_commit}\\"
    },
    \\"datastore\\": {
        \\"repo\\": \\"${zoa_common_docker_mf_repo}\\",
        \\"commit\\": \\"${zoa_common_docker_mf_commit}\\"
    }
}" > TMP/logs/common-svc-package.json
                                  cd TMP
                                  zip -rm ../../../DIST/zoacommon_libs.zip lib/ logs/
                                  cd -
                                fi
                                rm zoa_env.config .zoa_factory.config
                                echo ""
                            '''
                        }
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Create Docker transportable images on ${interp}") {
                    try {
                        // Run 'docker save' against the generated images
                        sh '''
                            ROOT=${PWD}
                            cd GITWORK/zoa-common-docker-MF
                            IMGSTRING=""
                            for IMG in $(docker images -f 'label=feature=IBM Z AIOps - Common Services - Core' -q)
                            do
                              IMGINFO=`docker inspect --format=\'{{.RepoTags}}\' ${IMG} | tr -d \'[]\'`
                              INAME=`echo ${IMGINFO} | cut -f 1 -d ":" | cut -f 3 -d "/"`
                              ITAG=`echo ${IMGINFO} | cut -f 2 -d ":"`
                              IMGSTRING="${IMGSTRING} icr.io/zoa-oci/${INAME}:${ITAG}"
                            done
                            docker save -o ${ROOT}/DIST/core/zoacore_images.tar ${IMGSTRING}
                            IMGSTRING=""
                            for IMG in $(docker images -f 'label=feature=IBM Z AIOps - Common Services - Z Operational Analytics' -q)
                            do
                              IMGINFO=`docker inspect --format=\'{{.RepoTags}}\' ${IMG} | tr -d \'[]\'`
                              INAME=`echo ${IMGINFO} | cut -f 1 -d ":" | cut -f 3 -d "/"`
                              ITAG=`echo ${IMGINFO} | cut -f 2 -d ":"`
                              IMGSTRING="${IMGSTRING} icr.io/zoa-oci/${INAME}:${ITAG}"
                            done
                            docker save -o ${ROOT}/DIST/common/zoacommon_images.tar ${IMGSTRING}
                        '''
                        // Package the individual TAR files, plus supporting scripts, into a gzipped TAR file
                        sh '''
                            ROOT=${PWD}
                            cd GITWORK/zoa-common-docker-MF
                            cp zoa_env.config.core ${ROOT}/DIST/core/zoa_env.config
                            cp zoa_env.config.common ${ROOT}/DIST/common/zoa_env.config
                            cp .zoa_factory.config.core ${ROOT}/DIST/core/.zoa_factory.config
                            cp .zoa_factory.config.common ${ROOT}/DIST/common/.zoa_factory.config
                            cp -R k8s-config/common ${ROOT}/DIST/common/k8s-config
                            mkdir -p ${ROOT}/DIST/common/k8s-pre-install
                            mv ${ROOT}/DIST/common/k8s-config/pv* ${ROOT}/DIST/common/k8s-pre-install/
                            mv ${ROOT}/DIST/common/k8s-config/StorageClass ${ROOT}/DIST/common/k8s-pre-install/
                            cp zoacore-docker-compose.yml ${ROOT}/DIST/core/
                            cp zoacommon-docker-compose.yml ${ROOT}/DIST/common/
                            cp tools/.featurespec ${ROOT}/DIST/common/
                            cp tools/*.sh ${ROOT}/DIST/core/ && chmod 755 ${ROOT}/DIST/core/*.sh
                            cp tools/*.rsp ${ROOT}/DIST/core/
                            cp tools/h2/kcdb_mig.tar.gz ${ROOT}/DIST/core/tools
                            cp -R k8s-config/core ${ROOT}/DIST/core/k8s-config
                            mkdir -p ${ROOT}/DIST/core/k8s-pre-install
                            mv ${ROOT}/DIST/core/k8s-config/pv* ${ROOT}/DIST/core/k8s-pre-install/
                            mv ${ROOT}/DIST/core/k8s-config/StorageClass ${ROOT}/DIST/core/k8s-pre-install/
                            sed -i -e "s%__PRODVER__%${COMPACT_VER}%g" ${ROOT}/DIST/core/*.sh
                            sed -i -e "s%__BUILDID__%${BUILD_ID}%g" ${ROOT}/DIST/core/*.sh
                            sed -i -e "s%__BUILDDATE__%${BUILD_DATE}%g" ${ROOT}/DIST/core/*.sh
                            sed -i -e "s%__IMGFILE__%ZOA-Common-\\${ARCH}.tar.gz%g" ${ROOT}/DIST/core/*.sh
                            mkdir -p ${ROOT}/DIST/core/samples/systemd
                            cp -R config/systemd/* ${ROOT}/DIST/core/samples/systemd/
                            mkdir -p ${ROOT}/DIST/core/bin/utils/kafka
                            mkdir -p ${ROOT}/DIST/core/bin/utils/keycloak
                            mkdir -p ${ROOT}/DIST/common/utils
                            mkdir -p ${ROOT}/DIST/common/bin/utils/piserver
                            mv ${ROOT}/DIST/core/*ManageZoa.sh ${ROOT}/DIST/core/bin/
                            mv ${ROOT}/DIST/core/*Manage-zoacommon.sh ${ROOT}/DIST/common/bin/utils/
                            mv ${ROOT}/DIST/core/kafkaPrune*.sh ${ROOT}/DIST/core/bin/utils/kafka/
                            mv ${ROOT}/DIST/core/zoa_create_perm_admin.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/zoa_kc_update.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/zoa_load_ids.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/kc_postUp.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/updateKC*.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/migrateKC.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/loadKCIds*.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/zoa_kc_updatepassword.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/checkKCreadiness.sh ${ROOT}/DIST/core/bin/utils/keycloak/
                            mv ${ROOT}/DIST/core/common_functions*.sh ${ROOT}/DIST/core/bin/utils/
                            mv ${ROOT}/DIST/core/*Deploy-*.sh ${ROOT}/DIST/common/utils/
                            mv ${ROOT}/DIST/core/customizeIPs.sh ${ROOT}/DIST/common/bin/utils/piserver/
                            cd kafka
                            tar cvzf ${ROOT}/DIST/core/migrate-to-kraft.tar.gz kafka_migration --numeric-owner --owner=0 --group=0
                            cd ${ROOT}/DIST/core
                            if [ "${ARCH}" = "x86_64" ]
                            then
                              find bin -name "*k8s*" | xargs -n 1 tar -uf ZOACORE-k8s-resources.tar --numeric-owner --owner=0 --group=0
                              for UTIL in zoa_create_perm_admin.sh zoa_kc_update.sh checkKCreadiness.sh kc_postUp.sh customizeIPs.sh zoa_load_ids.sh
                              do
                                find bin -name ${UTIL} | xargs -n 1 tar -uf ZOACORE-k8s-resources.tar --numeric-owner --owner=0 --group=0
                              done
                              tar uvf ZOACORE-k8s-resources.tar --numeric-owner --owner=0 --group=0 zoa_env.config .zoa_factory.config extensions/ samples/ tools/ k8s-config/ && rm -Rf k8s-config/
                              gzip ZOACORE-k8s-resources.tar
                              ln -sf k8sDeployZoa.sh ocDeployZoa.sh
                              tar cvf ../ZOACORE-k8s-install.tar --transform 'flags=rSH;s%^%zoacommon/%' k8sDeployZoa.sh ocDeployZoa.sh ZOACORE-k8s-resources.tar.gz *.rsp && rm -Rf k8sDeployZoa.sh ocDeployZoa.sh ZOACORE-k8s-resources.tar.gz 
                              tar uvf ../ZOACORE-k8s-install.tar k8s-pre-install/ && rm -Rf k8s-pre-install/
                              gzip ../ZOACORE-k8s-install.tar
                            fi
                            find bin utils -name "*k8s*" | xargs -n 1 rm -f
                            tar cvzf ZOA-Core-${ARCH}.tar.gz --numeric-owner --owner=0 --group=0 *.tar zoacore*.yml zoa_env.config .zoa_factory.config bin/ samples/ tools/ && rm -Rf *.tar zoacore*.yml zoa_env.config .zoa_factory.config bin/ samples/ tools/
                            if [ "${ARCH}" = "x86_64" ]
                            then
                              BIGTARCORE=${X86_CORE_TAR}.tar
                            elif [ "${ARCH}" = "s390x" ]
                            then
                              BIGTARCORE=${S390X_CORE_TAR}.tar
                            fi
                            tar cvf ../${BIGTARCORE} --numeric-owner --owner=0 --group=0 ZOA-Core*.tar.gz migrate-to-kraft.tar.gz *.sh *.rsp && rm -f ZOA-Core*.tar.gz migrate-to-kraft.tar.gz *.sh *.rsp
                            cd ${ROOT}/DIST/common
                            if [ "${ARCH}" = "x86_64" ]
                            then
                              find bin -name "*k8s*" | xargs -n 1 tar -uf ZOACOMMON-k8s-resources.tar --numeric-owner --owner=0 --group=0
                              tar uvf ZOACOMMON-k8s-resources.tar --numeric-owner --owner=0 --group=0 zoa_env.config .zoa_factory.config extensions/ samples/ k8s-config/ && rm -Rf k8s-config/
                              gzip ZOACOMMON-k8s-resources.tar
                              tar cvf ../ZOACOMMON-k8s-install.tar --transform 'flags=rSH;s%^%zoacommon/%' utils/k8sDeploy-zoacommon.sh ZOACOMMON-k8s-resources.tar.gz .featurespec && rm -Rf utils/k8sDeploy-zoacommon.sh ZOACOMMON-k8s-resources.tar.gz 
                              tar uvf ../ZOACOMMON-k8s-install.tar k8s-pre-install/ && rm -Rf k8s-pre-install/
                              gzip ../ZOACOMMON-k8s-install.tar
                            fi
                            find bin utils -name "*k8s*" | xargs -n 1 rm -f
                            tar cvzf ZOA-Common-${ARCH}.tar.gz --numeric-owner --owner=0 --group=0 *.tar zoacommon*.yml zoa_env.config .zoa_factory.config bin/ && rm -Rf *.tar zoacommon*.yml zoa_env.config .zoa_factory.config bin/
                            if [ "${ARCH}" = "x86_64" ]
                            then
                              BIGTARCOMMON=${X86_COMMON_TAR}.tar
                            elif [ "${ARCH}" = "s390x" ]
                            then
                              BIGTARCOMMON=${S390X_COMMON_TAR}.tar
                            fi
                            tar cvf ../${BIGTARCOMMON} --numeric-owner --owner=0 --group=0 ZOA-Common*.tar.gz .featurespec *.run utils/ && rm -Rf ZOA-Common*.tar.gz .featurespec *.run utils/
                        '''
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Publish Docker artifacts for ${interp}") {
                    try {
                        // Publish mega-TAR file to Artifactory
                        sh '''
                            cd DIST
                            if [ "${ARCH}" = "s390x" ]
                            then
                              MEGA_TAR_CORE=${S390X_CORE_TAR}.tar
                              MEGA_TAR_COMMON=${S390X_COMMON_TAR}.tar
                            elif [ "${ARCH}" = "x86_64" ]
                            then
                              MEGA_TAR_CORE=${X86_CORE_TAR}.tar
                              MEGA_TAR_COMMON=${X86_COMMON_TAR}.tar
                              jf rt upload zoacommon_libs.zip sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/
                              jf rt upload ZOACORE-k8s-install.tar.gz sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/
                              jf rt upload ZOACOMMON-k8s-install.tar.gz sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/
                              jf rt upload ibm-kafka-connect-elastic-sink-${CONNECT_SINK_VER}.jar sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/
                            fi
                            jf rt upload --retries=10 --retry-wait-time=60s ${MEGA_TAR_CORE} sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/${ARCH}/
                            jf rt upload --retries=10 --retry-wait-time=60s ${MEGA_TAR_COMMON} sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/${ARCH}/
                        '''
                        // Publish images to Artifactory
                        sh '''
                          set +x
                          . ${HOME}/.af
                          set -x
                          for IMG in $(docker images -f 'label=feature=IBM Z AIOps - Common Services - Core' -q) $(docker images -f 'label=feature=IBM Z AIOps - Common Services - Z Operational Analytics' -q) $(docker images -f 'label=feature=IBM Z AIOps - Common Services - ServiceNow Integration' -q)
                          do
                            IMGINFO=`docker inspect --format=\'{{.RepoTags}}\' ${IMG} | tr -d \'[]\'`
                            for IMAGE in ${IMGINFO}
                            do
                              docker push ${IMAGE}
                            done
                          done
                        '''
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
            }
        }
    }
}

parallel branches

class StageDef implements Serializable {

    String interp

    StageDef(final String interp) {
        this.interp = interp
    }
}
