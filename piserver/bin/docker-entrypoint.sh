#!/bin/bash

PI_HOME=/opt/ibm/piserver
INFO_PREFIX="INFO:    "
ERR_PREFIX="ERROR:   "
WARN_PREFIX="WARNING: "

# Prepare TLS artifacts
if [ -f /shared/config/zoasvc.tls ]
then
  ORIGIN=`pwd`
  mkdir -p /ssl && rm -f /ssl/*
  cd /ssl
  cat /shared/config/zoasvc.tls | base64 -d | tar xz
  cd ${ORIGIN}
else
  echo "ERROR: TLS artifacts not found."
  exit 1
fi

# Make sure all sample config files are in place
for TAR in $( find /opt/ibm/staging -name other*Resources.tar )
do
  echo "${INFO_PREFIX}Extracting configuration samples from ${TAR}..."
  tar -C ${PI_HOME} -xf ${TAR} samples/*
done

LOGMSGML_ENABLED=${LOGMSGML_ENABLED:-"false"}
METRICML_ENABLED=${METRICML_ENABLED:-"false"}
ZDAP_ENABLED=${ZDAP_ENABLED:-"false"}
ELK_ENABLED=${ELK_ENABLED:-"false"}
SPLUNK_ENABLED=${SPLUNK_ENABLED:-"false"}
EVENTS_ENABLED=${EVENTS_ENABLED:-"false"}
LOGMSGML_ENABLED_U=$( echo ${LOGMSGML_ENABLED} | tr [[:lower:]] [[:upper:]] )
METRICML_ENABLED_U=$( echo ${METRICML_ENABLED} | tr [[:lower:]] [[:upper:]] )
ZDAP_ENABLED_U=$( echo ${ZDAP_ENABLED} | tr [[:lower:]] [[:upper:]] )
ELK_ENABLED_U=$( echo ${ELK_ENABLED} | tr [[:lower:]] [[:upper:]] )
SPLUNK_ENABLED_U=$( echo ${SPLUNK_ENABLED} | tr [[:lower:]] [[:upper:]] )
EVENTS_ENABLED_U=$( echo ${EVENTS_ENABLED} | tr [[:lower:]] [[:upper:]] )
PI_SSL_ENABLED=${PI_SSL_ENABLED:-"false"}
PI_SSL_ENABLED_U=$( echo ${PI_SSL_ENABLED} | tr [[:lower:]] [[:upper:]] )

SSL_DEBUG_PARM=""
if [ "$( echo ${SSL_DEBUG} | tr [:lower:] [:upper:] )" == "TRUE" ]
then
  SSL_DEBUG_PARM="ssl"
fi

configLogMsgMl() {
  echo "${INFO_PREFIX}Log-based machine learning does not require any special Problem Insights server configuration."
}

configMetricMl() {
  echo "${INFO_PREFIX}Configuring Problem Insights server support for metric-based machine learning."
  cp ${PI_HOME}/samples/db2zos.config ${PI_HOME}/config/
  sed -i -e "s%^db24zos.user=.*$%db24zos.user=${METRICML_DB_USER}%g" ${PI_HOME}/config/db2zos.config
  METRICML_DB_PWD_C=$( echo ${METRICML_DB_PWD} | base64 -d)
  sed -i -e "s%^db24zos.password=.*$%db24zos.password=${METRICML_DB_PWD_C}%g" ${PI_HOME}/config/db2zos.config
  sed -i -e "s%^db24zos.schema=.*$%db24zos.schema=${METRICML_DB_SCHEMA}%g" ${PI_HOME}/config/db2zos.config
  sed -i -e "s%^db24zos.sid=.*$%db24zos.sid=${METRICML_DB_SERVER_SID}%g" ${PI_HOME}/config/db2zos.config
  sed -i -e "s%^db24zos.serverName=.*$%db24zos.serverName=${METRICML_DB_SERVER_NAME}%g" ${PI_HOME}/config/db2zos.config
  sed -i -e "s%^db24zos.serverPort=.*$%db24zos.serverPort=${METRICML_DB_SERVER_PORT}%g" ${PI_HOME}/config/db2zos.config
}

configZdap() {
  echo "${INFO_PREFIX}Configuring Problem Insights server support for the Z Data Analytics Platform."
  # Remove any old elk.config files
  rm -f ${PI_HOME}/config/splunk.config ${PI_HOME}/config/elk.config
  # Generate new config file
  cp ${PI_HOME}/samples/elk.config ${PI_HOME}/config/
  sed -i -e "s%^es.server=.*$%es.server=datastore%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.port=.*$%es.port=9200%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.esPathPrefix=.*$%es.esPathPrefix=%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.kibanaServer=.*$%es.kibanaServer=${EXTERNAL_GATEWAY_HOST}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.kibanaPort=.*$%es.kibanaPort=${IZOA_GATEWAY_PORT}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.kibanaPathPrefix=.*$%es.kibanaPathPrefix=/insights%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.data.maxsize=.*$%es.data.maxsize=5000%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.xpack.security.enabled=.*$%es.xpack.security.enabled=true%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.user=.*$%es.user=admin%g" ${PI_HOME}/config/elk.config
  API_PASS=$( echo ${API_PWD} | base64 -d )
  sed -i -e "s%^es.password=.*$%es.password=${API_PASS}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.ssl.enabled=.*$%es.ssl.enabled=true%g" ${PI_HOME}/config/elk.config
}

configElk() {
  echo "${INFO_PREFIX}Configuring Problem Insights server support for the Elastic Stack."
  # Remove any old config files
  rm -f ${PI_HOME}/config/splunk.config ${PI_HOME}/config/elk.config
  # Generate new config file
  cp ${PI_HOME}/samples/elk.config ${PI_HOME}/config/
  sed -i -e "s%^es.server=.*$%es.server=${ES_SERVER}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.port=.*$%es.port=${ES_PORT}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.esPathPrefix=.*$%es.esPathPrefix=${ES_PATH_PREFIX}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.kibanaServer=.*$%es.kibanaServer=${ES_KIBANA_SERVER}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.kibanaPort=.*$%es.kibanaPort=${ES_KIBANA_PORT}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.kibanaPathPrefix=.*$%es.kibanaPathPrefix=${ES_KIBANA_PATH_PREFIX}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.data.maxsize=.*$%es.data.maxsize=${ES_DATA_MAX_SIZE}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.xpack.security.enabled=.*$%es.xpack.security.enabled=${ES_XPACK_SECURITY_ENABLED}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.user=.*$%es.user=${ES_USER}%g" ${PI_HOME}/config/elk.config
  ES_PASSWORD_C=$( echo ${ES_PASSWORD} | base64 -d )
  sed -i -e "s%^es.password=.*$%es.password=${ES_PASSWORD_C}%g" ${PI_HOME}/config/elk.config
  sed -i -e "s%^es.ssl.enabled=.*$%es.ssl.enabled=${ES_SSL_ENABLED}%g" ${PI_HOME}/config/elk.config
}

configSplunk() {
  echo "${INFO_PREFIX}Configuring Problem Insights server support for Splunk Enterprise."
  # Remove any old config files
  rm -f ${PI_HOME}/config/splunk.config ${PI_HOME}/config/elk.config
  # Generate new config file
  cp ${PI_HOME}/samples/splunk.config ${PI_HOME}/config/
  sed -i -e "s%^splunk.server=.*$%splunk.server=${SPLUNK_SERVER}%g" ${PI_HOME}/config/splunk.config
  sed -i -e "s%^splunk.port=.*$%splunk.port=${SPLUNK_PORT}%g" ${PI_HOME}/config/splunk.config
  sed -i -e "s%^splunk.webUIPort=.*$%splunk.webUIPort=${SPLUNK_WEB_UI_PORT}%g" ${PI_HOME}/config/splunk.config
  sed -i -e "s%^splunk.user=.*$%splunk.user=${SPLUNK_USER}%g" ${PI_HOME}/config/splunk.config
  SPLUNK_PASSWORD_C=$( echo ${SPLUNK_PASSWORD} | base64 -d )
  sed -i -e "s%^splunk.password=.*$%splunk.password=${SPLUNK_PASSWORD_C}%g" ${PI_HOME}/config/splunk.config
  sed -i -e "s%^splunk.scheme=.*$%splunk.scheme=${SPLUNK_SCHEME}%g" ${PI_HOME}/config/splunk.config
  sed -i -e "s%^splunk.sslVersion=.*$%splunk.sslVersion=${SPLUNK_SSL_VERSION}%g" ${PI_HOME}/config/splunk.config
  sed -i -e "s%^splunk.version=.*$%splunk.version=${SPLUNK_VERSION}%g" ${PI_HOME}/config/splunk.config
  sed -i -e "s%^splunk.maxEvents=.*$%splunk.maxEvents=${SPLUNK_MAX_EVENTS}%g" ${PI_HOME}/config/splunk.config
  sed -i -e "s%^splunk.index=.*$%splunk.index=${SPLUNK_INDEX}%g" ${PI_HOME}/config/splunk.config
}

configEvents() {
  echo "${INFO_PREFIX}Configuring Problem Insights server event forwarding."
  cp ${PI_HOME}/samples/event.config ${PI_HOME}/config/
  sed -i -e "s%^eventapi.user=.*$%eventapi.user=${EVENTAPI_USER}%g" ${PI_HOME}/config/event.config
  EVENTAPI_PASSWORD_C=$( echo ${EVENTAPI_PASSWORD} | base64 -d )
  sed -i -e "s%^eventapi.password=.*$%eventapi.password=${EVENTAPI_PASSWORD_C}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^eventapi.url=.*$%eventapi.url=${EVENTAPI_URL}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.cem.user=.*$%izoa.notification.cem.user=${CEM_USER}%g" ${PI_HOME}/config/event.config
  CEM_PASSWORD_C=$( echo ${CEM_PASSWORD} | base64 -d )
  sed -i -e "s%^izoa.notification.cem.password=.*$%izoa.notification.cem.password=${CEM_PASSWORD_C}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.cem.url=.*$%izoa.notification.cem.url=${CEM_URL}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.chatops.user=.*$%izoa.notification.chatops.user=${ZCHATOPS_USER}%g" ${PI_HOME}/config/event.config
  ZCHATOPS_PASSWORD_C=$( echo ${ZCHATOPS_PASSWORD} | base64 -d )
  sed -i -e "s%^izoa.notification.chatops.password=.*$%izoa.notification.chatops.password=${ZCHATOPS_PASSWORD_C}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.chatops.auth.url=.*$%izoa.notification.chatops.auth.url=${ZCHATOPS_AUTH_URL}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.chatops.incident.url=.*$%izoa.notification.chatops.incident.url=${ZCHATOPS_INCIDENT_URL}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.chatops.post.to.channel=.*$%izoa.notification.chatops.post.to.channel=${ZCHATOPS_CHANNEL}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.email.host=.*$%izoa.notification.email.host=${NOTIFICATION_EMAIL_HOST}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.email.port=.*$%izoa.notification.email.port=${NOTIFICATION_EMAIL_PORT}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.email.username=.*$%izoa.notification.email.username=${NOTIFICATION_EMAIL_USERNAME}%g" ${PI_HOME}/config/event.config
  NOTIFICATION_EMAIL_PASSWORD_C=$( echo ${NOTIFICATION_EMAIL_PASSWORD} | base64 -d )
  sed -i -e "s%^izoa.notification.email.password=.*$%izoa.notification.email.password=${NOTIFICATION_EMAIL_PASSWORD_C}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.email.ssl.enabled=.*$%izoa.notification.email.ssl.enabled=${NOTIFICATION_EMAIL_SSL_ENABLED}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.email.from.address=.*$%izoa.notification.email.from.address=${NOTIFICATION_EMAIL_FROM_ADDRESS}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.email.from.name=.*$%izoa.notification.email.from.name=${NOTIFICATION_EMAIL_FROM_NAME}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.email.subject.prefix=.*$%izoa.notification.email.subject.prefix=${NOTIFICATION_EMAIL_SUBJECT_PREFIX}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^izoa.notification.email.to.address=.*$%izoa.notification.email.to.address=${NOTIFICATION_EMAIL_TO_ADDRESS}%g" ${PI_HOME}/config/event.config
  sed -i -e "s%^forward_messages=.*$%forward_messages=${FORWARD_MESSAGES}%g" ${PI_HOME}/config/event.config
}

configCore() {
  echo "${INFO_PREFIX}Configuring Problem Insights server core features."
  cp ${PI_HOME}/samples/cli.config ${PI_HOME}/config/
  sed -i -e "s%^host=.*$%host=piserver%g" ${PI_HOME}/config/cli.config
  sed -i -e "s%^gatewayhost=.*$%gatewayhost=${EXTERNAL_GATEWAY_HOST}%g" ${PI_HOME}/config/cli.config
  sed -i -e "s%^gatewayport=.*$%gatewayport=${IZOA_GATEWAY_PORT}%g" ${PI_HOME}/config/cli.config
  sed -i -e "s%^keycloak.auth.server.url=.*$%keycloak.auth.server.url=https://${EXTERNAL_GATEWAY_HOST}:${IZOA_GATEWAY_PORT}/${ZAIOPS_KC_CONTEXT_ROOT}/%g" ${PI_HOME}/config/cli.config
  sed -i -e "s%^keycloak.truststore=.*$%keycloak.truststore=/ssl/zoasvc.ts%g" ${PI_HOME}/config/cli.config
  ZAIOPS_ZOASVC_PASS_C=$( echo ${ZAIOPS_ZOASVC_PASS} | base64 -d )
  sed -i -e "s%^keycloak.truststore.password=.*$%keycloak.truststore.password=${ZAIOPS_ZOASVC_PASS_C}%g" ${PI_HOME}/config/cli.config
  sed -i -e "s%^runsInContainer=.*$%runsInContainer=true%g" ${PI_HOME}/config/cli.config
  sed -i -e "s%^loglevel=.*$%loglevel=${PI_TRACE_LOG_LEVEL}%g" ${PI_HOME}/config/cli.config
  sed -i -e "s%^piDbRetentionPeriod=.*$%piDbRetentionPeriod=${PI_DB_RETENTION_PERIOD}%g" ${PI_HOME}/config/cli.config
  if [ "$( echo ${ENSEMBLE_ENABLED} | tr [:upper:] [:lower:] )" == "true" ]
  then
    sed -i -e "s%^isEnsembleEnabled=.*$%isEnsembleEnabled=true%g" ${PI_HOME}/config/cli.config
  fi
  if [ "$( echo ${ZDAP_ENABLED} | tr [:upper:] [:lower:] )" == "true" ]
  then
    if [ "$( echo ${ENSEMBLE_ENABLED} | tr [:upper:] [:lower:] )" == "true" ]
    then
      # Ensemble is not set up to handle ZLDA events
      sed -i -e "s%^isZdapEnabled=.*$%isZdapEnabled=false%g" ${PI_HOME}/config/cli.config
    else
      sed -i -e "s%^isZdapEnabled=.*$%isZdapEnabled=true%g" ${PI_HOME}/config/cli.config
    fi
  fi
  # Clear out any prior trust store settings
  cat ${PI_HOME}/wlp/usr/servers/piFrameworkServer/jvm.options | grep -v javax.net.ssl.trustStore > ${PI_HOME}/wlp/usr/servers/piFrameworkServer/jvm.options.upd
  mv -f ${PI_HOME}/wlp/usr/servers/piFrameworkServer/jvm.options.upd ${PI_HOME}/wlp/usr/servers/piFrameworkServer/jvm.options
  sed -i -e "s%^-Djavax.net.debug=.*$%-Djavax.net.debug=${SSL_DEBUG_PARM}%g" ${PI_HOME}/wlp/usr/servers/piFrameworkServer/jvm.options
  echo "-Djavax.net.ssl.trustStore=/ssl/zoasvc.ts" >> ${PI_HOME}/wlp/usr/servers/piFrameworkServer/jvm.options
  echo "-Djavax.net.ssl.trustStorePassword=${ZAIOPS_ZOASVC_PASS_C}" >> ${PI_HOME}/wlp/usr/servers/piFrameworkServer/jvm.options
  rm -f ${PI_HOME}/wlp/usr/servers/piFrameworkServer/server.xml
  if [ "${PI_SSL_ENABLED_U}" == "FALSE" ]
  then
    cp ${PI_HOME}/wlp/usr/servers/piFrameworkServer/server.xml_nonssl ${PI_HOME}/wlp/usr/servers/piFrameworkServer/server.xml
  elif [ "${PI_SSL_ENABLED_U}" == "TRUE" ]
  then
    cp ${PI_HOME}/wlp/usr/servers/piFrameworkServer/server.xml_ssl ${PI_HOME}/wlp/usr/servers/piFrameworkServer/server.xml
  else
    echo "ERROR: Invalid setting for PI_SSL_ENABLED: ${PI_SSL_ENABLED}"
    echo "       Must be either 'true' or 'false' (case-insensitive)"
    exit 1
  fi
}

# Always perform core configuration
configCore

# Process configuration settings for event forwarding
if [ "${EVENTS_ENABLED_U}" == "TRUE" ]
then
  configEvents
fi

# Process configuration settings for log-based machine learning
if [ "${LOGMSGML_ENABLED_U}" == "TRUE" ]
then
  configLogMsgMl
fi

# Process configuration settings for metric-based machine learning
if [ "${METRICML_ENABLED_U}" == "TRUE" ]
then
  configMetricMl
fi

# Process configuration settings for Z Data Analytics Platform
if [ "${ZDAP_ENABLED_U}" == "TRUE" ]
then
  if [ "${SPLUNK_ENABLED_U}" == "TRUE" ]
  then
    echo "${ERR_PREFIX}The Problem Insights server can only support one search platform at a time."
    echo "Currently enabled: Z Data Analytics Platform and Splunk."
    echo "Unable to proceed."
    exit 1
  elif [ "${ELK_ENABLED_U}" == "TRUE" ]
  then
    echo "${ERR_PREFIX}The Problem Insights server can only support one search platform at a time."
    echo "Currently enabled: Z Data Analytics Platform and Elastic Stack."
    echo "Unable to proceed."
    exit 1
  else
    if [ "$( echo ${ENSEMBLE_ENABLED} | tr [:upper:] [:lower:] )" != "true" ]
    then
      configZdap
    else
      echo "${INFO_PREFIX}Z Operational Log and Data Analytics event integration is not compatible with Z Anomaly Analytics Ensemble."
    fi
  fi
fi

# Process configuration settings for Splunk
if [ "${SPLUNK_ENABLED_U}" == "TRUE" ]
then
  if [ "${ZDAP_ENABLED_U}" == "TRUE" ]
  then
    echo "${ERR_PREFIX}The Problem Insights server can only support one search platform at a time."
    echo "Currently enabled: Splunk and Z Data Analytics Platform."
    echo "Unable to proceed."
    exit 1
  elif [ "${ELK_ENABLED_U}" == "TRUE" ]
  then
    echo "${ERR_PREFIX}The Problem Insights server can only support one search platform at a time."
    echo "Currently enabled: Splunk and Elastic Stack."
    echo "Unable to proceed."
    exit 1
  else
    if [ "$( echo ${ENSEMBLE_ENABLED} | tr [:upper:] [:lower:] )" != "true" ]
    then
      configSplunk
    else
      echo "${INFO_PREFIX}Z Operational Log and Data Analytics event integration is not compatible with Z Anomaly Analytics Ensemble."
    fi
  fi
fi

# Process configuration settings for Elastic Stack
if [ "${ELK_ENABLED_U}" == "TRUE" ]
then
  if [ "${ZDAP_ENABLED_U}" == "TRUE" ]
  then
    echo "${ERR_PREFIX}The Problem Insights server can only support one search platform at a time."
    echo "Currently enabled: Elastic Stack and Z Data Analytics Platform."
    echo "Unable to proceed."
    exit 1
  elif [ "${SPLUNK_ENABLED_U}" == "TRUE" ]
  then
    echo "${ERR_PREFIX}The Problem Insights server can only support one search platform at a time."
    echo "Currently enabled: Elastic Stack and Splunk."
    echo "Unable to proceed."
    exit 1
  else
    if [ "$( echo ${ENSEMBLE_ENABLED} | tr [:upper:] [:lower:] )" != "true" ]
    then
      configElk
    else
      echo "${INFO_PREFIX}Z Operational Log and Data Analytics event integration is not compatible with Z Anomaly Analytics Ensemble."
    fi
  fi
fi

cd ${PI_HOME}
./bin/analysis.sh createBootstrap
export WLP_SKIP_UMASK=Y
./wlp/bin/server run piFrameworkServer
