FROM icr.io/zoa-oci/zoacommon-jre21-micro:21.0.5_11-x86_64

ARG REPO=unknown
ARG COMMIT=unknown

LABEL feature="IBM Z AIOps - Common Services - Z Operational Analytics"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

ENV PI_HOME=/opt/ibm/piserver
ENV PATH=${PATH}:${PI_HOME}/bin

COPY ./piserver.tar.gz .
COPY ./zoa-common-docker-MF/piserver/bin/docker-entrypoint.sh ./zoa-common-docker-MF/piserver/bin/healthcheck.sh /usr/local/bin/

# EXPOSE 9446

RUN chmod +x /usr/local/bin/docker-entrypoint.sh /usr/local/bin/healthcheck.sh&& \
    mkdir -p /opt/ibm/staging && \
    mkdir -p /ssl && \
    tar xzf ./piserver.tar.gz -C /opt/ibm && \
    rm -f ./piserver.tar.gz && \
    chgrp -R 0 /opt/ibm/piserver /opt/ibm/staging /ssl && \
    chmod -R g=u /opt/ibm/piserver /opt/ibm/staging /ssl

USER piserver

HEALTHCHECK NONE
# HEALTHCHECK CMD /usr/local/bin/healthcheck.sh

CMD ["docker-entrypoint.sh"]
