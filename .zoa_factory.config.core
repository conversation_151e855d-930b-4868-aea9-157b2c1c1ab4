###############################################################################
# DO NOT MODIFY THE SETTINGS IN THIS FILE UNLESS INSTRUCTED BY IBM SUPPORT    #
###############################################################################

###############################################################################
# CORE SERVICES SETTINGS                                                    #
###############################################################################
TAG=latest
DOCKER_GATEWAY_HOST=**********
COMPOSE_PROJECT_NAME=ibmzaiops
OCI_AGENT=docker
OCI_VERSION=
SSL_ENABLED=false
SSL_KEYSTORE=
SSL_PWD=
EUREKA_REGISTER=true
EUREKA_ENABLED=true
HOST_KEYCLOAK_INTERNAL=127.0.0.1
EXTERNAL_GATEWAY_HOST=
PI_FRAMEWORK_ROUTE=http://piserver:9446/piFramework/
ZAIOPS_KEYCLOAK_HOST=
ZAIOPS_KEYCLOAK_IP=
SSL_DEBUG=false
SERVICE_TIMEOUT=180
ZAIOPS_PROXY_HEADERS=forwarded
HOSTNAME_STRICT=false
KC_FORCE_EXTERNAL_HOSTNAME=false
KC_TRUSTSTORE_PATHS=/ssl/zoasvc_all.pem
ZAIOPS_KC_CONTEXT_ROOT=auth
ZAIOPS_KC_KEYSTORE_FILE=/ssl/zoasvc.ks.p12
ZAIOPS_KC_TRUSTSTORE_FILE=/ssl/zoasvc.ts.p12
ZAIOPS_KC_KEYSTORE_TYPE=pkcs12
ZAIOPS_KC_TRUSTSTORE_TYPE=pkcs12
ZAIOPS_KC_DB=dev-file
ZAIOPS_KC_DB_USER=
ZAIOPS_KC_DB_PWD=
ZAIOPS_KC_DB_URL=
ZAIOPS_KC_REALM=IzoaKeycloak
ZAIOPS_KEYCLOAK_ADMIN=keycloakadmin
ZAIOPS_KC_REALM_ADMIN=zoakcadmin
ZAIOPS_KC_HTTPS_PORT=8443
ZAIOPS_KC_CACHE_MODE=local
ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=true
ZAIOPS_KC_BOOTSTRAP_ADMIN=kcadmintemp
ZAIOPS_KC_BOOTSTRAP_PASSWORD=Y2hhbmdlbWU=
KC_INTERNAL_HOST=auth
KEYCLOAK_URI_INTERNAL=http://${KC_INTERNAL_HOST}:8080/${ZAIOPS_KC_CONTEXT_ROOT}/realms/IzoaKeycloak/protocol/openid-connect/certs
#KEYCLOAK_URI=https://${EXTERNAL_GATEWAY_HOST}:${ZAIOPS_KEYCLOAK_PORT}/${ZAIOPS_KC_CONTEXT_ROOT}/realms/IzoaKeycloak/protocol/openid-connect/certs
KEYCLOAK_URI=${KEYCLOAK_URI_INTERNAL}
#KEYCLOAK_ROUTE=https://${EXTERNAL_GATEWAY_HOST}:${ZAIOPS_KEYCLOAK_PORT}/${ZAIOPS_KC_CONTEXT_ROOT}/
KEYCLOAK_ROUTE=http://${KC_INTERNAL_HOST}:8080/${ZAIOPS_KC_CONTEXT_ROOT}/

# Gateway settings
ENABLE_HTTP2=false

# Logging level to use for debug logging of Kafka Broker and Datastore. Use either 'DEBUG' or 'TRACE'
ZOA_DEBUG_LEVEL=DEBUG

# UID under which to run containers
ZOA_UID=1000

# Lines of container log to return when a container fails to staart up in the allotted time
TS_LOG_LINES=40

# Settings for Kubernetes / OCP
# Inter-pod communication under K8s requires a service that represents a subdomain
# Leave this blank for Docker and Podman; set it to 'zoa' (or some other suitable value) for K8s
SUBDOMAIN=
NAMESPACE=zlda-for-support
REG_SECRET=zoacred
# Valid values for image pull policy: Always, IfNotPresent, Never
# During development, with frequent image changeas, it might be useful to set this value to 'Always' even though it will make pods start up more slowly
# Once image content becomes stable, 'IfNotPresent' might be more suitable.
IMAGE_PULL_POLICY=Always
