diff --git a/common/src/main/java/io/netty/util/internal/PlatformDependent.java b/common/src/main/java/io/netty/util/internal/PlatformDependent.java
index ed9b313256..f64870c64a 100644
--- a/common/src/main/java/io/netty/util/internal/PlatformDependent.java
+++ b/common/src/main/java/io/netty/util/internal/PlatformDependent.java
@@ -1631,6 +1631,9 @@ public final class PlatformDependent {
 
     private static String normalizeOs(String value) {
         value = normalize(value);
+        if (value.startsWith("zos")) {
+            return "zos";
+        }
         if (value.startsWith("aix")) {
             return "aix";
         }
diff --git a/resolver-dns/src/main/java/io/netty/resolver/dns/UnixResolverDnsServerAddressStreamProvider.java b/resolver-dns/src/main/java/io/netty/resolver/dns/UnixResolverDnsServerAddressStreamProvider.java
index f8eff1740e..0a7ccafa1d 100644
--- a/resolver-dns/src/main/java/io/netty/resolver/dns/UnixResolverDnsServerAddressStreamProvider.java
+++ b/resolver-dns/src/main/java/io/netty/resolver/dns/UnixResolverDnsServerAddressStreamProvider.java
@@ -16,6 +16,7 @@
 package io.netty.resolver.dns;
 
 import io.netty.util.NetUtil;
+import io.netty.util.internal.PlatformDependent;
 import io.netty.util.internal.SocketUtils;
 import io.netty.util.internal.logging.InternalLogger;
 import io.netty.util.internal.logging.InternalLoggerFactory;
@@ -53,12 +54,15 @@ public final class UnixResolverDnsServerAddressStreamProvider implements DnsServ
     private static final String ETC_RESOLV_CONF_FILE = "/etc/resolv.conf";
     private static final String ETC_RESOLVER_DIR = "/etc/resolver";
     private static final String NAMESERVER_ROW_LABEL = "nameserver";
+    private static final String NAMESERVER_ROW_LABEL_ZOS = "NSINTERADDR";
     private static final String SORTLIST_ROW_LABEL = "sortlist";
     private static final String OPTIONS_ROW_LABEL = "options ";
     private static final String OPTIONS_ROTATE_FLAG = "rotate";
     private static final String DOMAIN_ROW_LABEL = "domain";
+    private static final String DOMAIN_ROW_LABEL_ZOS = "DOMAINORIGIN";
     private static final String SEARCH_ROW_LABEL = "search";
     private static final String PORT_ROW_LABEL = "port";
+    private static final String PORT_ROW_LABEL_ZOS = "NSPORTADDR";
 
     private final DnsServerAddresses defaultNameServerAddresses;
     private final Map<String, DnsServerAddresses> domainToNameServerStreamMap;
@@ -172,6 +176,9 @@ public final class UnixResolverDnsServerAddressStreamProvider implements DnsServ
                 boolean rotate = rotateGlobal;
                 int port = DNS_PORT;
                 String line;
+                String nameserverTestLabel = NAMESERVER_ROW_LABEL;
+                String domainTestLabel = DOMAIN_ROW_LABEL;
+                String portTestLabel = PORT_ROW_LABEL;
                 while ((line = br.readLine()) != null) {
                     line = line.trim();
                     try {
@@ -181,10 +188,13 @@ public final class UnixResolverDnsServerAddressStreamProvider implements DnsServ
                         }
                         if (!rotate && line.startsWith(OPTIONS_ROW_LABEL)) {
                             rotate = line.contains(OPTIONS_ROTATE_FLAG);
-                        } else if (line.startsWith(NAMESERVER_ROW_LABEL)) {
-                            int i = indexOfNonWhiteSpace(line, NAMESERVER_ROW_LABEL.length());
+                        } else if (line.startsWith(NAMESERVER_ROW_LABEL) || line.startsWith(NAMESERVER_ROW_LABEL_ZOS)) {
+                            if (line.startsWith(NAMESERVER_ROW_LABEL_ZOS)) {
+                                nameserverTestLabel = NAMESERVER_ROW_LABEL_ZOS;
+                            }
+                            int i = indexOfNonWhiteSpace(line, nameserverTestLabel.length());
                             if (i < 0) {
-                                throw new IllegalArgumentException("error parsing label " + NAMESERVER_ROW_LABEL +
+                                throw new IllegalArgumentException("error parsing label " + nameserverTestLabel +
                                         " in file " + etcResolverFile + ". value: " + line);
                             }
                             String maybeIP;
@@ -195,7 +205,7 @@ public final class UnixResolverDnsServerAddressStreamProvider implements DnsServ
                                 // ignore comments
                                 int idx = indexOfNonWhiteSpace(line, x);
                                 if (idx == -1 || line.charAt(idx) != '#') {
-                                    throw new IllegalArgumentException("error parsing label " + NAMESERVER_ROW_LABEL +
+                                    throw new IllegalArgumentException("error parsing label " + nameserverTestLabel +
                                             " in file " + etcResolverFile + ". value: " + line);
                                 }
                                 maybeIP = line.substring(i, x);
@@ -205,7 +215,7 @@ public final class UnixResolverDnsServerAddressStreamProvider implements DnsServ
                             if (!NetUtil.isValidIpV4Address(maybeIP) && !NetUtil.isValidIpV6Address(maybeIP)) {
                                 i = maybeIP.lastIndexOf('.');
                                 if (i + 1 >= maybeIP.length()) {
-                                    throw new IllegalArgumentException("error parsing label " + NAMESERVER_ROW_LABEL +
+                                    throw new IllegalArgumentException("error parsing label " + nameserverTestLabel +
                                             " in file " + etcResolverFile + ". invalid IP value: " + line);
                                 }
                                 port = Integer.parseInt(maybeIP.substring(i + 1));
@@ -219,10 +229,13 @@ public final class UnixResolverDnsServerAddressStreamProvider implements DnsServ
                             if (!addr.isUnresolved()) {
                                 addresses.add(addr);
                             }
-                        } else if (line.startsWith(DOMAIN_ROW_LABEL)) {
-                            int i = indexOfNonWhiteSpace(line, DOMAIN_ROW_LABEL.length());
+                        } else if (line.startsWith(DOMAIN_ROW_LABEL) || line.startsWith(DOMAIN_ROW_LABEL_ZOS)) {
+                            if (line.startsWith(DOMAIN_ROW_LABEL_ZOS)) {
+                                domainTestLabel = DOMAIN_ROW_LABEL_ZOS;
+                            }
+                            int i = indexOfNonWhiteSpace(line, domainTestLabel.length());
                             if (i < 0) {
-                                throw new IllegalArgumentException("error parsing label " + DOMAIN_ROW_LABEL +
+                                throw new IllegalArgumentException("error parsing label " + domainTestLabel +
                                         " in file " + etcResolverFile + " value: " + line);
                             }
                             domainName = line.substring(i);
@@ -230,10 +243,13 @@ public final class UnixResolverDnsServerAddressStreamProvider implements DnsServ
                                 putIfAbsent(domainToNameServerStreamMap, domainName, addresses, rotate);
                             }
                             addresses = new ArrayList<InetSocketAddress>(2);
-                        } else if (line.startsWith(PORT_ROW_LABEL)) {
-                            int i = indexOfNonWhiteSpace(line, PORT_ROW_LABEL.length());
+                        } else if (line.startsWith(PORT_ROW_LABEL) || line.startsWith(PORT_ROW_LABEL_ZOS)) {
+                            if (line.startsWith(PORT_ROW_LABEL_ZOS)) {
+                                portTestLabel = PORT_ROW_LABEL_ZOS;
+                            }
+                            int i = indexOfNonWhiteSpace(line, portTestLabel.length());
                             if (i < 0) {
-                                throw new IllegalArgumentException("error parsing label " + PORT_ROW_LABEL +
+                                throw new IllegalArgumentException("error parsing label " + portTestLabel +
                                         " in file " + etcResolverFile + " value: " + line);
                             }
                             port = Integer.parseInt(line.substring(i));
@@ -383,6 +399,11 @@ public final class UnixResolverDnsServerAddressStreamProvider implements DnsServ
                     if (i >= 0) {
                         localDomain = line.substring(i);
                     }
+                } else if (localDomain == null && line.startsWith(DOMAIN_ROW_LABEL_ZOS)) {
+                    int i = indexOfNonWhiteSpace(line, DOMAIN_ROW_LABEL_ZOS.length());
+                    if (i >= 0) {
+                        localDomain = line.substring(i);
+                    }
                 } else if (line.startsWith(SEARCH_ROW_LABEL)) {
                     int i = indexOfNonWhiteSpace(line, SEARCH_ROW_LABEL.length());
                     if (i >= 0) {
