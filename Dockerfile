######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ZAA (C) Copyright IBM Corp. 2021
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

# Docker Image which is used as foundation for our Docker Image
# to be based off of
FROM icr.io/zoa-oci/zoacommon-jre21:21.0.5_11-x86_64


USER root



ARG REPO=unknown
ARG COMMIT=unknown

WORKDIR /app

# need to run 'mvn clean package' firstly
# copy tar.gz package to /app and uncompress
ADD target/*.tar.gz .

LABEL feature="IBM Z AIOps - Z Resource Discovery Data Service - Ext API"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

RUN mv zrdds-ext-api* zrdds-ext-api

COPY docker/docker-entrypoint.sh /app/
RUN chmod +x docker-entrypoint.sh && \
    groupadd zaiops && \
    useradd -g zaiops discovery && \
    chown -R discovery:zaiops /app/zrdds-ext-api

ENV HOME /home/<USER>

USER discovery

ENTRYPOINT ["./docker-entrypoint.sh"]

CMD ["java","-jar","./zrdds-ext-api.jar"]
