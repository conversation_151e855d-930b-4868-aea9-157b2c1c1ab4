# Copyright 2020 IBM Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

name=elastic-sink
connector.class=com.ibm.eventstreams.connect.elasticsink.ElasticSinkConnector

# You can increase this for higher throughput to match the number of topic partitions
tasks.max=1

# The list of source Kafka topics
topics=

# The hostname:port to connect to Elasticsearch.
es.connection=localhost:9200

# Conversion of the Kafka message to a document is done via this class
es.document.builder=com.ibm.eventstreams.connect.elasticsink.builders.JsonDocumentBuilder
# Creation of the index for the document is done via this class
es.index.builder=com.ibm.eventstreams.connect.elasticsink.builders.DefaultIndexBuilder
# Creation of the identifier for the document is done via this class
es.identifier.builder=com.ibm.eventstreams.connect.elasticsink.builders.DefaultIdentifierBuilder
#es.identifier.builder=com.ibm.eventstreams.connect.elasticsink.builders.KeyIdentifierBuilder

# Authentication to Elasticsearch. Defaults are empty
#es.user.name=
#es.password= 
 
# JKS Keystore locations and passwords. 
# TLS is used for connection to Elasticsearch if any of these are set
#es.tls.keystore.location=
#es.tls.keystore.password=
#es.tls.truststore.location=
#es.tls.truststore.password=
 
# HTTP Proxy information. Default port is 8080  
#es.http.proxy.host=
#es.http.proxy.port=8080

# HTTP/Jetty configuration. Timers given in seconds
# Operation timeout is the one you are most likely to want to change
#es.http.timeout.operation=6
#es.http.timeout.idle=30
#es.http.timeout.connection=5
#es.http.connections=10
# Maximum number of uninterrupted failures before considering the connection failed
#es.max.failures=5
 
# These converters control conversion of data between the internal Kafka Connect representation and the messages in Kafka.
key.converter=org.apache.kafka.connect.storage.StringConverter
value.converter=org.apache.kafka.connect.json.JsonConverter
value.converter.schemas.enable=false
