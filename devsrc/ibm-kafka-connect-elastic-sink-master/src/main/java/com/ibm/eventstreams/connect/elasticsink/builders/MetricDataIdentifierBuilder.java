package com.ibm.eventstreams.connect.elasticsink.builders;

import org.apache.kafka.connect.json.JsonConverter;
import org.apache.kafka.connect.sink.SinkRecord;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * Builds document identifiers from Kafka Connect SinkRecords using metric data records.
 */
public class MetricDataIdentifierBuilder implements IdentifierBuilder{
    private static final Logger log = LoggerFactory.getLogger(MetricDataIdentifierBuilder.class);
    private final JsonConverter converter = new JsonConverter();

    public MetricDataIdentifierBuilder() {
        HashMap<String, String> m = new HashMap<>();
        m.put("schemas.enable", "false");
        converter.configure(m, false);
    }

    /**
     * Convert a Kafka Connect SinkRecord into a document identifier.
     *
     * @param record             the Kafka Connect SinkRecord
     *
     * @return the document identifier
     */
    @Override
    public String fromSinkRecord(SinkRecord record) {
        byte[] payload = converter.fromConnectData(record.topic(), record.valueSchema(), record.value());
        if (payload != null) {
            String document = new String(payload, UTF_8);
            JSONObject jsonObject = new JSONObject(document);
            String metrics = "";
            if(jsonObject.has("metricName")){
                metrics=jsonObject.get("metricName").toString();
            }else if(jsonObject.has("contributingMetrics")){
                JSONArray contributingMetrics = jsonObject.getJSONArray("contributingMetrics");
                metrics = contributingMetrics.length()+"KPIs";
            }else{
                metrics="missing";
            }

            try {
                //id for the document of the metric data record which is composed of the record's timestamp, system ID,
                // subsystem type, subsystem name and metric name.
                return String.join("_",
                        jsonObject.get("timestamp").toString(),
                        jsonObject.get("mvsSystemId").toString(),
                        jsonObject.get("resourceType").toString(),
                        jsonObject.get("resourceName").toString(),
                        metrics);
            } catch (JSONException e){
                log.info("Unable to get field from record, {}, building default document ID instead.", record.value().toString());
            }
        }
        return record.topic() + "!" + record.kafkaPartition() + "!" + record.kafkaOffset();
    }

    /**
     * Whether document identifiers generated by this builder are unique.
     *
     * @return true if unique, false if not
     */
    @Override
    public boolean isUnique() {
        return false;
    }
}
