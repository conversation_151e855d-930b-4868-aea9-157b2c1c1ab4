/**
 * Copyright 2020 IBM Corporation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ibm.eventstreams.connect.elasticsink.builders;

import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.sink.SinkRecord;

/**
 * Builds document identifiers from Kafka Connect SinkRecords
 */
public class KeyIdentifierBuilder implements IdentifierBuilder {
    /**
     * Convert a Kafka Connect SinkRecord into a document identifier.
     *
     * @param record             the Kafka Connect SinkRecord
     *
     * @return the document identifier
     */
    @Override
    public String fromSinkRecord(SinkRecord record) {
        if (record.key() == null) {
            throw new ConnectException("Key must be present to use as document ID.");
        }
        return record.key().toString();
    }

    /**
     * Whether document identifiers generated by this builder are unique.
     *
     * @return true if unique, false if not
     */
    @Override
    public boolean isUnique() {
        // Kafka record keys are not guaranteed to be unique
        return false;
    }
}