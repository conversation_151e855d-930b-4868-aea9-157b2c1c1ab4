<?xml version="1.0" encoding="UTF-8"?>
<!--
 * Copyright 2020 IBM Corporation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 -->
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0
    http://maven.apache.org/xsd/assembly-2.0.0.xsd">
  <id>jar-with-dependencies</id>
  <formats>
    <format>jar</format>
  </formats>
  <includeBaseDirectory>false</includeBaseDirectory>
  <files>
    <file>
      <source>LICENSE</source>
      <outputDirectory></outputDirectory>
    </file>
  </files>
  <dependencySets>
    <dependencySet>
      <outputDirectory></outputDirectory>
      <unpack>true</unpack>
      <unpackOptions>
        <excludes>
          <exclude>**/copyright-exclude</exclude>
          <exclude>META-INF/maven/**</exclude>
        </excludes>
      </unpackOptions>
      <useTransitiveFiltering>true</useTransitiveFiltering>
      <scope>runtime</scope>
    </dependencySet>
  </dependencySets>
</assembly>
