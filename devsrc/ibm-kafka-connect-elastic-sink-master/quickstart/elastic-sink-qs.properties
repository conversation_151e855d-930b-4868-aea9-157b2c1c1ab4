# Copyright 2020 IBM Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

name=elastic-sink
connector.class=com.ibm.eventstreams.connect.elasticsink.ElasticSinkConnector

# The list of source Kafka topics
topics=ESSINKTEST

# The hostname:port to connect to Elasticsearch.
es.connection=elastic:9200

# Conversion of the Kafka message to a document is done via this class
es.document.builder=com.ibm.eventstreams.connect.elasticsink.builders.JsonDocumentBuilder
# Creation of the index for the document is done via this class
es.index.builder=com.ibm.eventstreams.connect.elasticsink.builders.DefaultIndexBuilder
# Creation of the identifier for the document is done via this class
es.identifier.builder=com.ibm.eventstreams.connect.elasticsink.builders.DefaultIdentifierBuilder

# These converters control conversion of data between the internal Kafka Connect representation and the messages in Kafka.
key.converter=org.apache.kafka.connect.storage.StringConverter
value.converter=org.apache.kafka.connect.json.JsonConverter
value.converter.schemas.enable=false