on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+' # Push events to matching semver, i.e. 10.0.0

name: Build and create release

jobs:
  build:
    name: Build and upload release binary
    #if: github.event.base_ref == 'refs/heads/master' # only run if on master branch
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set up JDK 1.8
        uses: actions/setup-java@v1
        with:
          java-version: 1.8
      - name: Get java-version
        run: |
          BUILD_VERSION=$( mvn help:evaluate -Dexpression=project.version -q -DforceStdout )
          echo "::set-env name=VERSION::$BUILD_VERSION"
      - name: Build
        run: mvn package
      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
      - name: Upload Release Asset
        id: upload-release-asset 
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./target/kafka-connect-elastic-sink-${{env.VERSION}}-jar-with-dependencies.jar
          asset_name: kafka-connect-elastic-sink-${{env.VERSION}}-jar-with-dependencies.jar
          asset_content_type: application/java-archive
