# zoa-common-docker

## Purpose
To build OCI images shared between ZAA and ZLDA

## Development _build_ environment vs. development _runtime_ environment
Building software containers (and, if required, their precursors) will not happen in the same shell
as running the same software containers for development / test purposes. The main reason is that 
containers -- once built and stored in the local OCI repository -- can be _run_ from any location,
whereas the same is not possible for _building_ the container images given our current compose file
structure.

To build the container images included in ZAA and ZLDA, individual build processes need to be launched
in one, several or all of the following repo directories:
* IZOA/zoa-docker-base-images
* IZOA/zoa-os_plugin-customizations (OpenSearch plugin pre-build)
* IZOA/zoa-common-docker-MF
* IZOA/izoa-zaware-docker-MF
* IZOA/metric-ml-docker-MF
* ZLDA/zdap-osd-customizations (OpenSearch Dashboards pre-build [incl. plugins])
* ZLDA/zlda-zdap-docker

In the end, a complete development environment for OCI images will require at least two shell sessions:
* One to build container images
* The other to launch, manage and test the containers created from the container images

## Common prerequisites and preparation
### Hard prerequisites
* docker
* docker compose
* ability to connect to the Docker daemon socket
* bash shell as your runtime environment
* python3
* GNU sed
  * **NOTE:** MacOS users should install GNU sed via homebrew (`brew install gnu-sed`). It is not necessary to replace
    the default sed. The standard installation will result in a 'gsed' executable in the system path. 
    The 'setup-local-env.sh' script will alias 'gsed' to 'sed' for its own operation.
* GNU tar
  * **NOTE:** MacOS users should install GNU tar via homebrew (`brew install gnu-tar`). It is not necessary to replace
    the default tar. The standard installation will result in a 'gtar' executable in the system path. 
    The 'setup-local-env.sh' script will alias 'gtar' to 'tar' for its own operation.

### Soft prerequisites
* Rancher Desktop
    * Rancher Desktop provides the hard prerequisites (except for bash) to you on both MacOS and Windows with WSL2. 
    * If you don't use Rancher Desktop, then you need some other means of getting the hard prerequisites
      installed in your local development environment.

### Preparation
* Create a configuration file by
  * copying `./devutils/samples/.zoalocal` to `${HOME}/`
  * editing `${HOME}/.zoalocal` to ensure the properties in it have the correct values.
    See the comments in the file for instructions.

## How to build in a development environment
* Make sure you have all the participating GitHub repos cloned into your development environment.
* Run the following commands to set up environment variables and to invoke the build:
```sh
. ./devutils/build-prep.sh        # Run this command EXACTLY as shown from the repo's top-level directory
docker compose -f docker-compose-dev.yml build (<service>)
```

* To reset docker compose files and Dockerfiles to the x86_64 architecture tags stored in source control,
  run this command:
```
./devutils/build-clean.sh
```

## How to run from a development environment
### Use
#### Common usage
* Run the following commands:
```sh
rdctl start                                    # to start Rancher Desktop
./devutils/setup-local-env.sh prepare          # to create a working directory; 
                                               #   needs to be run after each time you run the 'clean' command
./devutils/setup-local-env.sh gen-tls          # to create TLS artifacts needed by some of the containers; 
                                               #   needs to be run after each time you run the 'clean' command or after each 
                                               #   time you delete the zaiops_shared volume
./devutils/setup-local-env.sh enable-metricml  # OPTIONAL: to copy artifacts from IZOA/metric-ml-docker-MF into the working directory
./devutils/setup-local-env.sh enable-logmsgml  # OPTIONAL: to copy artifacts from IZOA/izoa-zaware-docker-MF into the working directory
./devutils/setup-local-env.sh enable-zaacommon # OPTIONAL: to copy artifacts from IZOA/zaa-common-docker-MF into the working directory
./devutils/setup-local-env.sh enable-zdap      # OPTIONAL: to copy artifacts from ZLDA/zlda-zdap-docker into the working directory
./devutils/setup-local-env.sh oci-shell        # to open a new shell with required environment variables preloaded and with
                                               #   some convenience commands to simplify managing the OCI containers
```

* Once in the OCI shell, run the built-in command `zoabaseserial` to bring up the common components in an orderly fashion _and_ 
  create the required Keycloak realm.
  * Alternatively, you can run the following command(s) if you don't want to bring up all common components and/or want more
    control over the startup sequence:
```sh
zoacompose up -d <service_1> [ <service_2> <service_3> ... ]
```

**Note:**
* If you use the `zoacompose` command instead of the `zoabaseserial` command, the IzoaKeycloak realm is not automatically
  created for you. You will need to run the built-in command `zoagenrealm` to generate it.

#### Special considerations for ZAA
* Before running `./devutils/setup-local-env.sh oci-shell`, run either the `enable-metricml` command or the `enable-logmsgml`
  or the `enable-zaacommon` command (or multiple or all) to enable MetricML, LogMsgML and/or ZAA Common containers in the OCI 
  shell.
* There is currently no process for automatically downloading insight packs. If you need an insight pack installed, do 
  the following:
  * Download or locally build the insight pack ZIP file.
  * Run `zoaloadip` to load the insight pack into the PI server.
  * Log into the PI server administrative web UI to manage the insight pack as usual.

#### Special considerations for ZLDA (ZDAP)
* Before running `./devutils/setup-local-env.sh oci-shell`, run the `enable-zdap` command to enable ZDAP containers 
  in the OCI shell.
* Once in the OCI shell, you can download the latest builds of the dashboards and parser configuration files using the
  `zdapgetextensions` built-in command. And you can load them into the running containers using the `zdapload` built-in
  command.
* There is currently no process for automatically downloading message library XML files. If you need a message library
  XML file installed, do the following:
  * Locate the message library file in source control (or locate your own hown-spun message library file).
  * Run `zoaimportlibrary` to load the message file into the PI server.

### Post-installation configuration
#### Reconfiguration after IP address change
In case of a location change (e.g., from home to office), the IP address used by the tooling to configure the containers 
and TLS artifacts may change. In this situation, take the following steps to update the configuration:
* If you are in the OCI shell, exit it via `<Ctrl-D>` or `exit`.
* Run `./devutils/setup-local-env.sh gen-tls` to refresh the TLS artifacts.
* Run `./devutils/setup-local-env.sh oci-shell` to re-enter the OCI shell. Once inside the OCI shell, do the following:
  * If any containers are running, shut them down: `zoacompose down`
  * Bring up the desired containers again: `zoabaseserial` or `zoacompose up -d [<service>]`
  * If you used `zoacompose` to start the containers, run `zoagenrealm` to update the realm configuration settings
    that are sensitive to IP address changes. (If you used `zoabaseserial`, `zoagenrealm` was run for you automatically.)
  * If you have insight packs installed, run `zoaupdateips` to update IP address references in the installed insight packs.

### Cleaning up
* If you need or want to get rid of your working directory, exit the OCI shell and run the following commands:
```sh
./devutils/setup-local-env.sh clean            # To remove the working directory.
                                               #   NOTE: This command currently does NOT delete the Docker volumes.
docker volume rm $( docker volume ls -q | grep zaiops_ )
                                               # OPTIONAL: To remove the Docker volumes.
```

### Synopsis: OCI shell built-in commands

| Command | Use case | What it does... |
|---------|----------|-----------------|
| zoahelp | ZAA / ZLDA | list of built-in commands with brief explanations (this list) |
| zoacompose | ZAA / ZLDA | shorthand for `docker compose -f <compose_file_1> -f <compose_file_2> ...` |
| zoabaseserial | ZAA / ZLDA | bring up ZOA base services one at a time to avoid contention during startup |
| zoagenrealm | ZAA / ZLDA | create IzoaKeycloak realm after starting auth (Keycloak) service |
| zoadown | ZAA / ZLDA | stop and remove all ZOACOMMON containers |
| zoagetlibraries | ZAA / ZLDA | get list of message libraries stored in the PI server |
| zoadeletelibrary | ZAA / ZLDA | delete a single message library stored in the PI server |
| zoaexportlibrary | ZAA / ZLDA | export a single message library stored in the PI server |
| zoaimportlibrary | ZAA / ZLDA | import a single new message library into the PI server |
| zoaloadip | ZAA | load a single insight pack ZIP file into the PI server and configure it to use correct IP address and port |
| zoaupdateips | ZAA | update IP address and port for insight packs already loaded into PI server |
| logmsgmldown | ZAA | stop and remove all LogMsgML containers |
| metricmldown | ZAA | stop and remove all MetricML containers |
| zdapgetextensions | ZLDA | download parser, datastore and dashboards extensions for ZDAP and unpack them into `<WORKDIR>/extensions` |
| zdapload | ZLDA | load dashboards, visualizations and searches as well as parser configuration into ZDAP |
| zdapdown | ZLDA | stop and remove all ZDAP containers |
