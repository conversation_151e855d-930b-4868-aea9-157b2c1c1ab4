# Kafka-Sieve

A Kafka Connect transformation plugin that filters messages based on column changes before they are sent to Kafka topics.

## Overview

Kafka-Sieve is a Single Message Transform (SMT) for Kafka Connect that allows you to filter out messages based on specific column changes. This is particularly useful when you want to:

- Ignore changes to timestamp columns like `scan_date` and `kafka_send_date`
- Reduce noise in your Kafka topics by filtering out irrelevant changes
- Implement custom filtering logic for different tables

## Requirements

- Java 21
- Kafka Connect 4.0.0 or higher
- Maven 3.6+ (for building)

## Building

```bash
mvn clean package
```

This will create a JAR file in the `target` directory.

## Installation

1. Build the plugin or download the pre-built JAR
2. Create a directory named `kafka-sieve` in your Kafka Connect plugins directory (e.g., `/opt/kafka/connect/kafka-sieve`)
3. Copy the JAR file to this directory
4. Restart Kafka Connect

## Configuration

Add the following to your connector configuration:

```properties
# Add the transform to your connector
transforms=unwrap,sieve

# Configure ExtractNewRecordState (if using Debezium)
transforms.unwrap.type=io.debezium.transforms.ExtractNewRecordState

# Configure Kafka-Sieve
transforms.sieve.type=com.ibm.palantir.transforms.KafkaSieveTransform
transforms.sieve.ignore.columns=scan_date,kafka_send_date
transforms.sieve.default.action=PASS
```

### Configuration Options

| Option | Description | Default | Required |
|--------|-------------|---------|----------|
| `ignore.columns` | Comma-separated list of columns to ignore for change detection | `null` | No |
| `default.action` | Default action: `PASS` or `FILTER` | `PASS` | No |

## Example Use Case

When used with Debezium PostgreSQL connector, Kafka-Sieve can filter out changes that only affect timestamp columns:

```properties
name=postgres-source
connector.class=io.debezium.connector.postgresql.PostgresConnector
database.hostname=localhost
database.port=5432
database.user=postgres
database.password=postgres
database.dbname=discovery
topic.prefix=IBM-ZRDDS-SNOW
table.include.list=public.z_series_computer,public.sysplex
transforms=unwrap,sieve
transforms.unwrap.type=io.debezium.transforms.ExtractNewRecordState
transforms.sieve.type=com.ibm.palantir.transforms.KafkaSieveTransform
transforms.sieve.ignore.columns=scan_date,kafka_send_date
transforms.sieve.default.action=PASS
```

## Integration with Jenkins

The project includes a Jenkinsfile that:

- Builds the plugin with Java 21
- Runs tests and SonarQube analysis
- Creates a distribution package
- Uploads artifacts to Artifactory

## License

IBM Confidential
