################################################################################
# GENERAL NOTE:                                                                #
#                                                                              #
# - All password values in this configuration file must be base64-encoded.     #
#   You will find instructions for how to base64-encode a plain-text password  #
#   in the notes for each property containing a password value.                #
#   Should you have the need to use the password value outside of the control  #
#   of the Z Operational Analytics container-based applications, you must      #
#   base64-decode the password value found in this configuration file. A       #
#   possible application is the use of the ZAIOPS_ZOASVC_PASS value in         #
#   conjunction with the keytool utility.                                      #
#   To base64-decode a password value stored in this configuration file,       #
#   perform the following command:                                             #
#     echo <encoded_password> | base64 -d                                      #
#                                                                              #
################################################################################

################################################################################
# SETTINGS FOR CORE COMMON SERVICES                                            #
################################################################################

# General settings
#-------------------------------------------------------------------------------
# ZAIOPS_ZOASVC_PASS=<Password for the ZOA common services keystore and truststores>
#
# Description:
#   This value specifies the password for the keystore and truststores used for
#   the Z operational analytics common services. The password value must be base64-
#   encoded in this configuration file.
#
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   ZAIOPS_ZOASVC_PASS=c2VydmVycGFzcw==
#
# Notes:
#   To base64-encode the password, perform the following command:
#     echo -n <clear_text_password> | base64
#
#-------------------------------------------------------------------------------
ZAIOPS_ZOASVC_PASS=

#-------------------------------------------------------------------------------
# ZAIOPS_TLS_VERSION=<TLS version to use for secure connections to the common services>
#
# Description:
#   This value specifies the TLS version that should be used for externally accessible
#   service ports. The externally accessible TLS-enabled service ports are:
#   - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT
#   - IZOA_GATEWAY_PORT
#   - ZAIOPS_KEYCLOAK_PORT
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZAIOPS_TLS_VERSION=TLSv1.2
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_TLS_VERSION=TLSv1.2

#-------------------------------------------------------------------------------
# LOGGING_DRIVER=<OCI runtime logging driver to use>
#
# Description:
#   This value specifies the Docker logging driver to use for the Z operational 
#   analytics common services. The logging driver controls the logging behavior
#   of the service containers, such as the logging destination, log file rotation
#   and whether to use compression on archived log files.
#   Supported logging drivers are:
#   - journald (recommended if journald is in use on the Docker host)
#   - local (Docker only; recommended only if journald is not in use)
#   - k8s-file (Podman only)
#   - json-file (Always available, but generally not recommended as it provides
#     no log rotation or log compression capabilities)
#
# Required value? Yes
#
# Default value: json-file
#
# Example:
#   LOGGING_DRIVER=local
#
# Notes:
#   Other logging solutions, such as forwarding to a central syslog daemon or to
#   a log aggregator like Graylog or the Z Data Analytics Platform itself can
#   be implemented. However, they will require modifications of the 
#   *-docker-compose.yml files and should only be performed under the guidance
#   of IBM Support.
#
#-------------------------------------------------------------------------------
LOGGING_DRIVER=

#-------------------------------------------------------------------------------
# LOGGING_MODE=<Whether to perform OCI container logging in blocking or non-blocking mode>
#
# Description:
#   This value specifies the logging mode for the Z operational analytics common 
#   services. Available modes are:
#   - non-blocking: Optimizes container performance at the risk of potential 
#     log record loss.
#   - blocking: Optimizes log record retention at the risk of degrading container 
#     performance.
#
# Required value? Yes
#
# Default value: non-blocking
#
# Example:
#   LOGGING_MODE=non-blocking
#
# Notes:
#   This property applies to Docker-based deployments only
#
#-------------------------------------------------------------------------------
LOGGING_MODE=

# Gateway service settings
#-------------------------------------------------------------------------------
# IZOA_GATEWAY_PORT=<Access port of the Z Anomaly Analytics gateway service>
#
# Description:
#   This value specifies the port on which the Z Anomaly Analytics gateway
#   service listens. This value must be the same as the value of the 'gatewayport'
#   property in the Problem Insights server's cli.config file.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   IZOA_GATEWAY_PORT=8085
#
# Notes:
#
#-------------------------------------------------------------------------------
IZOA_GATEWAY_PORT=8085

#-------------------------------------------------------------------------------
# ZAIOPS_GATEWAY_REQUESTS_PER_MIN=<Number of requests per minute that a client can submit to the gateway service>
#
# Description:
#   This property specifies how many requests per minute a client can submit
#   to the Z Operational Analytics gateway service before the gateway's request
#   rate limit is enforced and further requests are rejected.
#   For ServiceNow CMDB integration use cases, set this property to the same
#   value as the rest.max_per_minute property in the Discovery Agent 
#   configuration file.
#   For Z Data Analytics Platform use cases, increase the value of this property
#   above the default if you work with multiple dashboards at the same time or 
#   utilize high refresh rates on your dashboards, and if you encounter errors 
#   loading dashboard visualizations as a result.
#
#   For other use cases, leave this property unset unless instructed otherwise
#   by IBM Support.
#
# Required value? No
#
# Default value: 80
#
# Example:
#   ZAIOPS_GATEWAY_REQUESTS_PER_MIN=200
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_GATEWAY_REQUESTS_PER_MIN=

# Authentication service settings
#-------------------------------------------------------------------------------
# ZAIOPS_KEYCLOAK_PORT=<HTTPS access port of the Keycloak authentication service>
#
# Description:
#   This value specifies the HTTPS port on which the Keycloak authentication 
#   service listens.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZAIOPS_KEYCLOAK_PORT=8443
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KEYCLOAK_PORT=8443

#-------------------------------------------------------------------------------
# ZAIOPS_KEYCLOAK_TIMEOUT=<Amount of time to allow Keycloak service to become stable>
#
# Description:
#   This value specifies the amount of time (in seconds) to allow the Keycloak
#   service to become stable. If the service does not become stable within the specified
#   time, it will fail.
#
# Required value? Yes
#
# Default value: 300
#
# Example:
#   ZAIOPS_KEYCLOAK_TIMEOUT=900
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KEYCLOAK_TIMEOUT=900

#-------------------------------------------------------------------------------
# ZAIOPS_KEYCLOAK_ADMIN_PASS=<Password for the authentication service administrative user ID>
#
# Description:
#   This value specifies the password for the administrative user ID for the authentication 
#   service. This password must be the base64-encoded value of the password set inside the 
#   'Master' realm of the authentication service.  Passwords for other users are not supported.
#
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   ZAIOPS_KEYCLOAK_ADMIN_PASS=Y2hhbmdlbWU=
#
# Notes:
#   To base64-encode the password, perform the following command:
#     echo -n <clear_text_password> | base64
#   Changing the password in this configuration file alone will not change it
#   in the 'Master' realm of the authentication service. Change the password from the 
#   web UI first, then change it in this configuration file.
#
#-------------------------------------------------------------------------------
ZAIOPS_KEYCLOAK_ADMIN_PASS=Y2hhbmdlbWU=

#-------------------------------------------------------------------------------
# ZAIOPS_ZOAREALM_ADMIN_PASS=<Password for the administrative user ID of the IzoaKeycloak realm>
#
# Description:
#   This value specifies the password for the administrative user ID for the IzoaKeycloak realm
#   in the authentication service. This password must be the base64-encoded value of the password 
#   set for the 'zoakcadmin' user ID inside the 'IzoaKeycloak' realm.  Passwords for other users 
#   are not supported.
#
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   ZAIOPS_ZOAREALM_ADMIN_PASS=Y2hhbmdlbWU=
#
# Notes:
#   To base64-encode the password, perform the following command:
#     echo -n <clear_text_password> | base64
#   Changing the password in this configuration file alone will not change it
#   in the 'IzoaKeycloak' realm of the authentication service. Change the password from the 
#   web UI first, then change it in this configuration file.
#
#-------------------------------------------------------------------------------
ZAIOPS_ZOAREALM_ADMIN_PASS=Y2hhbmdlbWU=

#-------------------------------------------------------------------------------
# ZAIOPS_KEYCLOAK_LOGLEVEL=<Logging level for the authentication service>
#
# Description:
#   This value specifies the logging level for the authentication service.
#   Supported values are:
#     - FATAL   Critical failures with complete inability to serve any kind of request.
#     - ERROR   A significant error or problem leading to the inability to process requests.
#     - WARN    A non-critical error or problem that might not require immediate correction.
#     - INFO    Lifecycle events or important information. Low frequency.
#     - DEBUG   More detailed information for debugging purposes, such as database logs. 
#               Higher frequency.
#     - TRACE   Most detailed debugging information. Very high frequency.
#     - ALL     Special level for all log messages.
#     - OFF     Special level to turn logging off entirely (not recommended).
#
# Required value? Yes
#
# Default value: INFO
#
# Example:
#   ZAIOPS_KEYCLOAK_LOGLEVEL=TRACE
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KEYCLOAK_LOGLEVEL=

#-------------------------------------------------------------------------------
# ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED=<Whether or not to enable the health API endpoint for the authentication service>
#
# Description:
#   This value determines whether the health API endpoint for the authentication 
#   service is enabled.
#   Supported values are 'true' and 'false'. If no value is specified, the 
#   authentication service setting defaults to 'false'.
#
# Required value? No
#
# Default value: false
#
# Example:
#   ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED=true
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED=

#-------------------------------------------------------------------------------
# ZAIOPS_KEYCLOAK_METRICAPI_ENABLED=<Whether or not to enable the metric API endpoint for the authentication service>
#
# Description:
#   This value determines whether the metric API endpoint for the authentication 
#   service is enabled.
#   Supported values are 'true' and 'false'. If no value is specified, the 
#   authentication service setting defaults to 'false'.
#
# Required value? No
#
# Default value: false
#
# Example:
#   ZAIOPS_KEYCLOAK_METRICAPI_ENABLED=true
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KEYCLOAK_METRICAPI_ENABLED=

# Apache Kafka service settings
#-------------------------------------------------------------------------------
# ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=<Hostname or IP address for the Z Anomaly Analytics Apache Kafka service>
#
# Description:
#   This value specifies the hostname (or IP address) of the system on which
#   the Z Anomaly Analytics OCI containers, incl. the Apache Kafka service,
#   are installed.
#
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=zaahost.myco.com
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=

#-------------------------------------------------------------------------------
# ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT=<Non-TLS port on which the Z Anomaly Analytics Apache Kafka service listens for producers and consumers>
#
# Description:
#   This value specifies the non-TLS port on which the Apache Kafka service 
#   embedded in Z Anomaly Analytics listens for requests from data producers and
#   data consumers.
#
# Required value? yes
#
# Default value: This property has no default value
#
# Example:
#   ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT=9092
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT=9092

#-------------------------------------------------------------------------------
# ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT=<TLS port on which the Z Anomaly Analytics Apache Kafka service listens for producers and consumers>
#
# Description:
#   This value specifies the TLS port on which the Apache Kafka service 
#   embedded in Z Anomaly Analytics listens for requests from data producers and
#   data consumers.
#
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT=9093
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT=9093

#-------------------------------------------------------------------------------
# ZAIOPS_KAFKA_RETENTION_HOURS=<Retention period, in hours, for data stored in a Kafka topic>
#
# Description:
#   This value specifies the default retention period for data in any newly 
#   created Kafka topic. The retention period is the amount of time before data
#   in a topic becomes eligible for deletion.
#   This value is used for any topic that is generated without a specific topic-
#   level retention value. The retention period for a topic can be reconfigured
#   after initial creation if required; see the dockerManageZoa.sh kafka-prune
#   command for more details.
#
# Required value? Yes
#
# Default value: 24
#
# Example:
#   ZAIOPS_KAFKA_RETENTION_HOURS=24
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KAFKA_RETENTION_HOURS=24

#-------------------------------------------------------------------------------
# ZAIOPS_KAFKA_HEAP=<Kafka broker heap size in GB>
#
# Description:
#   The heap memory size to allocate to the Z Data Analytics Platform Kafka
#   broker process.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZAIOPS_KAFKA_HEAP=2
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_KAFKA_HEAP=1

# Settings for generation of TLS artifacts
#-------------------------------------------------------------------------------
# CRYPT_ALGO=<Cryptographic algorithm to use for encryption of private key>
#
# Description:
#   This value specifies the cryptographic algorithm to use for encrypting the 
#   private key generated by the 'config-certificates' command. Supported options 
#   are 'EC' (for Elliptic Curve) and 'RSA' (for Rivest-Shamir-Adleman).
#
# Required value? Yes
#
# Default value: EC
#
# Example:
#   CRYPT_ALGO=RSA
#
# Notes:
#
#-------------------------------------------------------------------------------
CRYPT_ALGO=

#-------------------------------------------------------------------------------
# COUNTRY=<Country to use when generating certificate signing request>
#
# Description:
#   This value specifies the country to use when generating the certificate signing 
#   request. It corresponds to the 'C' parameter of a certificate's subject.
#
# Required value? No
#
# Default value: This property has no default value
#
# Example:
#   COUNTRY=DE
#
# Notes:
#   Spaces are not supported in the value of this variable
#
#-------------------------------------------------------------------------------
COUNTRY=

#-------------------------------------------------------------------------------
# STATE=<State to use when generating certificate signing request>
#
# Description:
#   This value specifies the state to use when generating the certificate signing 
#   request. It corresponds to the 'ST' parameter of a certificate's subject.
#
# Required value? No
#
# Default value: This property has no default value
#
# Example:
#   STATE=Bayern
#
# Notes:
#   Spaces are not supported in the value of this variable
#
#-------------------------------------------------------------------------------
STATE=

#-------------------------------------------------------------------------------
# LOCATION=<Location to use when generating certificate signing request>
#
# Description:
#   This value specifies the location to use when generating the certificate signing 
#   request. It corresponds to the 'L' parameter of a certificate's subject.
#
# Required value? No
#
# Default value: This property has no default value
#
# Example:
#   LOCATION=Passau
#
# Notes:
#   Spaces are not supported in the value of this variable
#
#-------------------------------------------------------------------------------
LOCATION=

#-------------------------------------------------------------------------------
# ORG_NAME=<Organization name to use when generating certificate signing request>
#
# Description:
#   This value specifies the organization name to use when generating the certificate 
#   signing request. It corresponds to the 'O' parameter of a certificate's subject.
#
# Required value? No
#
# Default value: This property has no default value
#
# Example:
#   ORG_NAME=IBM
#
# Notes:
#   Spaces are not supported in the value of this variable
#
#-------------------------------------------------------------------------------
ORG_NAME=

#-------------------------------------------------------------------------------
# ORG_UNIT=<Organizational unit to use when generating certificate signing request>
#
# Description:
#   This value specifies the organizational unit to use when generating the certificate 
#   signing request. It corresponds to the 'OU' parameter of a certificate's subject.
#
# Required value? No
#
# Default value: This property has no default value
#
# Example:
#   ORG_UNIT=IBMSoftware
#
# Notes:
#   Spaces are not supported in the value of this variable
#
#-------------------------------------------------------------------------------
ORG_UNIT=
