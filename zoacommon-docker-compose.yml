services:
  datastore:
    image: icr.io/zoa-oci/zoa-datastore:${ZOACOMMON_TAG}-x86_64
    # TODO: Remove the 'ports' section before shipping this code
    # ports:
    #   - ${ZAIOPS_DATASTORE_PORT}:9200
    container_name: zoa-datastore
    hostname: datastore
    user: "${ZOA_UID}:0"
    environment:
      - DATASTORE_HEAP=${ZAIOPS_DATASTORE_HEAP}
      - EXTERNAL_GATEWAY_HOST=${EXTERNAL_GATEWAY_HOST}
      - IZOA_GATEWAY_PORT=${IZOA_GATEWAY_PORT}
      - ZAIOPS_GW_PASS=${ZAIOPS_ZOASVC_PASS}
      - ZAIOPS_KC_CONTEXT_ROOT=${ZAIOPS_KC_CONTEXT_ROOT}
      - discovery.type=single-node
    volumes:
      - zaiops_datastore:/usr/share/opensearch/data
      - zaiops_datastore_misc:/usr/share/os_misc
      - zaiops_datastore_snapshots:/usr/share/os_snapshots
      - zaiops_shared:/shared:ro
    restart: always
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks: 
      - zaiops
  piserver:
    image: icr.io/zoa-oci/zoa-piserver:${ZOACOMMON_TAG}-x86_64
    # TODO: Remove the 'ports' section before shipping this code
    # ports:
    #   - ${PISERVER_PORT}:9446
    env_file:
      - ./zoa_env.config
      - ./.zoa_factory.config
    environment:
      - SSL_DEBUG=${SSL_DEBUG}
      - PI_SSL_ENABLED=${PI_SSL_ENABLED}
      - WLP_LOGGING_CONSOLE_SOURCE=message,trace,accessLog,ffdc
      - WLP_LOGGING_CONSOLE_FORMAT=${PI_CONSOLE_LOG_FORMAT:-SIMPLE}
      - WLP_LOGGING_CONSOLE_LOGLEVEL=${PI_CONSOLE_LOG_LEVEL:-INFO}
      - WLP_LOGGING_MESSAGE_FORMAT=json
      - WLP_LOGGING_MESSAGE_SOURCE=
      - ENSEMBLE_ENABLED=${ENSEMBLE_ENABLED:-false}
      - ZAIOPS_KC_CONTEXT_ROOT=${ZAIOPS_KC_CONTEXT_ROOT}
    restart: always
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    hostname: piserver
    user: "${ZOA_UID}:0"
    container_name: zoa-piserver
    networks:
      - zaiops
    volumes:
      - zaiops_shared:/shared:ro
      - zaiops_piserver_db:/opt/ibm/piserver/wlp/usr/servers/piFrameworkServer/derby
      - zaiops_piserver_usr:/opt/ibm/piserver/usr
      - zaiops_piserver_ip:/opt/ibm/piserver/wlp/usr/servers/piFrameworkServer/insightpacks
      - zaiops_piserver_staging:/opt/ibm/staging

volumes:
  zaiops_datastore:
  zaiops_datastore_misc:
  zaiops_datastore_snapshots:
  zaiops_piserver_db:
  zaiops_piserver_usr:
  zaiops_piserver_ip:
  zaiops_piserver_staging:
  zaiops_shared:
    external: true

networks:
  zaiops:
    driver: bridge
