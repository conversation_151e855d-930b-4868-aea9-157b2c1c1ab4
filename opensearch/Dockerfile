# © Copyright IBM Corporation 2017, 2021.
# LICENSE: Apache License, Version 2.0 (http://www.apache.org/licenses/LICENSE-2.0)

##################################### Dockerfile for OpenSearch version 1.3 ########################################
#
# This Dockerfile builds a basic installation of OpenSearch.
#
# OpenSearch is a search server based on Lucene. It provides a distributed, multitenant-capable
# full-text search engine with an HTTP web interface and schema-free JSON documents.
#
# The vm_max_map_count kernel setting needs to be set to at least 262144 for production use using the below command.(See "https://github.com/docker-library/elasticsearch/issues/111")
# sysctl -w vm.max_map_count=262144
#
# To build this image, from the directory containing this Dockerfile
# (assuming that the file is named Dockerfile):
# docker build -t <image_name> .
#
# Start OpenSearch container using the below command
# docker run --name <container_name> -p 9200:9200 -d <image_name>
#
# Start OpenSearch with configuration file
# For ex. docker run --name <container_name> -v <path_on_host>/opensearch.yml:/usr/share/opensearch/config/opensearch.yml -p <port>:9200 -d <image_name>
#
##############################################################################################################
################################################################################
# Build stage 0 `builder`:
# Extract OpenSearch artifact
# Set gid=0 and make group perms==owner perms
################################################################################

FROM icr.io/zoa-oci/x86_64/ubuntu:20.04 AS builder

ARG OS_VER=see_.buildenv
ARG ARCH=amd64
ARG DEBIAN_FRONTEND=noninteractive
ARG FORKBRANCH=develop
ARG FALLBACKBRANCH=develop
ARG BUILDER=TBD
ARG TOKEN=TBD

ENV LANG="en_US.UTF-8"
ENV SOURCE_DIR="/tmp/"
ENV PATH=$JAVA_HOME/bin:$PATH
ENV PATCH_PATH="${OS_VER}/patch"
ENV TZ=Etc/UTC

RUN apt-get update && apt-get install -y \
    curl \
    git \
    gzip \
    maven \
    tar \
    openjdk-11-jdk

ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-${ARCH}

# `tini` is a tiny but valid init for containers. This is used to cleanly
# control how OpenSearch and any child processes are shut down.
#
# The tini GitHub page gives instructions for verifying the binary using
# gpg, but the keyservers are slow to return the key and this can fail the
# build. Instead, we check the binary against the published checksum.
RUN set -eux ; \
    \
    tini_bin="" ; \
    case "$(arch)" in \
        aarch64) tini_bin='tini-arm64' ;; \
        x86_64)  tini_bin='tini-amd64' ;; \
        s390x)   tini_bin='tini-s390x' ;; \
        *) echo >&2 ; echo >&2 "Unsupported architecture $(arch)" ; echo >&2 ; exit 1 ;; \
    esac ; \
    curl --retry 8 -S -L -O https://github.com/krallin/tini/releases/download/v0.19.0/${tini_bin} ; \
    curl --retry 8 -S -L -O https://github.com/krallin/tini/releases/download/v0.19.0/${tini_bin}.sha256sum ; \
    sha256sum -c ${tini_bin}.sha256sum ; \
    rm ${tini_bin}.sha256sum ; \
    mv ${tini_bin} /tini ; \
    chmod +x /tini

ENV PATH /usr/share/opensearch/bin:$PATH

RUN /usr/sbin/groupadd -g 1000 opensearch && \
    /usr/sbin/useradd --uid 1000 --gid 1000 -d /usr/share/opensearch opensearch

WORKDIR /usr/share/opensearch

COPY zoa-common-docker-MF/opensearch/${OS_VER} ./${OS_VER}

# Set up locale
RUN apt-get install -y locales && rm -rf /var/lib/apt/lists/* \
 && localedef -i en_US -c -f UTF-8 -A /usr/share/locale/locale.alias en_US.UTF-8 \
 && PASSKEY=$( echo ${TOKEN} | base64 -d ) \
 # Download and build OpenSearch \
 && THIS_DIR=${PWD} \
 && mvn install:install-file -q -Dfile=${OS_VER}/common-utils-${OS_VER}.0.jar -DgroupId=org.opensearch -DartifactId=common-utils -Dversion=${OS_VER}.0 -Dpackaging=jar \
 && cd ${SOURCE_DIR} \
 && RESULT=$( git ls-remote --heads "https://${BUILDER}:${PASSKEY}@github.ibm.com/ZLDAforks/opensearch-OpenSearch" ${OS_VER}z-${FORKBRANCH} | awk '{ print $2 }' )  \
 && if [ -z ${RESULT} ] ; then PULLBRANCH=${FALLBACKBRANCH} ; else PULLBRANCH=${FORKBRANCH} ; fi \
 && git clone "https://${BUILDER}:${PASSKEY}@github.ibm.com/ZLDAforks/opensearch-OpenSearch.git" \
 && cd opensearch-OpenSearch \
 && git checkout ${OS_VER}z-${PULLBRANCH} \
 && ./gradlew -Dbuild.snapshot=false :distribution:archives:linux-tar:assemble --parallel \
 && ./gradlew -Dbuild.snapshot=false :distribution:docker:docker-build-context:assemble \
 && mkdir -p /usr/share/opensearch/config/tls \
 && ls -al distribution/archives/linux-tar/build/distributions/ \
 && tar -xzf distribution/archives/linux-tar/build/distributions/opensearch-min-${OS_VER}-linux-x64.tar.gz -C /usr/share/opensearch --strip-components 1 \
 && rm -rf /usr/share/opensearch/jdk \
 && mkdir -p /usr/share/opensearch/resources \
 && mv ${THIS_DIR}/${OS_VER}/opensearch-*-${OS_VER}.0.zip /usr/share/opensearch/resources \
 && rm -Rf ${THIS_DIR}/${OS_VER} \
 && cd /usr/share/opensearch \
 && ./bin/opensearch-plugin install -b file:///usr/share/opensearch/resources/opensearch-job-scheduler-${OS_VER}.0.zip \
 && rm -f /usr/share/opensearch/resources/opensearch-job-scheduler-${OS_VER}.0.zip \
 && ./bin/opensearch-plugin install -b file:///usr/share/opensearch/resources/opensearch-index-management-${OS_VER}.0.zip \
 && rm -f /usr/share/opensearch/resources/opensearch-index-management-${OS_VER}.0.zip \
 && ./bin/opensearch-plugin install -b file:///usr/share/opensearch/resources/opensearch-reports-scheduler-${OS_VER}.0.zip \
 && rm -f /usr/share/opensearch/resources/opensearch-reports-scheduler-${OS_VER}.0.zip \
 && ./bin/opensearch-plugin install -b file:///usr/share/opensearch/resources/opensearch-notifications-core-${OS_VER}.0.zip \
 && rm -f /usr/share/opensearch/resources/opensearch-notifications-core-${OS_VER}.0.zip \
 && ./bin/opensearch-plugin install -b file:///usr/share/opensearch/resources/opensearch-notifications-${OS_VER}.0.zip \
 && rm -f /usr/share/opensearch/resources/opensearch-notifications-${OS_VER}.0.zip \
 && ./bin/opensearch-plugin install -b file:///usr/share/opensearch/resources/opensearch-alerting-${OS_VER}.0.zip \
 && rm -f /usr/share/opensearch/resources/opensearch-alerting-${OS_VER}.0.zip \
 && ./bin/opensearch-plugin install -b file:///usr/share/opensearch/resources/opensearch-security-${OS_VER}.0.zip \
 && rm -f /usr/share/opensearch/resources/opensearch-security-${OS_VER}.0.zip \
 && chmod 755 /usr/share/opensearch/plugins/opensearch-security/tools/*.sh

RUN sed -i -e 's/OPENSEARCH_DISTRIBUTION_TYPE=tar/OPENSEARCH_DISTRIBUTION_TYPE=docker/' /usr/share/opensearch/bin/opensearch-env
RUN mkdir -p config config/jvm.options.d data logs
RUN chmod 0775 config config/jvm.options.d data logs
COPY zoa-common-docker-MF/opensearch/config/opensearch.yml zoa-common-docker-MF/opensearch/config/jvm.options zoa-common-docker-MF/opensearch/config/log4j2.properties config/
COPY zoa-common-docker-MF/opensearch/config/security-config/* config/opensearch-security/
RUN chmod 0660 config/opensearch.yml config/jvm.options config/log4j2.properties

################################################################################
# Build stage 1 (the actual OpenSearch image):
# Copy OpenSearch from stage 0
# Add entrypoint
################################################################################

FROM icr.io/zoa-oci/zoacommon-jre21-micro:21.0.5_11-x86_64

ARG OS_VER=see_.buildenv
ARG REPO=unknown
ARG COMMIT=unknown
ARG VRMF=unknown

LABEL feature="IBM Z AIOps - Common Services - Z Operational Analytics"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

ENV PATH=$JAVA_HOME/bin:$PATH
ENV OPENSEARCH_CONTAINER true

COPY --from=builder /tini /tini

WORKDIR /usr/share/opensearch
COPY --from=builder /usr/share/opensearch /usr/share/opensearch
COPY zoa-common-docker-MF/opensearch/bin/docker-entrypoint.sh zoa-common-docker-MF/opensearch/bin/healthcheck.sh /usr/local/bin/
ENV PATH /usr/share/opensearch/bin:$PATH

RUN mkdir /usr/share/os_snapshots /usr/share/os_misc && \
    chgrp -R 0 /usr/share/opensearch /usr/share/os_snapshots /usr/share/os_misc && \
    chmod -R g=u /usr/share/opensearch /usr/share/os_snapshots /usr/share/os_misc && \
# Replace OpenJDK's built-in CA certificate keystore with the one from the OS \
# vendor. The latter is superior in several ways. \
# REF: https://github.com/elastic/elasticsearch-docker/issues/171 \
    ln -sf /etc/pki/ca-trust/extracted/java/cacerts /opt/java/openjdk/lib/security/cacerts && \
    chmod 0775 /usr/local/bin/docker-entrypoint.sh /usr/local/bin/healthcheck.sh && \
# Ensure that there are no files with setuid or setgid, in order to mitigate "stackclash" attacks. \
    find / -xdev -perm -4000 -exec chmod ug-s {} +

#EXPOSE 9200 9300

USER opensearch

HEALTHCHECK NONE
# HEALTHCHECK CMD /usr/local/bin/healthcheck.sh

ENTRYPOINT ["/tini", "--", "/usr/local/bin/docker-entrypoint.sh"]
# Dummy overridable parameter parsed by entrypoint
CMD ["opensearchwrapper"]

################################################################################
# End of multi-stage Dockerfile
################################################################################
