#!/bin/bash
set -e

# Files created by Elasticsearch should always be group writable too
umask 0002

run_as_other_user_if_needed() {
  if [[ "$(id -u)" == "0" ]]; then
    # If running as root, drop to specified UID and run command
    exec chroot --userspec=1000:0 / "${@}"
  else
    # Either we are running in Openshift with random uid and are a member of the root group
    # or with a custom --user
    exec "${@}"
  fi
}

export GW_PASS=`echo "${ZAIOPS_GW_PASS}" | base64 -d`
SSL_DEBUG_PARM=""

if [ "${SUBDOMAIN}x" != "x" ]
then
  AUTH_HOST=auth.${SUBDOMAIN}
else
  AUTH_HOST=auth
fi

# Prepare TLS artifacts
if [ -f /shared/config/zoasvc.tls ]
then
  ORIGIN=`pwd`
  mkdir -p /usr/share/opensearch/config/tls && rm -f /usr/share/opensearch/config/tls/*
  cd /usr/share/opensearch/config/tls
  cat /shared/config/zoasvc.tls | base64 -d | tar xz
  chmod 600 *.*
  cd ${ORIGIN}
else
  echo "ERROR: TLS artifacts not found."
  exit 1
fi

# Restore opensearch.keystore from OCI volume
if [ -f /usr/share/os_misc/opensearch.keystore ]
then
  rm -f /usr/share/opensearch/config/opensearch.keystore
  cp /usr/share/os_misc/opensearch.keystore /usr/share/opensearch/config/
fi

# Restore internal_users.yml from OCI volume
if [ -f /usr/share/os_misc/internal_users.yml ]
then
  rm -f /usr/share/opensearch/config/opensearch-security/internal_users.yml
  cp /usr/share/os_misc/internal_users.yml /usr/share/opensearch/config/opensearch-security/
  chmod 600 /usr/share/opensearch/config/opensearch-security/internal_users.yml
fi

if [ "$( echo ${SSL_DEBUG} | tr [:lower:] [:upper:] )" = "TRUE" ]
then
    export SSL_DEBUG_PARM="-Djavax.net.debug=ssl"
fi

# Append SSL_DEBUG_PARM to OPENSEARCH_JAVA_OPTS
sed -i -re "s%^(OPENSEARCH_JAVA_OPTS.*)$%\1\nOPENSEARCH_JAVA_OPTS=\"\${OPENSEARCH_JAVA_OPTS}\ \${SSL_DEBUG_PARM}\"%g" /usr/share/opensearch/bin/opensearch
# Set key password in opensearch.yml file
sed -i -e "s%keystore_password:.*$%keystore_password:\ ${GW_PASS}%g" /usr/share/opensearch/config/opensearch.yml
sed -i -e "s%truststore_password:.*$%truststore_password:\ ${GW_PASS}%g" /usr/share/opensearch/config/opensearch.yml
sed -i -e "s%ext_link_host:.*$%ext_link_host:\ ${EXTERNAL_GATEWAY_HOST}%g" /usr/share/opensearch/config/opensearch.yml
sed -i -e "s%ext_link_port:.*$%ext_link_port:\ ${IZOA_GATEWAY_PORT}%g" /usr/share/opensearch/config/opensearch.yml
sed -i -e "s%__AUTH_HOST__%${AUTH_HOST}%g" /usr/share/opensearch/config/opensearch-security/config.yml
sed -i -e "s%__AUTH_ROOT__%${ZAIOPS_KC_CONTEXT_ROOT}%g" /usr/share/opensearch/config/opensearch-security/config.yml

# Allow user specify custom CMD, maybe bin/opensearch itself
# for example to directly specify `-E` style parameters for opensearch on k8s
# or simply to run /bin/bash to check the image
if [[ "$1" != "opensearchwrapper" ]]; then
  if [[ "$(id -u)" == "0" && $(basename "$1") == "opensearch" ]]; then
    # centos:7 chroot doesn't have the `--skip-chdir` option and
    # changes our CWD.
    # Rewrite CMD args to replace $1 with `opensearch` explicitly,
    # so that we are backwards compatible with the docs
    # from the previous Elasticsearch versions<6
    # and configuration option D:
    # https://www.elastic.co/guide/en/elasticsearch/reference/5.6/docker.html#_d_override_the_image_8217_s_default_ulink_url_https_docs_docker_com_engine_reference_run_cmd_default_command_or_options_cmd_ulink
    # Without this, user could specify `opensearch -E x.y=z` but
    # `bin/opensearch -E x.y=z` would not work.
    set -- "opensearch" "${@:2}"
    # Use chroot to switch to UID 1000 / GID 0
    exec chroot --userspec=1000:0 / "$@"
  else
    # User probably wants to run something else, like /bin/bash, with another uid forced (Openshift?)
    exec "$@"
  fi
fi

# Allow environment variables to be set by creating a file with the
# contents, and setting an environment variable with the suffix _FILE to
# point to it. This can be used to provide secrets to a container, without
# the values being specified explicitly when running the container.
#
# This is also sourced in opensearch-env, and is only needed here
# as well because we use ELASTIC_PASSWORD below. Sourcing this script
# is idempotent.
source /usr/share/opensearch/bin/opensearch-env-from-file

if [[ -f bin/opensearch-users ]]; then
  # Check for the ELASTIC_PASSWORD environment variable to set the
  # bootstrap password for Security.
  #
  # This is only required for the first node in a cluster with Security
  # enabled, but we have no way of knowing which node we are yet. We'll just
  # honor the variable if it's present.
  if [[ -n "$ELASTIC_PASSWORD" ]]; then
    [[ -f /usr/share/opensearch/config/opensearch.keystore ]] || (run_as_other_user_if_needed opensearch-keystore create)
    if ! (run_as_other_user_if_needed opensearch-keystore has-passwd --silent) ; then
      # keystore is unencrypted
      if ! (run_as_other_user_if_needed opensearch-keystore list | grep -q '^bootstrap.password$'); then
        (run_as_other_user_if_needed echo "$ELASTIC_PASSWORD" | opensearch-keystore add -x 'bootstrap.password')
      fi
    else
      # keystore requires password
      if ! (run_as_other_user_if_needed echo "$KEYSTORE_PASSWORD" \
          | opensearch-keystore list | grep -q '^bootstrap.password$') ; then
        COMMANDS="$(printf "%s\n%s" "$KEYSTORE_PASSWORD" "$ELASTIC_PASSWORD")"
        (run_as_other_user_if_needed echo "$COMMANDS" | opensearch-keystore add -x 'bootstrap.password')
      fi
    fi
  fi
fi

if [[ "$(id -u)" == "0" ]]; then
  # If requested and running as root, mutate the ownership of bind-mounts
  if [[ -n "$TAKE_FILE_OWNERSHIP" ]]; then
    chown -R 1000:0 /usr/share/opensearch/{data,logs}
  fi
fi

# Apply jvm.options settings
HALF_DATASTORE_HEAP=$(( ${DATASTORE_HEAP} / 2 ))
sed -i -e "s%^-Xms.*$%-Xms${DATASTORE_HEAP}g%g" /usr/share/opensearch/config/jvm.options
sed -i -e "s%^-Xmx.*$%-Xmx${DATASTORE_HEAP}g%g" /usr/share/opensearch/config/jvm.options
sed -i -e "s%^-XX:MaxDirectMemorySize=.*$%-XX:MaxDirectMemorySize=${HALF_DATASTORE_HEAP}g%g" /usr/share/opensearch/config/jvm.options

# OpenSearch suppresses JAVA_TOOL_OPTIONS -- at least in some contexts -- but honors OPENSEARCH_JAVA_OPTS
# Copy the Java options we normally use from JAVA_TOOL_OPTIONS to OPENSEARCH_JAVA_OPTS
export OPENSEARCH_JAVA_OPTS="${JAVA_TOOL_OPTIONS}" && unset JAVA_TOOL_OPTIONS

run_as_other_user_if_needed /usr/share/opensearch/bin/opensearch <<<"$KEYSTORE_PASSWORD"
