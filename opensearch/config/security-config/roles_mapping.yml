---
# In this file users, backendroles and hosts can be mapped to Security roles.
# Permissions for OpenSearch roles are configured in roles.yml

_meta:
  type: "rolesmapping"
  config_version: 2

# Define your roles mapping here

## Demo roles mapping

all_access:
  reserved: false
  backend_roles:
    - "zdapsuper"
  description: "Maps zdapsuper to all_access"

own_index:
  reserved: false
  backend_roles:
    - "zdapbaseuser"
    - "zdapadvuser"
    - "zdapadmin"
    - "zdapsuper"
  description: "Allow full access to an index named like the username"

zdapui_user:
  reserved: false
  backend_roles:
    - "zdapuiuser"

readall:
  reserved: false
  backend_roles:
    - "readall"

index_management_reader:
  reserved: false
  backend_roles:
    - "zdapadmin"

manage_snapshots:
  reserved: false
  backend_roles:
    - "snapshotrestore"

zdapui_server:
  reserved: true
  users:
    - "zdapui"

zdapui_read_only:
  reserved: true
  backend_roles:
    - "zdapbaseuser"

zdapui_advuser:
  reserved: true
  backend_roles:
    - "zdapadvuser"

zdapui_admin:
  reserved: true
  backend_roles:
    - "zdapadmin"

super_admin:
  reserved: false
  and_backend_roles:
    - "tenant-super"
    - "zdapsuper"

super_advuser:
  reserved: false
  and_backend_roles:
    - "tenant-super"
    - "zdapadvuser"

super_baseuser:
  reserved: false
  and_backend_roles:
    - "tenant-super"
    - "zdapbaseuser"
