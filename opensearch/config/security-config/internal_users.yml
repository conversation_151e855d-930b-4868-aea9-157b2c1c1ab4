---
# This is the internal user database
# The hash value is a bcrypt hash and can be generated with plugin/tools/hash.sh

_meta:
  type: "internalusers"
  config_version: 2

# Define your internal users here

## Demo users

admin:
  hash: "$2y$12$yEYFNk1.yWRnQ3AJpk.4I.RqXY5PtzLH86i04lKeXmq1aOSRKRC7O"
  reserved: true
  backend_roles:
  - "zdapsuper"
  description: "ZDAP datastore administrative user"

zdapui:
  hash: "$2y$12$7Ss/DuSL./jFwBPi3jizcOpdj0uvG4ttb9Ox/iMULDN4XcYRTH/E6"
  reserved: true
  description: "ZDAP UI server user"
