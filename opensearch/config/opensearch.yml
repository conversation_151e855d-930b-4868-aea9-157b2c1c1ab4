bootstrap:
  system_call_filter: "false"
cluster:
  name: "docker-cluster"
discovery:
  seed_hosts : []
network:
  host: 0.0.0.0
node:
  max_local_storage_nodes: 3
path:
  repo: ["/usr/share/os_snapshots"]
plugins:
  security:
    ssl:
      transport:
        keystore_type: PKCS12
        keystore_filepath: tls/datastore.ks
        keystore_alias: datastore
        keystore_password: __KEYPASS__
        truststore_type: PKCS12
        truststore_filepath: tls/datastore.ts
        truststore_password: __KEYPASS__
        enforce_hostname_verification: false
      http:
        enabled: true
        keystore_type: PKCS12
        keystore_filepath: tls/datastore.ks
        keystore_alias: datastore
        keystore_password: __KEYPASS__
        truststore_type: PKCS12
        truststore_filepath: tls/datastore.ts
        truststore_password: __KEYPASS__
    allow_unsafe_democertificates: true
    allow_default_init_securityindex: true
    authcz:
      admin_dn:
        - CN=secadmin,OU=zoa,O=zoa,L=internal
    audit.type: internal_opensearch
    enable_snapshot_restore_privilege: true
    check_snapshot_restore_write_privileges: true
    restapi:
      roles_enabled: ["all_access", "security_rest_api_access"]
    system_indices:
      enabled: true
      indices: [".opendistro-alerting-config", ".opendistro-alerting-alert*", ".opendistro-reports-*", ".opensearch-notifications-*", ".opensearch-notebooks", ".ql-datasources", ".opendistro-asynchronous-search-response*", ".replication-metadata-store", ".opensearch-knn-models", ".geospatial-ip2geo-data*"]
gateway:
  ext_link_host: __GATEWAY_HOST__
  ext_link_port: __GATEWAY_PORT__
script:
  context:
    filter:
      cache_max_size: 300
      max_compilations_rate: "900/1m"
