{"exclude": {"files": "^.secrets.baseline$", "lines": null}, "generated_at": "2024-08-08T02:27:50Z", "plugins_used": [{"name": "AWSKeyDetector"}, {"name": "ArtifactoryDetector"}, {"name": "AzureStorageKeyDetector"}, {"base64_limit": 4.5, "name": "Base64HighEntropyString"}, {"name": "BasicAuthDetector"}, {"name": "BoxDetector"}, {"name": "CloudantDetector"}, {"ghe_instance": "github.ibm.com", "name": "GheDetector"}, {"name": "GitHubTokenDetector"}, {"hex_limit": 3, "name": "HexHighEntropyString"}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "JwtTokenDetector"}, {"keyword_exclude": null, "name": "KeywordDetector"}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "PrivateKeyDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TwilioKeyDetector"}], "results": {"Dockerfile": [{"hashed_secret": "afc848c316af1a89d49826c5ae9d00ed769415f3", "is_secret": false, "is_verified": false, "line_number": 22, "type": "Secret Keyword", "verified_result": null}], "config/application.properties": [{"hashed_secret": "aa6d09434914553dad7400e258b60d72b39765de", "is_secret": false, "is_verified": false, "line_number": 13, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "4028a0e356acc947fcd2bfbf00cef11e128d484a", "is_secret": false, "is_verified": false, "line_number": 41, "type": "Hex High Entropy String", "verified_result": null}, {"hashed_secret": "afc848c316af1a89d49826c5ae9d00ed769415f3", "is_secret": false, "is_verified": false, "line_number": 65, "type": "Secret Keyword", "verified_result": null}], "docker/docker-entrypoint.sh": [{"hashed_secret": "05be325f77bba3fb3fad73f1d838d835ef00e503", "is_secret": false, "is_verified": false, "line_number": 14, "type": "Secret Keyword", "verified_result": null}], "src/main/java/com/ibm/zds/ext/api/service/KeyCloakService.java": [{"hashed_secret": "8b142a91cfb6e617618ad437cedf74a6745f8926", "is_secret": false, "is_verified": false, "line_number": 34, "type": "Secret Keyword", "verified_result": null}], "src/main/java/com/ibm/zds/ext/api/utils/HttpUtils.java": [{"hashed_secret": "fe010f73fb8ad3cad0718f4f4e39fa83024b1310", "is_secret": false, "is_verified": false, "line_number": 401, "type": "Base64 High Entropy String", "verified_result": null}]}, "version": "0.13.1+ibm.62.dss", "word_list": {"file": null, "hash": null}}