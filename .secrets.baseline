{"exclude": {"files": "package*.json|^.secrets.baseline$", "lines": null}, "generated_at": "2025-05-05T13:54:59Z", "plugins_used": [{"name": "AWSKeyDetector"}, {"name": "ArtifactoryDetector"}, {"name": "AzureStorageKeyDetector"}, {"base64_limit": 4.5, "name": "Base64HighEntropyString"}, {"name": "BasicAuthDetector"}, {"name": "BoxDetector"}, {"name": "CloudantDetector"}, {"ghe_instance": "github.ibm.com", "name": "GheDetector"}, {"name": "GitHubTokenDetector"}, {"hex_limit": 3, "name": "HexHighEntropyString"}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "JwtTokenDetector"}, {"keyword_exclude": null, "name": "KeywordDetector"}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "PrivateKeyDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TwilioKeyDetector"}], "results": {".zoa_factory.config.core": [{"hashed_secret": "c605efae2e1e0da2182277206214a6abcf421396", "is_secret": false, "is_verified": false, "line_number": 44, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "e8f3b4fd34d02aeaf3e8ccc41dfe7bfa2794dc35", "is_secret": false, "is_verified": false, "line_number": 69, "type": "Secret Keyword", "verified_result": null}], "devsrc/ibm-kafka-connect-elastic-sink-master/quickstart/docker-compose.yml": [{"hashed_secret": "7c6a61c68ef8b9b6b061b28c348bc1ed7921cb53", "is_secret": false, "is_verified": false, "line_number": 8, "type": "Secret Keyword", "verified_result": null}], "devsrc/ibm-kafka-connect-elastic-sink-master/src/main/java/com/ibm/eventstreams/connect/elasticsink/ElasticSinkConnector.java": [{"hashed_secret": "9f22a8c340af3a11ca31ecf9bd83d8988fda786c", "is_secret": false, "is_verified": false, "line_number": 49, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "11ba4c18958be10ea5962d58fff5647a1be24b3f", "is_secret": false, "is_verified": false, "line_number": 57, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "cf7d283ff63bfb4996097e4ffc9bd00295270daf", "is_secret": false, "is_verified": false, "line_number": 65, "type": "Secret Keyword", "verified_result": null}], "devutils/samples/.zoalocal": [{"hashed_secret": "d3ac7a4ef1a838b4134f2f6e7f3c0d249d74b674", "is_secret": false, "is_verified": false, "line_number": 14, "type": "Secret Keyword", "verified_result": null}], "devutils/setup-local-env.sh": [{"hashed_secret": "251e76217e69e2990a4d7c2ff22e87b96d67bd65", "is_secret": false, "is_verified": false, "line_number": 136, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "18efe9027e894ff9cb59ce50989abf0b2f4fa7c8", "is_secret": false, "is_verified": false, "line_number": 175, "type": "Secret Keyword", "verified_result": null}], "kafka/kafkabroker/config/broker.properties": [{"hashed_secret": "cd2234c6b7755b8dd230bdbc84544c3838e48904", "is_secret": false, "is_verified": false, "line_number": 64, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "d033e22ae348aeb5660fc2140aec35850c4da997", "is_secret": true, "is_verified": false, "line_number": 75, "type": "Secret Keyword", "verified_result": null}], "kafka/kafkabroker/config/controller.properties": [{"hashed_secret": "cd2234c6b7755b8dd230bdbc84544c3838e48904", "is_secret": false, "is_verified": false, "line_number": 57, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "d033e22ae348aeb5660fc2140aec35850c4da997", "is_secret": true, "is_verified": false, "line_number": 68, "type": "Secret Keyword", "verified_result": null}], "opensearch/bin/docker-entrypoint.sh": [{"hashed_secret": "14f25d4c0f5b7de0cbb21e3dd45d2522b3bdb5eb", "is_secret": false, "is_verified": false, "line_number": 65, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "eb19dd9b419cfaece7743a8b43f76d184831fe1a", "is_secret": false, "is_verified": false, "line_number": 66, "type": "Secret Keyword", "verified_result": null}], "opensearch/config/opensearch.yml": [{"hashed_secret": "10c3b757969a3a90bf55182c7d8db0ce18e32037", "is_secret": false, "is_verified": false, "line_number": 33, "type": "Secret Keyword", "verified_result": null}], "tools/common_functions.sh": [{"hashed_secret": "251e76217e69e2990a4d7c2ff22e87b96d67bd65", "is_secret": false, "is_verified": false, "line_number": 343, "type": "Secret Keyword", "verified_result": null}], "tools/dockerDeployZoa.sh": [{"hashed_secret": "2ace62c1befa19e3ea37dd52be9f6d508c5163e6", "is_secret": false, "is_verified": false, "line_number": 351, "type": "Secret Keyword", "verified_result": null}], "tools/dockerManageZoa.sh": [{"hashed_secret": "a6572d6ceb9aa301fc194a981d2986aeb335ad78", "is_secret": false, "is_verified": false, "line_number": 185, "type": "Secret Keyword", "verified_result": null}], "tools/k8sDeployZoa.sh": [{"hashed_secret": "2ace62c1befa19e3ea37dd52be9f6d508c5163e6", "is_secret": false, "is_verified": false, "line_number": 351, "type": "Secret Keyword", "verified_result": null}], "tools/kc_postUp.sh": [{"hashed_secret": "5e3274dc1e352e75268d4e2aa2b793eaf2765289", "is_secret": false, "is_verified": false, "line_number": 33, "type": "Secret Keyword", "verified_result": null}], "tools/loadKCIds.sh": [{"hashed_secret": "a6572d6ceb9aa301fc194a981d2986aeb335ad78", "is_secret": false, "is_verified": false, "line_number": 128, "type": "Secret Keyword", "verified_result": null}], "tools/podmanDeployZoa.sh": [{"hashed_secret": "2ace62c1befa19e3ea37dd52be9f6d508c5163e6", "is_secret": false, "is_verified": false, "line_number": 332, "type": "Secret Keyword", "verified_result": null}], "tools/podmanManageZoa.sh": [{"hashed_secret": "a6572d6ceb9aa301fc194a981d2986aeb335ad78", "is_secret": false, "is_verified": false, "line_number": 399, "type": "Secret Keyword", "verified_result": null}], "tools/updateKC.sh": [{"hashed_secret": "a6572d6ceb9aa301fc194a981d2986aeb335ad78", "is_secret": false, "is_verified": false, "line_number": 128, "type": "Secret Keyword", "verified_result": null}], "tools/updateKCpassword.sh": [{"hashed_secret": "2ace62c1befa19e3ea37dd52be9f6d508c5163e6", "is_verified": false, "line_number": 58, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "a6572d6ceb9aa301fc194a981d2986aeb335ad78", "is_verified": false, "line_number": 179, "type": "Secret Keyword", "verified_result": null}], "tools/zoa_create_perm_admin.sh": [{"hashed_secret": "5e3274dc1e352e75268d4e2aa2b793eaf2765289", "is_secret": false, "is_verified": false, "line_number": 30, "type": "Secret Keyword", "verified_result": null}], "tools/zoa_kc_update.sh": [{"hashed_secret": "5e3274dc1e352e75268d4e2aa2b793eaf2765289", "is_secret": false, "is_verified": false, "line_number": 32, "type": "Secret Keyword", "verified_result": null}], "tools/zoa_load_ids.sh": [{"hashed_secret": "5e3274dc1e352e75268d4e2aa2b793eaf2765289", "is_secret": false, "is_verified": false, "line_number": 50, "type": "Secret Keyword", "verified_result": null}], "zoa_env.config.common": [{"hashed_secret": "7cf7ed86e8f660ade5996a1072d74cd35def47af", "is_secret": false, "is_verified": false, "line_number": 238, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "33e1d5cc7f93d8ebd540cbbab36aac9ed2afcb0f", "is_secret": false, "is_verified": false, "line_number": 300, "type": "Secret Keyword", "verified_result": null}, {"hashed_secret": "bd3c220afdec7cb7fbbd695079ccf11c500c9d2e", "is_secret": false, "is_verified": false, "line_number": 421, "type": "Secret Keyword", "verified_result": null}]}, "version": "0.13.1+ibm.62.dss", "word_list": {"file": null, "hash": null}}