#!/usr/bin/env groovy
/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

import groovy.json.JsonOutput

echo "Branch: ${env.BRANCH_NAME}"

env.BASE_PATH = "com/ibm/nazare/discovery"
env.COMPONENT = "kafka-sieve"
env.VERSION = "1.4.3"
def uploadSubFolder = "$BASE_PATH/$COMPONENT/$VERSION"

def jfUrl = "https://na.artifactory.swg-devops.com/artifactory"
def repository = "sys-izoa-zds-maven-local"
def jfCredentialsId = 'zoa-artifactory-token'
def jfServer = Artifactory.newServer url: "${jfUrl}", credentialsId: "${jfCredentialsId}"

def now = new Date()
def buildTime = now.format("yyyyMMdd.HHmmss", TimeZone.getTimeZone('UTC'))

pipeline {
   agent {
      node {
         label 'zds-node'
      }
   }


   tools {
      jdk 'java-21'
   }

   options {
      disableConcurrentBuilds()
      buildDiscarder(logRotator(numToKeepStr: '5', artifactNumToKeepStr: '5'))
   }


   stages {

      stage('Build') {
         steps {
            sh """
            echo "Building"
            mvn clean compile
            """
         }
      }

      stage('Test') {
         environment {
            scannerHome = tool 'SonarQubeScanner'
         }
         steps {
            withSonarQubeEnv('ZDSSonarQube') {
               echo "Testing & Analyzing"
               sh "mvn org.jacoco:jacoco-maven-plugin:prepare-agent -Dmaven.test.failure.ignore=false -Dproject.version=${env.VERSION}-${buildTime} package sonar:sonar"
            }
         }
      }

      stage('Package') {
         steps {
            sh """
            echo "Packaging"
            mvn package
            """

            // Create a plugin directory structure for Kafka Connect
            sh """
            mkdir -p target/kafka-connect-plugin/kafka-sieve
            cp target/kafka-sieve-*.jar target/kafka-connect-plugin/kafka-sieve/

            # Create a distribution zip file
            cd target
            zip -r kafka-sieve-plugin-${env.VERSION}-${buildTime}.zip kafka-connect-plugin/
            """
         }
      }

      stage('Archive') {
         steps {
            script {
               if (env.BRANCH_NAME ==~ /(main|dev)/) {
                  archiveArtifacts artifacts: "target/*.jar,target/*.zip", fingerprint: true

                  def uploadSpec = """{
                  "files": [
                        {
                           "pattern": "target/kafka-sieve-*.jar",
                           "target": "${repository}/${uploadSubFolder}/"
                        },
                        {
                           "pattern": "target/kafka-sieve-plugin-*.zip",
                           "target": "${repository}/${uploadSubFolder}/"
                        }
                     ]
                  }"""
                  jfServer.upload spec: uploadSpec
               } else {
                  echo "Archive stage is skipped for feature branches!"
               }
            }
         }
      }
   }

   post {
      success {
         echo "Build ${buildTime} passed"
         notifyBuild(buildTime, "SUCCESS")
         deleteDir()
      }
      failure {
         echo "Build ${buildTime} failed"
         notifyBuild(buildTime, "FAILED")
         deleteDir()
      }
   }
}

// Notify slack for branch builds
def notifyBuild(String buildTime, String buildStatus = 'STARTED')
{
    // build status of null means successful
    buildStatus = buildStatus ?: 'SUCCESS'

    // Default values
    def colorName = 'RED'
    def colorCode = '#FF0000'
    def gitCommitMessage = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()

    def subject = "${env.JOB_NAME} - Build #${env.BUILD_NUMBER} : ${buildStatus}"
    def innerText = "Build started at: ${buildTime}"

    if (buildStatus == 'STARTED')
    {
        colorName = 'YELLOW'
        colorCode = '#FFFF00'
    }
    else if (buildStatus == 'SUCCESS')
    {
        colorName = 'GREEN'
        colorCode = '#00FF00'
    }
    else
    {
        colorName = 'RED'
        colorCode = '#FF0000'
    }

   def attachments = [
      [
         color: colorCode,
         pretext: 'IBM Z Discovery CI/CD',
         title: subject,
         title_link: env.BUILD_URL,
         text: innerText,
         fields: [[
               title: 'Component',
               value: env.COMPONENT,
               short: true
            ],
            [
               title: 'Branch',
               value: env.BRANCH_NAME,
               short: true
            ],
            [
               title: 'Version',
               value: env.VERSION,
               short: true
            ],
            [
               title: 'Node',
               value: env.NODE_NAME,
               short: true
            ]],
         footer: 'IBM Z Discovery',
         footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png'
      ]
   ]

   slackSend(attachments: attachments)
}
