#!/usr/bin/env groovy
import groovy.json.JsonOutput

env.COMPONENT = "zrdds-kafkaconnect"
env.SIEVE_COMPONENT = "kafka-sieve"
env.VERSION = "1.4.3"
env.BASE_PATH = "com/ibm/nazare/discovery"
def dockerRegistry = "icr.io/zoa-oci"
def sieve_repository = "sys-izoa-zds-maven-local"
def now = new Date()
def buildTime = now.format("yyyyMMdd.HHmmss", TimeZone.getTimeZone('UTC'))
def platform = "linux"
def tagPrefix = "${env.VERSION}.${buildTime}-${platform}"

def gitRepo = "discovery-kafkaconnect"
def sieveUploadSubFolder = "$BASE_PATH/$SIEVE_COMPONENT/$VERSION"

pipeline {
   agent none
   options {
      //skipDefaultCheckout()
      disableConcurrentBuilds()
      // Only keep the last 5 builds to save space. Always keeps the last successful build.
      buildDiscarder(logRotator(numToKeepStr: '5', artifactNumToKeepStr: '5'))
   }
   environment {
     DOCKER_REGISTRY = "${dockerRegistry}"
     REPO = "${gitRepo}"
     BRANCH = "${env.BRANCH_NAME}"
     COMPONENT = "${env.COMPONENT}"
     VERSION = "${env.VERSION}"
     TAG_PREFIX = "${tagPrefix}"
   }

   // Code checkout is implicit
   stages {
      stage('Parallel Build') {
         matrix {
            agent {
               label "docker-${ARCH}"
            }
            axes {
               axis {
                  name 'ARCH'
                  values 'amd64', 's390x'
               }
            }
            stages {
               stage('Prepare') {
                  steps {
                     withCredentials([
                        string(
                          credentialsId: 'zldazaadevfunc', 
                          variable: 'IBMCLOUD_API_KEY'
                        )
                     ])
                     {
                       script {
                         sh '''
                           ibmcloud config --check-version=false
                           ibmcloud login -r us-east
                           ibmcloud cr login --client docker
                         '''
                       }
                     }
                  }
               }

               stage('Fetch Kafka Sieve') {
                 steps {
                    withCredentials([
                      string(credentialsId: 'zoa-artifactory-token', variable: 'ARTIFACTORY_TOKEN')
                    ]) {
                      sh '''#!/bin/bash
                        set -euxo pipefail

                        mkdir -p docker/plugins/kafka-sieve
                        ARTIFACT_URL="https://na.artifactory.swg-devops.com/artifactory/${sieve_repository}/${sieveUploadSubFolder}/kafka-sieve-${VERSION}.jar"
                        curl -fsSL -H "X-JFrog-Art-Api: ${ARTIFACTORY_TOKEN}" \
                        -o docker/plugins/kafka-sieve/kafka-sieve-${VERSION}.jar \
                        "${ARTIFACT_URL}"
                        ls -l docker/plugins/kafka-sieve
                      '''
                    }
                 }
              }

               stage('Build') {
                  steps {
                     script {
                       sh '''
                         LINARCH=`uname -m`
                         if [ "${LINARCH}" = "s390x" ]
                         then
                           sed -i -e "s%-x86_64%-s390x%g" Dockerfile
                           BUILDARCH=s390x
                         else
                           BUILDARCH=amd64
                         fi
                         REPO_U=`echo ${REPO} | tr "-" "_" | tr [[:upper:]] [[:lower:]]`
                         COMMIT_HASH=`git rev-parse --verify origin/${BRANCH}`
                         if [ "${BRANCH}" = "main" ] || [ "${BRANCH}" = "dev" ]
                         then
                           docker build --build-arg COMMIT=${COMMIT_HASH} --build-arg REPO=${REPO_U} -f Dockerfile -t ${DOCKER_REGISTRY}/${COMPONENT}:${VERSION}-${BUILDARCH} .
                         else
                           docker build --build-arg COMMIT=${COMMIT_HASH} --build-arg REPO=${REPO_U} -f Dockerfile -t ${DOCKER_REGISTRY}/${COMPONENT}:${TAG_PREFIX}-${BUILDARCH} .
                         fi
                       '''
                     }
                  }
               }

               stage('Push') {
                  steps {
                     script {
                        if (env.BRANCH_NAME ==~ /(main|dev)/) {
                           sh "docker push ${dockerRegistry}/${env.COMPONENT}:${env.VERSION}-${ARCH}"
                        } else {
                           echo "Docker push is skipped for feature branches!"
                        }
                     }
                  }
               }

               stage('Clean') {
                  steps {
                     script {
                        if (env.BRANCH_NAME ==~ /(main|dev)/) {
                           sh "docker rmi ${dockerRegistry}/${env.COMPONENT}:${env.VERSION}-${ARCH}"
                        } else {
                           sh "docker rmi ${dockerRegistry}/${env.COMPONENT}:${env.TAG_PREFIX}-${ARCH}"
                        }
                     }
                  }
               }
            }
            post {
               success {
                  echo "Build ${buildTime} passed!"
                  notifyBuild(buildTime, "SUCCESS")
               }
               unsuccessful {
                  echo "Build ${buildTime} failed!"
                  notifyBuild(buildTime, "FAILED")
               }
            }
         }
      }
   }
}

// Notify slack for branch builds
def notifyBuild(String buildTime, String buildStatus = 'STARTED')
{
    // build status of null means successful
    buildStatus = buildStatus ?: 'SUCCESS'

    // Default values
    def colorName = 'RED'
    def colorCode = '#FF0000'
    def gitCommitMessage = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()

    def subject = "${env.JOB_NAME} - Build #${env.BUILD_NUMBER} : ${buildStatus}"
    def innerText = "Build started at: ${buildTime}"

    if (buildStatus == 'STARTED')
    {
        colorName = 'YELLOW'
        colorCode = '#FFFF00'
    }
    else if (buildStatus == 'SUCCESS')
    {
        colorName = 'GREEN'
        colorCode = '#00FF00'
    }
    else
    {
        colorName = 'RED'
        colorCode = '#FF0000'
    }

   def attachments = [
      [
         color: colorCode,
         pretext: 'IBM Z Discovery CI/CD',
         title: subject,
         title_link: env.BUILD_URL,
         text: innerText,
         fields: [[
               title: 'Container',
               value: env.COMPONENT,
               short: true
            ],
            [
               title: 'Branch',
               value: env.BRANCH_NAME,
               short: true
            ],
            [
               title: 'Version',
               value: env.VERSION,
               short: true
            ],
            [
               title: 'Node',
               value: env.NODE_NAME,
               short: true
            ]],
         footer: 'IBM Z Discovery',
         footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png'
      ]
   ]

   slackSend(attachments: attachments)
}
