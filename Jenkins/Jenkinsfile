#!/usr/bin/env groovy
/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021, 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

 echo "Branch: ${env.BRANCH_NAME}"

env.COMPONENT = "zrdds-ext-api"
env.VERSION = "1.4.3"

def dockerRegistry = "icr.io/zoa-oci"

def artifactoryDockerRegistry = "docker-eu.artifactory.swg-devops.com/sys-nazare-cicd-team-wazi-discovery-docker-local/discovery"

def now = new Date()
def buildTime = now.format("yyyyMMdd.HHmmss", TimeZone.getTimeZone('UTC'))
def buildVersion = "${env.VERSION}"

def gitRepo = "zrdds-ext-api"
def tagPrefix = "${env.VERSION}.${buildTime}"

pipeline {
   agent none

   options {
      //skipDefaultCheckout()
      disableConcurrentBuilds()
      // Only keep the last 5 builds to save space. Always keeps the last successful build.
      buildDiscarder(logRotator(numToKeepStr: '5', artifactNumToKeepStr: '5'))
   }
      tools {
      maven 'Nazare_MVN'
   }

      environment {
     DOCKER_REGISTRY = "${dockerRegistry}"
     COMPONENT = "${env.COMPONENT}"
     TAG_PREFIX = "${tagPrefix}"
     REPO = "${gitRepo}"
     BRANCH = "${env.BRANCH_NAME}"
   }

   // Code checkout is implicit
   stages {
      stage('Parallel Build') {
         matrix {
            agent {
               label "docker-${ARCH}"
            }
            axes {
               axis {
                  name 'ARCH'
                  values 'amd64', 's390x'
               }
            }
            stages {
               stage('Compile') {
                  steps {
                     sh "mvn clean compile"
                  }
               }
               stage('Test') {
                  environment {
                     scannerHome = tool 'SonarQubeScanner'
                   }
                  steps {
                     withSonarQubeEnv('ZDSSonarQube') { 
                        echo "Testing & Analyzing"
                        sh "mvn org.jacoco:jacoco-maven-plugin:prepare-agent -Dmaven.test.failure.ignore=false -Dproject.version=${env.VERSION}-${buildTime} package sonar:sonar"
                     }
                  }
               }
               stage('Build') {
                  steps {
                     script {
                        echo "Docker Build"

                        if (env.BRANCH_NAME ==~ /(release|main)/) {
                           tagPrefix = "${env.VERSION}"
                        }

                        sh "docker build -f Dockerfile -t ${artifactoryDockerRegistry}/${env.COMPONENT}:${tagPrefix}-${ARCH} ."
                     }
                  }
               }
               stage('Push') {
                  steps {
                     script {
                        if (env.BRANCH_NAME ==~ /(release|main)/) {
                           sh "docker push ${artifactoryDockerRegistry}/${env.COMPONENT}:${tagPrefix}-${ARCH}"
                        } else {
                           echo "Push stage is skipped for feature branches!"
                        }
                     }
                  }
               }
               stage('Clean') {
                  steps {
                     script {
                        sh "docker rmi ${artifactoryDockerRegistry}/${env.COMPONENT}:${tagPrefix}-${ARCH}"
                     }
                  }
               }
            }
            post {
               success {
                  echo "Build ${buildTime} passed!"
                  notifyBuild(buildTime, "SUCCESS")        
               }
               unsuccessful {
                  echo "Build ${buildTime} failed!" 
                  notifyBuild(buildTime, "FAILED")
               }
            }
         }
      }
   }
}

// Notify slack for branch builds
def notifyBuild(String buildTime, String buildStatus = 'STARTED')
{
    // build status of null means successful
    buildStatus = buildStatus ?: 'SUCCESS'

    // Default values
    def colorName = 'RED'
    def colorCode = '#FF0000'
    def gitCommitMessage = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()

    def subject = "${env.JOB_NAME} - Build #${env.BUILD_NUMBER} : ${buildStatus}"
    def innerText = "Build started at: ${buildTime}"

    if (buildStatus == 'STARTED')
    {
        colorName = 'YELLOW'
        colorCode = '#FFFF00'
    }
    else if (buildStatus == 'SUCCESS')
    {
        colorName = 'GREEN'
        colorCode = '#00FF00'
    }
    else
    {
        colorName = 'RED'
        colorCode = '#FF0000'
    }

   def attachments = [
      [
         color: colorCode,
         pretext: 'IBM Z Discovery CI/CD',
         title: subject,
         title_link: env.BUILD_URL,
         text: innerText,
         fields: [[
               title: 'Container',
               value: env.COMPONENT,
               short: true
            ],
            [
               title: 'Branch',
               value: env.BRANCH_NAME,
               short: true
            ],
            [
               title: 'Version',
               value: env.VERSION,
               short: true
            ],
            [
               title: 'Node',
               value: env.NODE_NAME,
               short: true
            ]],
         footer: 'IBM Z Discovery',
         footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png'
      ]
   ]

   slackSend(attachments: attachments)
}