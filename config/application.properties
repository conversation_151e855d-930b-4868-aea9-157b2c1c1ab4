server.port=7778

host.allowlist=

# SSL protocol to use
server.ssl.protocol=TLS
# Enabled SSL protocols
server.ssl.enabled-protocols=TLSv1.3
#enable/disable https
server.ssl.enabled=true
server.ssl.key-alias=baratheon_https
server.ssl.key-password=baratheon123456
server.ssl.key-store-password=baratheon123456
server.ssl.key-store=classpath:baratheon_https.keystore
server.ssl.key-store-type=PKCS12

# Enable ajp protocol in embedded Tomcat instance
tomcat.ajp.port=9999
tomcat.ajp.remoteauthentication=false
tomcat.ajp.enabled=false
tomcat.accept-count=200
tomcat.max-connections=200
tomcat.max-http-post-size=200
tomcat.max-http-header-size=209715200

# Authentication Mode: "local" or "keycloak"
authentication.mode=local

# KeyCloak Config
# integrate JWT to validate access_token via the `jwks_uri`
# the format of `jwks_uri`: ${host}:${port}/auth/realms/${realm_name}/protocol/openid-connect/certs
keycloak.jwks.uri=http://localhost:8080/auth/realms/Test/protocol/openid-connect/certs

# use the `token_uri` can get access_token with sensitive information and validate it via the `token_introspect_uri` with same information
# the format `token_uri`: ${host}:${port}/auth/realms/${realm_name}/protocol/openid-connect/token
#keycloak.token.uri=http://localhost:8080/auth/realms/Test/protocol/openid-connect/token

# Local Token String
api.token=098f6bcd4621d373cade4e832627b4f6
core.url=https://localhost:7777
core.api.token=098f6bcd4621d373cade4e832627b4f6
server.error.include-exception=false
server.error.include-message=always

# Logging
logging.level.org.springframework.boot.autoconfigure.security=INFO
logging.level.org.springframework.security=INFO
logging.level.root=INFO
logging.file.name=./log/zrdds-ext-api.log

# springdoc
app.version=v1.4.3
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.contextPath=/zrdds/ext/api/v1
springdoc.api-docs.enabled=true
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.disable-swagger-default-url=true

# DataSource Config
mybatis.datasource.driver-class-name=org.postgresql.Driver
mybatis.datasource.url=******************************************
mybatis.datasource.username=postgres
mybatis.datasource.password=postgres
