How to integrate Z Operational Analytics services run on <PERSON><PERSON> into the 
systemd management framework
*****************************************************************************

What products does this package apply to?
-----------------------------------------------------------------------------
- Z Anomaly Analytics
- Z Operational Log and Data Analytics

What does this package contain?
-----------------------------------------------------------------------------
- zoa-podman-systemd.tar.gz:
  A TAR file containing a sample systemd unit file that can be used to stop 
  and start Z Operational Analytics services running on Podman via systemd.
- ZOA_HOME/bin/podmanRestartZoa.sh:
  A helper script that helps resolve some of the challenges that may occur 
  when integrating Podman-based services into systemd.
- This README file with instructions for how to install and configure the 
  aforementioned artifacts.

How to install and configure the artifacts
-----------------------------------------------------------------------------
NOTES:
- These instructions assume that the user ID under which podmanManageZoa.sh
  is run is 'zoauser'. If you use a different user ID, adjust the
  instructions accordingly.
- Most of the steps in these instructions require superuser authority.
- The instructions do NOT apply to Docker users.

1. Unpack zoa-podman-systemd.tar.gz into the root of the file system tree.
   This will place the systemd unit file contained in it into the correct
   location:

     tar xvf zoa-podman-systemd.tar.gz -C /

2. Open the file /usr/lib/systemd/system/zoa-podman.service in a text
   editor and make the following updates:
   - Replace <ZOAUSER> with the user ID under which you run the
     podmanManageZoa.sh script.
   - Replace <ZOA_HOME> with the directory in which the Z Operational
     Analytics product is installed.

3. Enable 'login lingering' for the user ID under which you run the
   podmanManageZoa.sh script:

     sudo loginctl enable-linger zoauser

4. Enable the zoa-podman service:

     sudo systemctl enable zoa-podman

5. Copy the file ZOA_HOME/bin/podmanRestartZoa.sh into the 'bin/'
   subdirectory of the location where the Z Operational Analytics product
   is installed:

     cp ZOA_HOME/bin/podmanRestartZoa.sh <ZOA_HOME>/bin/

6. Make sure that podmanRestartZoa.sh is owned by the correct user ID
   and that it is executable:

     sudo chown zoauser:zoauser <ZOA_HOME>/bin/podmanRestartZoa.sh
     sudo chmod 755 <ZOA_HOME>/bin/podmanRestartZoa.sh

7. Start the zoa-podman service:

     sudo systemctl start zoa-podman

   If any of the Podman-based services are running, they will be shut down.
   Then, all of the Podman-based services will be started up.
