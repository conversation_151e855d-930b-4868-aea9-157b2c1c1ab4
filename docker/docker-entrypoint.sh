#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ZAA (C) Copyright IBM Corp. 2021
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

cd zrdds-ext-api

MYBATIS_DATASOURCE_URL="${MYBATIS_DATASOURCE_URL:-******************************************}"
MYBATIS_DATASOURCE_USERNAME="${MYBATIS_DATASOURCE_USERNAME:-postgres}"
MYBATIS_DATASOURCE_PASSWORD="${MYBATIS_DATASOURCE_PASSWORD:-postgres}"

sed -e "s|mybatis.datasource.url=.*|mybatis.datasource.url=${MYBATIS_DATASOURCE_URL}| ;\
s|mybatis.datasource.username=.*|mybatis.datasource.username=${MYBATIS_DATASOURCE_USERNAME}| ;\
s|mybatis.datasource.password=.*|mybatis.datasource.password=${MYBATIS_DATASOURCE_PASSWORD}| ;"\
  config/application.properties > config/application.properties.tmp

mv config/application.properties.tmp config/application.properties

exec "$@"