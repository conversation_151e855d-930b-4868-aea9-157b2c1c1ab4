offset.storage.file.filename=/opt/kafka/data/connect.offsets
# auto.offset.reset=latest
offset.flush.interval.ms=10000
name=connector
connector.class=io.debezium.connector.postgresql.PostgresConnector
plugin.name=pgoutput
database.hostname=localhost
database.port=5432
database.user=postgres
database.password=postgres
database.dbname=dev
topic.prefix=IBM-ZRDDS-SNOW
table.include.list=public.z_series_computer,public.sysplex,public.zos,public.lpar,public.db2_data_sharing_group,public.db2_subsystem,public.db2_database,public.db2_stored_procedure,public.cics_region,public.cics_transaction,public.ims_subsystem,public.ims_database,public.ims_transaction,public.ims_sysplex_group,public.mq_subsystem,public.mq_local_queue,public.mq_model_queue,public.mq_alias_queue,public.mq_remote_queue,public.mq_queue_sharing_group,public.jcl_operation_data,public.jcl_dynamic_data,public.relationship_service_now
column.exclude.list=
skip.messages.without.change=true

# Transformation:
transforms=sieve,unwrap
transforms.sieve.type=com.ibm.palantir.transforms.KafkaSieveTransform
transforms.sieve.ignore.columns=scan_date,kafka_send_date
transforms.unwrap.type=io.debezium.transforms.ExtractNewRecordState

#Snapshot:
snapshot.mode=no_data