# Build Spring Cloud Gateway JAR
FROM icr.io/zoa-oci/zoatools-maven:3.9.9-x86_64 AS build

ARG NETTY_VERSION=see_.buildenv

RUN mkdir -p /gateway-app
COPY ./zoa-spring-cloud-gateway-MF /gateway-app
COPY ./zoa-common-docker-MF/netty_jars.tar /gateway-app/
WORKDIR /gateway-app
RUN NETTY_SHORTVER=${NETTY_VERSION##netty-} && \
  tar xvf netty_jars.tar && rm netty_jars.tar && \
  mvn install:install-file -Dfile=netty-resolver-dns-${NETTY_SHORTVER}.jar -DgroupId=io.netty -DartifactId=netty-resolver-dns -Dversion=${NETTY_SHORTVER} -Dpackaging=jar && \
  mvn install:install-file -Dfile=netty-common-${NETTY_SHORTVER}.jar -DgroupId=io.netty -DartifactId=netty-common -Dversion=${NETTY_SHORTVER} -Dpackaging=jar && \
  mvn -Dmaven.test.skip clean install

# Build Spring Cloud Gateway OCI container
FROM icr.io/zoa-oci/zoacommon-jre21-micro:21.0.5_11-x86_64

ARG REPO=unknown
ARG COMMIT=unknown
ARG VRMF=0.0.1-SNAPSHOT

LABEL feature="IBM Z AIOps - Common Services - Core"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

ENV VRMF=${VRMF}

RUN mkdir -p /gateway-app /ssl
COPY --from=build /gateway-app/target/zoa-spring-cloud-gateway-${VRMF}.jar /gateway-app/src/main/resources/log4j2.xml /gateway-app/

WORKDIR /gateway-app
COPY /zoa-spring-cloud-gateway-MF/docker/bin/main.sh ./zoa-spring-cloud-gateway-MF/docker/bin/healthcheck.sh /usr/local/bin/

RUN chgrp -R 0 /gateway-app /opt /ssl && \
  chmod -R g=u /gateway-app /opt /ssl && \
  chmod 755 /usr/local/bin/*.sh && \
  mv /usr/local/bin/main.sh /gateway-app/

USER nonroot

HEALTHCHECK NONE
# HEALTHCHECK CMD /usr/local/bin/healthcheck.sh

CMD ["./main.sh"]
