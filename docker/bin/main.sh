#! /bin/bash
export GW_PASS=`echo "${ZAIOPS_GW_PASS}" | base64 -d`
export ZRDDS_CORE_HOST
export ZRDDS_API_HOST
export TOPOLOGY_UI
export ZRDDS_KB_HOST
SSL_DEBUG_PARM=""

if [ "${SUBDOMAIN}x" != "x" ]
then
  export DISCOVERY_ROUTE=http://discovery.${SUBDOMAIN}:8761/eureka
  export ZDAP_ROUTE=https://dashboards.${SUBDOMAIN}:5601/insights/
  export KEYCLOAK_ROUTE=http://${KC_INTERNAL_HOST}.${SUBDOMAIN}:8080/${ZAIOPS_KC_CONTEXT_ROOT}/
  export KEYCLOAK_URI=http://${KC_INTERNAL_HOST}.${SUBDOMAIN}:8080/${ZAIOPS_KC_CONTEXT_ROOT}/realms/IzoaKeycloak/protocol/openid-connect/certs
else
  export KEYCLOAK_ROUTE=http://${KC_INTERNAL_HOST}:8080/${ZAIOPS_KC_CONTEXT_ROOT}/
  export KEYCLOAK_URI=http://${KC_INTERNAL_HOST}:8080/${ZAIOPS_KC_CONTEXT_ROOT}/realms/IzoaKeycloak/protocol/openid-connect/certs
fi

# Prepare TLS artifacts
if [ -f /shared/config/zoasvc.tls ]
then
  ORIGIN=`pwd`
  mkdir -p /ssl && rm -f /ssl/*
  cd /ssl
  cat /shared/config/zoasvc.tls | base64 -d | tar xz
  cd ${ORIGIN}
else
  echo "ERROR: TLS artifacts not found."
  exit 1
fi

if [ "$( echo ${SSL_DEBUG} | tr [:lower:] [:upper:] )" = "TRUE" ]
then
  SSL_DEBUG_PARM="-Djavax.net.debug=ssl"
fi

# Update log4j2.xml to only log to console
sed -i "/ref=\"LogToFile\"/d; " /gateway-app/log4j2.xml

java ${SSL_DEBUG_PARM} -Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache -Dlog4j.configurationFile=/gateway-app/log4j2.xml -jar zoa-spring-cloud-gateway-${VRMF}.jar
