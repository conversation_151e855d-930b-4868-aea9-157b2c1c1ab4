apiVersion: apps/v1
kind: Deployment
metadata:
  name: zaiops-util
  namespace: ${NAMESPACE}
spec:
  selector:
    matchLabels:
      app: zaiops-util
  template:
    metadata:
      labels:
        app: zaiops-util
        family: ${SUBDOMAIN}
    spec:
      hostname: zaiops-util
      subdomain: ${SUBDOMAIN}
      containers:
      - name: zaiops-util
        image: icr.io/zoa-oci/zoa-service-discovery:${TAG}-x86_64
        imagePullPolicy: ${IMAGE_PULL_POLICY}
        command: ['/bin/bash', '-c', 'sleep 3600']
        resources:
          limits:
            # minimum required
            # cpu: "250m"
            # memory: "500M"
            # actually available
            cpu: "250m"
            memory: "500M"
        volumeMounts:
          - name: zaiops-shared
            mountPath: /shared
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: [ "ALL" ]
            add: [ "NET_BIND_SERVICE" ]
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      imagePullSecrets:
      - name: ${REG_SECRET}
      volumes:
      - name: zaiops-shared
        persistentVolumeClaim:
          claimName: zaiops-shared-pvc
