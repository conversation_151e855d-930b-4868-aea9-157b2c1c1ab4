apiVersion: apps/v1
kind: Deployment
metadata:
  name: zoa-gateway
  namespace: ${NAMESPACE}
spec:
  selector:
    matchLabels:
      app: zoa-gateway
  template:
    metadata:
      labels:
        app: zoa-gateway
        family: ${SUBDOMAIN}
    spec:
      hostname: gateway
      subdomain: ${SUBDOMAIN}
      containers:
      - name: zoa-gateway
        image: icr.io/zoa-oci/zoa-gateway:${TAG}-x86_64
        imagePullPolicy: ${IMAGE_PULL_POLICY}
        ports:
        - containerPort: 8085
        env:
        - name: EXTERNAL_GATEWAY_HOST
          value: '${EXTERNAL_GATEWAY_HOST}'
        - name: IZOA_GATEWAY_PORT
          value: '${IZOA_GATEWAY_PORT}'
        - name: ZAIOPS_KC_REALM
          value: '${ZAIOPS_KC_REALM}'
        - name: SERVER_HTTP2_ENABLED
          value: '${ENABLE_HTTP2}'
        - name: PI_FRAMEWORK_ROUTE
          value: '${PI_FRAMEWORK_ROUTE}'
        - name: ZAIOPS_GW_PASS
          value: '${ZAIOPS_ZOASVC_PASS}'
        - name: SSL_DEBUG
          value: '${SSL_DEBUG}'
        - name: KC_INTERNAL_HOST
          value: '${KC_INTERNAL_HOST}'
        - name: RATELIMIT_LIMIT
          value: '${ZAIOPS_TOTAL_RATE_LIMIT}'
        - name: ZAIOPS_KC_CONTEXT_ROOT
          value: '${ZAIOPS_KC_CONTEXT_ROOT}'
        - name: SUBDOMAIN
          value: '${SUBDOMAIN}'
        - name: ZRDDS_API_HOST
          value: '${ZRDDS_API_HOST}'
        - name: ZRDDS_CORE_HOST
          value: '${ZRDDS_CORE_HOST}'
        - name: TOPOLOGY_UI
          value: '${TOPOLOGY_UI:-zrddsui}'
        - name: ZRDDS_KB_HOST
          value: '${ZRDDS_KB_HOST:-kafkabridge}'
        resources:
          limits:
            # minimum required
            # cpu: "1000m"
            # memory: "2G"
            # actually available
            cpu: "1000m"
            memory: "2G"
        volumeMounts:
          - name: zaiops-shared
            mountPath: /shared
            readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: [ "ALL" ]
            add: [ "NET_BIND_SERVICE" ]
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      imagePullSecrets:
      - name: ${REG_SECRET}
      volumes:
      - name: zaiops-shared
        persistentVolumeClaim:
          claimName: zaiops-shared-pvc
