apiVersion: apps/v1
kind: Deployment
metadata:
  name: kcdb-util
  namespace: ${NAMESPACE}
spec:
  selector:
    matchLabels:
      app: kcdb-util
  template:
    metadata:
      labels:
        app: kcdb-util
        family: ${SUBDOMAIN}
    spec:
      hostname: kcdb-util
      subdomain: ${SUBDOMAIN}
      containers:
      - name: kcdb-util
        image: icr.io/zoa-oci/zoa-service-discovery:${TAG}-x86_64
        imagePullPolicy: ${IMAGE_PULL_POLICY}
        command: ['/bin/bash', '-c', 'sleep 3600']
        resources:
          limits:
            # minimum required
            # cpu: "1000m"
            # memory: "2G"
            # actually available
            cpu: "1000m"
            memory: "2G"
        volumeMounts:
          - name: zaiops-keycloak
            mountPath: /kcdb
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: [ "ALL" ]
            add: [ "NET_BIND_SERVICE" ]
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      imagePullSecrets:
      - name: ${REG_SECRET}
      volumes:
      - name: zaiops-keycloak
        persistentVolumeClaim:
          claimName: zaiops-keycloak-pvc
