apiVersion: v1
kind: Service
metadata:
  name: zoa-kafkabroker
  namespace: ${NAMESPACE}
spec:
  selector:
    app: zoa-kafkabroker
  # For Fyre OCP, use 'type: NodePort' for now
  # type: NodePort
  # For ITC OCP, use no type
  ports:
    - name: kafkanontls
      protocol: TCP
      port: ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}
      # 'nodePort' is only required for a NodePort configuration type
      # nodePort: ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}
      targetPort: 9092
    - name: kafkatls
      protocol: TCP
      port: ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}
      # 'nodePort' is only required for a NodePort configuration type
      # nodePort: ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}
      targetPort: 9093
