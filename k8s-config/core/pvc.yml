apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-shared-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 1M
  accessModes:
    - ReadWriteOnce
  volumeName: zaiops-shared-pv
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-keycloak-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 1G
  accessModes:
    - ReadWriteOnce
  volumeName: zaiops-keycloak-pv
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-kafkabroker-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 10G
  accessModes:
    - ReadWriteOnce
  volumeName: zaiops-kafkabroker-pv
