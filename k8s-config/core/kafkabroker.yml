apiVersion: apps/v1
kind: Deployment
metadata:
  name: zoa-kafkabroker
  namespace: ${NAMESPACE}
spec:
  selector:
    matchLabels:
      app: zoa-kafkabroker
  template:
    metadata:
      labels:
        app: zoa-kafkabroker
        family: ${SUBDOMAIN}
    spec:
      hostname: kafkabroker
      subdomain: ${SUBDOMAIN}
      containers:
      - name: zoa-kafkabroker
        image: icr.io/zoa-oci/zoa-kafkabroker:${TAG}-x86_64
        imagePullPolicy: ${IMAGE_PULL_POLICY}
        ports:
        - containerPort: 9092
        - containerPort: 9093
        - containerPort: 19092
        env:
        - name: KAFKA_NODE_ROLE
          value: 'broker'
        - name: KAFKA_HEAP_OPTS
          value: -Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g
        - name: KAFKA_OPTS
          value: -Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache
        - name: ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST
          value: '${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}'
        - name: ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT
          value: '${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}'
        - name: ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT
          value: '${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}'
        - name: ZAIOPS_KAFKA_PASS
          value: '${ZAIOPS_ZOASVC_PASS}'
        - name: ZAIOPS_KAFKA_RETENTION_HOURS
          value: '${ZAIOPS_KAFKA_RETENTION_HOURS}'
        - name: SSL_DEBUG
          value: '${SSL_DEBUG}'
        - name: SUBDOMAIN
          value: '${SUBDOMAIN}'
        resources:
          limits:
            # minimum required
            # cpu: "2000m"
            # memory: "4G"
            # actually available
            cpu: "2000m"
            memory: "4G"
        volumeMounts:
          - name: zaiops-kafkabroker
            mountPath: /opt/kafka/data
          - name: zaiops-shared
            mountPath: /shared
            readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: [ "ALL" ]
            add: [ "NET_BIND_SERVICE" ]
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      imagePullSecrets:
      - name: ${REG_SECRET}
      volumes:
      - name: zaiops-kafkabroker
        persistentVolumeClaim:
          claimName: zaiops-kafkabroker-pvc
      - name: zaiops-shared
        persistentVolumeClaim:
          claimName: zaiops-shared-pvc
