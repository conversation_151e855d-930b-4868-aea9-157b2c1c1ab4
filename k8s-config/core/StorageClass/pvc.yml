apiVersion: v1
kind: PersistentVolumeClaim
#--------------------------------------------------------------------------------#
# 'rook-cephfs' is for deployment in a Fyre OCP cluster, assuming you have set
# up a rook-cephfs storage class from the storage available on worker nodes.
# For deployment in the ITC OCP cluster, use 'ibm-spectrum-scale-csi-lt' instead of
# 'rook-cephfs'.
#--------------------------------------------------------------------------------#
metadata:
  name: zaiops-shared-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 1M
  accessModes:
    - ReadWriteMany
  storageClassName: rook-cephfs
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-keycloak-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 1G
  accessModes:
    - ReadWriteMany
  storageClassName: rook-cephfs
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-kafkabroker-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 10G
  accessModes:
    - ReadWriteMany
  storageClassName: rook-cephfs
