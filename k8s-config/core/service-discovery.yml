apiVersion: apps/v1
kind: Deployment
metadata:
  name: zoa-service-discovery
  namespace: ${NAMESPACE}
spec:
  selector:
    matchLabels:
      app: zoa-service-discovery
  template:
    metadata:
      labels:
        app: zoa-service-discovery
        family: ${SUBDOMAIN}
    spec:
      hostname: discovery
      subdomain: ${SUBDOMAIN}
      containers:
      - name: zoa-service-discovery
        image: icr.io/zoa-oci/zoa-service-discovery:${TAG}-x86_64
        imagePullPolicy: ${IMAGE_PULL_POLICY}
        env:
        - name: JAVA_OPTS
          value: -DEUREKA_URI=http://localhost:8761/eureka
        resources:
          limits:
            # minimum required
            # cpu: "250m"
            # memory: "500M"
            # actually available
            cpu: "250m"
            memory: "500M"
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: [ "ALL" ]
            add: [ "NET_BIND_SERVICE" ]
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      imagePullSecrets:
      - name: ${REG_SECRET}
