apiVersion: apps/v1
kind: Deployment
metadata:
  name: zoa-auth
  namespace: ${NAMESPACE}
spec:
  selector:
    matchLabels:
      app: zoa-auth
  template:
    metadata:
      labels:
        app: zoa-auth
        family: ${SUBDOMAIN}
    spec:
      hostname: auth
      subdomain: ${SUBDOMAIN}
      containers:
      - name: zoa-auth
        image: icr.io/zoa-oci/zoa-auth:${TAG}-x86_64
        imagePullPolicy: ${IMAGE_PULL_POLICY}
        ports:
        - containerPort: 8443
        env:
        - name: EXTERNAL_GATEWAY_HOST
          value: '${EXTERNAL_GATEWAY_HOST}'
        - name: HOSTNAME_STRICT
          value: '${HOSTNAME_STRICT}'
        - name: IZOA_GATEWAY_PORT
          value: '${IZOA_GATEWAY_PORT}'
        - name: KC_FORCE_EXTERNAL_HOSTNAME
          value: '${KC_FORCE_EXTERNAL_HOSTNAME}'
        - name: <PERSON>L_DEBUG
          value: '${SSL_DEBUG}'
        - name: ZA<PERSON>PS_KC_BOOTSTRAP_ADMIN
          value: '${ZAIOPS_KC_BOOTSTRAP_ADMIN}'
        - name: ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED
          value: '${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}'
        - name: ZAIOPS_KC_BOOTSTRAP_PASSWORD
          value: '${ZAIOPS_KC_BOOTSTRAP_PASSWORD}'
        - name: ZAIOPS_KC_CACHE_MODE
          value: '${ZAIOPS_KC_CACHE_MODE}'
        - name: ZAIOPS_KC_CONTEXT_ROOT
          value: '${ZAIOPS_KC_CONTEXT_ROOT}'
        - name: ZAIOPS_KC_DB
          value: '${ZAIOPS_KC_DB}'
        - name: ZAIOPS_KC_DB_PWD
          value: '${ZAIOPS_KC_DB_PWD}'
        - name: ZAIOPS_KC_DB_URL
          value: '${ZAIOPS_KC_DB_URL}'
        - name: ZAIOPS_KC_DB_USER
          value: '${ZAIOPS_KC_DB_USER}'
        - name: ZAIOPS_KC_HTTPS_PORT
          value: '${ZAIOPS_KC_HTTPS_PORT}'
        - name: ZAIOPS_KC_KEYSTORE_FILE
          value: '${ZAIOPS_KC_KEYSTORE_FILE}'
        - name: ZAIOPS_KC_KEYSTORE_TYPE
          value: '${ZAIOPS_KC_KEYSTORE_TYPE}'
        - name: ZAIOPS_KC_PASS
          value: '${ZAIOPS_ZOASVC_PASS}'
        - name: ZAIOPS_KC_TRUSTSTORE_FILE
          value: '${ZAIOPS_KC_TRUSTSTORE_FILE}'
        - name: ZAIOPS_KC_TRUSTSTORE_TYPE
          value: '${ZAIOPS_KC_TRUSTSTORE_TYPE}'
        - name: ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED
          value: '${ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED:-false}'
        - name: ZAIOPS_KEYCLOAK_IP
          value: '${ZAIOPS_KEYCLOAK_IP}'
        - name: ZAIOPS_KEYCLOAK_LOGLEVEL
          value: '${ZAIOPS_KEYCLOAK_LOGLEVEL:-INFO}'
        - name: ZAIOPS_KEYCLOAK_METRICAPI_ENABLED
          value: '${ZAIOPS_KEYCLOAK_METRICAPI_ENABLED:-false}'
        - name: ZAIOPS_KEYCLOAK_PORT
          value: '${ZAIOPS_KEYCLOAK_PORT}'
        - name: ZAIOPS_PROXY_HEADERS
          value: '${ZAIOPS_PROXY_HEADERS}'
        - name: ZAIOPS_TLS_VERSION
          value: '${ZAIOPS_TLS_VERSION}'
        - name: SUBDOMAIN
          value: '${SUBDOMAIN}'
        resources:
          limits:
            # minimum required
            # cpu: "2000m"
            # memory: "8G"
            # actually available
            cpu: "2000m"
            memory: "8G"
        volumeMounts:
          - name: zaiops-keycloak
            mountPath: /opt/keycloak/data/h2
          - name: zaiops-shared
            mountPath: /shared
            readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: [ "ALL" ]
            add: [ "NET_BIND_SERVICE" ]
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      imagePullSecrets:
      - name: ${REG_SECRET}
      volumes:
      - name: zaiops-keycloak
        persistentVolumeClaim:
          claimName: zaiops-keycloak-pvc
      - name: zaiops-shared
        persistentVolumeClaim:
          claimName: zaiops-shared-pvc
