apiVersion: v1
kind: Service
metadata:
  name: zoa-gateway
  namespace: ${NAMESPACE}
spec:
  selector:
    app: zoa-gateway
  # For Fyre OCP, use 'type: NodePort' for now
  # type: NodePort
  # For ITC OCP, use no type
  ports:
    - name: gwtls
      protocol: TCP
      port: 8085
      # 'nodePort' is only required for a NodePort configuration type
      # nodePort: ${IZOA_GATEWAY_PORT}
      targetPort: 8085
