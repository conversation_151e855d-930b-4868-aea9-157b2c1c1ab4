apiVersion: v1
kind: PersistentVolume
metadata:
  name: zaiops-shared-pv
  labels:
    type: local
spec:
  capacity:
    storage: 1M
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/usr/share/k8s_storage/zaiops_shared"
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: zaiops-keycloak-pv
  labels:
    type: local
spec:
  capacity:
    storage: 1G
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/usr/share/k8s_storage/zaiops_keycloak"
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: zaiops-kafkabroker-pv
  labels:
    type: local
spec:
  capacity:
    storage: 10G
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/usr/share/k8s_storage/zaiops_kafkabroker"
