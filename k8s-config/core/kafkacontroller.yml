apiVersion: apps/v1
kind: Deployment
metadata:
  name: zoa-kafkacontroller
  namespace: ${NAMESPACE}
spec:
  selector:
    matchLabels:
      app: zoa-kafkacontroller
  template:
    metadata:
      labels:
        app: zoa-kafkacontroller
        family: ${SUBDOMAIN}
    spec:
      hostname: kafkacontroller
      subdomain: ${SUBDOMAIN}
      containers:
      - name: zoa-kafkacontroller
        image: icr.io/zoa-oci/zoa-kafkabroker:${TAG}-x86_64
        imagePullPolicy: ${IMAGE_PULL_POLICY}
        env:
        - name: KAFKA_NODE_ROLE
          value: 'controller'
        - name: KAFKA_HEAP_OPTS
          value: -Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g
        - name: KAFKA_OPTS
          value: -Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache
        - name: ZAIOPS_KAFKA_PASS
          value: '${ZAIOPS_ZOASVC_PASS}'
        - name: ZAIOPS_KAFKA_RETENTION_HOURS
          value: '${ZAIOPS_KAFKA_RETENTION_HOURS}'
        - name: SSL_DEBUG
          value: '${SSL_DEBUG}'
        - name: SUBDOMAIN
          value: '${SUBDOMAIN}'
        # To change log level to debug, uncomments the next two lines.
        # Other possible values for log levels are TRACE DEBUG INFO WARN ERROR FATAL
        # - LOGGING_LEVEL_ROOT=DEBUG
        # - LOGGING_LEVEL_COM_IBM_ZSYSTEM_ZMANAGED_PIGATEWAY=DEBUG
        resources:
          limits:
            # minimum required
            # cpu: "500m"
            # memory: "1G"
            # actually available
            cpu: "500m"
            memory: "1G"
        volumeMounts:
          - name: zaiops-kafkabroker
            mountPath: /opt/kafka/data
          - name: zaiops-shared
            mountPath: /shared
            readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: [ "ALL" ]
            add: [ "NET_BIND_SERVICE" ]
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      imagePullSecrets:
      - name: ${REG_SECRET}
      volumes:
      - name: zaiops-kafkabroker
        persistentVolumeClaim:
          claimName: zaiops-kafkabroker-pvc
      - name: zaiops-shared
        persistentVolumeClaim:
          claimName: zaiops-shared-pvc
