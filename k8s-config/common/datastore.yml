apiVersion: apps/v1
kind: Deployment
metadata:
  name: zoa-datastore
  namespace: ${NAMESPACE}
spec:
  selector:
    matchLabels:
      app: zoa-datastore
  template:
    metadata:
      labels:
        app: zoa-datastore
        family: ${SUBDOMAIN}
    spec:
      hostname: datastore
      subdomain: ${SUBDOMAIN}
      containers:
      - name: zoa-datastore
        image: icr.io/zoa-oci/zoa-datastore:${TAG}-x86_64
        imagePullPolicy: ${IMAGE_PULL_POLICY}
        env:
        - name: discovery.type
          value: single-node
        - name: DATASTORE_HEAP
          value: '${ZAIOPS_DATASTORE_HEAP}'
        - name: ZAIOPS_GW_PASS
          value: '${ZAIOPS_ZOASVC_PASS}'
        - name: SSL_DEBUG
          value: '${SSL_DEBUG}'
        - name: EXTERNAL_GATEWAY_HOST
          value: '${EXTERNAL_GATEWAY_HOST}'
        - name: IZOA_GATEWAY_PORT
          value: '${IZOA_GATEWAY_PORT}'
        - name: ZAIOPS_KC_CONTEXT_ROOT
          value: '${ZAIOPS_KC_CONTEXT_ROOT}'
        - name: SUBDOMAIN
          value: '${SUBDOMAIN}'
        resources:
          limits:
            # minimum required
            # cpu: "3000m"
            # memory: "12G"
            # actually available
            cpu: "3000m"
            memory: "10G"
        volumeMounts:
          - name: zaiops-datastore
            mountPath: /usr/share/opensearch/data
          - name: zaiops-datastore-misc
            mountPath: /usr/share/os-misc
          - name: zaiops-datastore-snapshots
            mountPath: /usr/share/os-snapshots
          - name: zaiops-shared
            mountPath: /shared
            readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: [ "ALL" ]
            add: [ "NET_BIND_SERVICE" ]
          runAsNonRoot: true
          seccompProfile:
            type: RuntimeDefault
      imagePullSecrets:
      - name: ${REG_SECRET}
      volumes:
      - name: zaiops-datastore
        persistentVolumeClaim:
          claimName: zaiops-datastore-pvc
      - name: zaiops-datastore-misc
        persistentVolumeClaim:
          claimName: zaiops-datastore-misc-pvc
      - name: zaiops-datastore-snapshots
        persistentVolumeClaim:
          claimName: zaiops-datastore-snapshots-pvc
      - name: zaiops-shared
        persistentVolumeClaim:
          claimName: zaiops-shared-pvc
