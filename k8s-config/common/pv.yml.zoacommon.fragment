---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: zaiops-datastore-pv
  labels:
    type: local
spec:
  capacity:
    storage: 10G
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/usr/share/k8s_storage/zaiops_datastore"
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: zaiops-datastore-misc-pv
  labels:
    type: local
spec:
  capacity:
    storage: 1M
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/usr/share/k8s_storage/zaiops_datastore_misc"
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: zaiops-datastore-snapshots-pv
  labels:
    type: local
spec:
  capacity:
    storage: 1G
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/usr/share/k8s_storage/zaiops_datastore_snapshots"
