---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-datastore-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 10G
  accessModes:
    - ReadWriteOnce
  volumeName: zaiops-datastore-pv
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-datastore-misc-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 1M
  accessModes:
    - ReadWriteOnce
  volumeName: zaiops-datastore-misc-pv
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-datastore-snapshots-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 1G
  accessModes:
    - ReadWriteOnce
  volumeName: zaiops-datastore-snapshots-pv
