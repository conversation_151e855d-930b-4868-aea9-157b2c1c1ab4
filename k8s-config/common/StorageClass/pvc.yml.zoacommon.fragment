---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-datastore-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 10G
  accessModes:
    - ReadWriteMany
  storageClassName: rook-cephfs
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-datastore-misc-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 1M
  accessModes:
    - ReadWriteMany
  storageClassName: rook-cephfs
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zaiops-datastore-snapshots-pvc
  namespace: ${NAMESPACE}
spec:
  resources:
    requests:
      storage: 1G
  accessModes:
    - ReadWriteMany
  storageClassName: rook-cephfs
