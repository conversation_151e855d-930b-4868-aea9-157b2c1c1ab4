version: '3.3'
services:
  gateway:
    build:
      context: .
      dockerfile: docker/Dockerfile
    image: icr.io/zoa-oci/zoa-spring-cloud-gateway:1.0.0
    ports:
      - 8085:8085
    environment:
      - SERVER_SSL_ENABLED=true
      - SSL_DEBUG=${SSL_DEBUG}
    # - JAVA_OPTS=
    #    -DEUREKA_URI=http://discovery:8761/eureka
    #    -Djavax.net.debug=all   
    hostname: izoa.gateway
    container_name: izoa-gateway
    # Linux OS only !!!!
    #extra_hosts:
    #  - "host.docker.internal:host-gateway"
    # set host=host.docker.internal in .env on linux for xdebug
    #   - ${host:-host}:host-gateway
    networks:
      - izoa-pi-discovery-server_zaware
    volumes:  
      - ./src/main/resources/ssl/:/ssl
networks:
  izoa-pi-discovery-server_zaware:
    external: true
    
volumes: 
  ssl:
