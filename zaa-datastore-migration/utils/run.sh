#!/bin/bash

OPT=${1}
INSTDIR=${2}
if [ "${LMAI_INDEX_LIST}x" == "x" ]
then
  IDX_LIST="itoa-va-rarity-summary \
            itoa-va-rarity-model \
            loganomaly-usernotification \
            loganomaly-heartbeat \
            loganomaly-score-intervalanomaly \
            itoa-va-rarity-latest-model \
            loganomaly-score-intervalanomalysummary \
            itoa-zos-syslog-console"
else
  IDX_LIST="${LMAI_INDEX_LIST}"
fi

echo ""
if [ "${OPT}" == "EXPORT" ] || [ "${OPT}" == "DUMP" ]
then
  ACTION=exported
elif [ "${OPT}" == "IMPORT" ]
then
  ACTION=imported
fi
echo "INFO: The following indices will be ${ACTION}:"
for IDX in ${IDX_LIST}
do
  echo "  - ${IDX}"
done
echo ""
echo "This list can be controlled by exporting the environment variable LMAI_INDEX_LIST."
echo ""
read -n 1 -s -r -p "Press <Ctrl-C> to cancel or any other key to continue..."

ARCH=`uname -m`

importDerby() {
  ${OCI_AGENT} top ${LMAI_CONTAINER_PREFIX}derby 2>&1 | grep -q ^Error
  if [ $? -ne 0 ]
  then
    DERBYDOWN="FALSE"
    echo "SQL container '${LMAI_CONTAINER_PREFIX}derby' is running and will be shut down."
    ${OCI_AGENT} stop ${LMAI_CONTAINER_PREFIX}derby
    ${OCI_AGENT} rm ${LMAI_CONTAINER_PREFIX}derby
  fi
  ${OCI_AGENT} run --rm -d -t -u ${ZOA_UID}:0 \
    --name temp-derby --entrypoint /bin/bash \
    -v ${COMPOSE_PROJECT_NAME}_zaware_derby:/to \
    ibm-zaiops/logmsgml-derby:${TAG}-${ARCH} > /dev/null
  ${OCI_AGENT} cp ${ZAAPATH}/lmai_model_data.tar.gz temp-derby:/tmp/
  ${OCI_AGENT} exec temp-derby bash -c "rm -Rf /to/zAwareIOAz ; tar -C /to -xzf /tmp/lmai_model_data.tar.gz"
  ${OCI_AGENT} exec temp-derby bash -c "rm -f /tmp/lmai_model_data.tar.gz"
  ${OCI_AGENT} stop temp-derby > /dev/null 2>&1
  if [ "${DERBYDOWN}" == "FALSE" ]
  then
    echo "Please start SQL container '${LMAI_CONTAINER_PREFIX}derby' up again."
  fi
}

exportDerby() {
  if [ "${IFTAG}x" == "x" ]
  then
    # No 5.1.0.9a interim feature present
    DERBYTAG=${TAG}
  else
    DERBYTAG=${IFTAG}
  fi
  ${OCI_AGENT} run --rm -d -t -u ${ZOA_UID}:0 \
    --name temp-derby --entrypoint /bin/bash \
    -v ${COMPOSE_PROJECT_NAME}_zaware_derby:/from:ro \
    ibm-zaiops/logmsgml-derby:${DERBYTAG}-${ARCH} > /dev/null
  ${OCI_AGENT} exec temp-derby bash -c "tar -C /from -czf /tmp/lmai_model_data.tar.gz zAwareIOAz"
  ${OCI_AGENT} cp temp-derby:/tmp/lmai_model_data.tar.gz ${ZAAPATH}/
  ${OCI_AGENT} exec temp-derby bash -c "rm -f /tmp/lmai_model_data.tar.gz"
  ${OCI_AGENT} stop temp-derby > /dev/null 2>&1
}

startDatastore() {
  DS_CONTAINER=${1}
  LEGACY_DS=${2}
  echo "Starting temporary datastore container with legacy datastore volume '${LEGACY_DS}'..."
  ${OCI_AGENT} run --rm -u ${ZOA_UID}:0 \
    --name ${DS_CONTAINER} -d \
    --security-opt no-new-privileges \
    -e discovery.type=single-node \
    -e DATASTORE_HEAP=${ZAIOPS_DATASTORE_HEAP} \
    -v ${LEGACY_DS}:/usr/share/opensearch/data \
    ibm-zaiops/zoa-datastore:${TAG}-${ARCH} > /dev/null  
}

checkDatastore() {
  DS_CONTAINER=${1}
  # Check whether datastore is even running
  ${OCI_AGENT} top ${DS_CONTAINER} 2>&1 | grep -q ^Error
  if [ $? -eq 0 ]
  then
    DSDOWN="TRUE"
    echo "Datastore container '${DS_CONTAINER}' is not running."
  else
    # Sleep for up to 300 seconds to give datastore a chance to come up
    DSREADY="FALSE"
    echo -n "Waiting for datastore service to be ready "
    count=0
    while :
    do
      echo -n .
      DSSTAT=`${OCI_AGENT} exec ${DS_CONTAINER} curl http://localhost:9200/_cluster/health?pretty 2>/dev/null | grep status`
      echo ${DSSTAT} | grep -qE 'green|yellow'
      if [ $? -eq 0 ]
      then
        echo -n " "
        echo "successful after ${count} seconds."
        DSREADY="TRUE"
        break
      elif [ ${count} -ge 300 ]
      then
        echo -n " "
        echo "still not ready after ${count} seconds; giving up."
        break
      else
        sleep 5
        count=$(( ${count} + 5 ))
      fi
    done
  fi
}

getInstallDir() {
  TYPE=${1}
  echo ""
  if [ "${TYPE}" == "LEGACY" ]
  then
    echo "Provide the directory in which the legacy Z Anomaly Analytics (prior to Fix Pack ********) can be found:"
  elif [ "${TYPE}" == "CURRENT" ]
  then
    echo "Provide the directory in which the current Z Anomaly Analytics (Fix Pack ******** or later) can be found:"
  fi
  read -e ZAAPATH
  if [ ! -f ${ZAAPATH}/logmsgml-docker-compose.yml ]
  then
    echo ""
    echo "ERROR: logmsgml-docker-compose.yml not found in directory '${ZAAPATH}'."
    echo "       This directory does not contain a valid installation of the Log Based Machine Learning feature."
    echo ""
    exit 1
  else
    if [ "${TYPE}" == "LEGACY" ]
    then
      if [ -f ${ZAAPATH}/.env ]
      then
        set -a
        . ${ZAAPATH}/.env
        set +a
      else
        echo ""
        echo "ERROR: Z Anomaly Analytics configuration file not found in directory '${ZAAPATH}'."
        echo ""
        exit 1
      fi
    elif [ "${TYPE}" == "CURRENT" ]
    then
      if [ -f ${ZAAPATH}/zoa_env.config ]
      then
        set -a
        . ${ZAAPATH}/zoa_env.config
        . ${ZAAPATH}/.zoa_factory.config
        set +a
        if [ "${OCI_AGENT}" == "docker" ]
        then
          COMMON_CONTAINER_PREFIX=zoa-
          LMAI_CONTAINER_PREFIX=logmsgml-
        elif [ "${OCI_AGENT}" == "podman" ]
        then
          COMMON_CONTAINER_PREFIX=""
          LMAI_CONTAINER_PREFIX=""
        fi
      else
        echo ""
        echo "ERROR: Z Anomaly Analytics configuration file not found in directory '${ZAAPATH}'."
        echo ""
        exit 1
      fi
    fi
  fi
}

if [ "${OPT}" == "EXPORT" ]
then
  getInstallDir LEGACY
  LEGACY_DS=`${OCI_AGENT} volume ls -q | grep ${COMPOSE_PROJECT_NAME}_zaware_elastic`
  echo "This process will export prior log-based machine learning score results and -- optionally -- model data."
  echo "Do you want to export log-based machine learning model data? (Y/n)"
  read -e EXPORTMODEL
  EXPORTMODEL=${EXPORTMODEL:-"y"}   # accept no input as "YES"
  if [ "${EXPORTMODEL}" = "Y" ] || [ "${EXPORTMODEL}" = "y" ]
  then
    exportDerby
  fi
  # Don't do anything if there is no legacy datastore volume
  if [ $? -ne 0 ]
  then
    echo ""
    echo "INFO: No legacy datastore volume found."
    echo ""
    exit 0
  fi
  checkDatastore logmsgml-elasticsearch
  if [ "${DSDOWN}" == "TRUE" ] || [ "${DSREADY}" == "FALSE" ]
  then
    echo "ERROR: Datastore container not in ready state. Unable to proceed."
    echo ""
    exit 1
  else
    ${OCI_AGENT} exec logmsgml-elasticsearch bash -c "mkdir -p /tmp/dump"
    ${OCI_AGENT} cp ${INSTDIR}/lib/zaa-datastore-migration.jar logmsgml-elasticsearch:/tmp/dump
    for IDX in ${IDX_LIST}
    do
      echo ${IDX} | grep -q ^itoa-
      if [ $? -eq 0 ]
      then
        OLDIDX=`echo ${IDX} | sed -e "s%^itoa-%zoa-%g"`
        echo "Locking legacy index ${OLDIDX}..."
        ${OCI_AGENT} exec logmsgml-elasticsearch bash -c "curl -XPUT http://localhost:9200/${OLDIDX}/_settings -H 'Content-Type: application/json' -d '{ \"settings\": { \"index.blocks.write\": true } }'" > /dev/null 2>&1
        echo "Cloning legacy index ${OLDIDX} into ${IDX}..."
        ${OCI_AGENT} exec logmsgml-elasticsearch bash -c "curl -XPOST http://localhost:9200/${OLDIDX}/_clone/${IDX}" > /dev/null 2>&1
      fi
    done
    ${OCI_AGENT} exec logmsgml-elasticsearch bash -c "cd /tmp/dump ; for IDX in ${IDX_LIST} ; do echo Exporting index \'\${IDX}\'... ; java -jar /tmp/dump/zaa-datastore-migration.jar exporttofile -i localhost:9200 -d /tmp/dump -x \${IDX} ; done ; tar --numeric-owner --owner=0 --group=0 -cf idx_dump.tar *.json.gz"
    ${OCI_AGENT} cp logmsgml-elasticsearch:/tmp/dump/idx_dump.tar ${ZAAPATH}/
    ${OCI_AGENT} exec logmsgml-elasticsearch bash -c "rm -Rf /tmp/dump"
  fi
elif [ "${OPT}" == "IMPORT" ]
then
  getInstallDir CURRENT
  CURRENT_DS=`${OCI_AGENT} volume ls -q | grep ${COMPOSE_PROJECT_NAME}_zaiops_datastore`
  echo "This process will import prior log-based machine learning score results and -- optionally -- model data."
  echo "Do you want to import log-based machine learning model data? (Y/n)"
  read -e IMPORTMODEL
  IMPORTMODEL=${IMPORTMODEL:-"y"}   # accept no input as "YES"
  if [ "${IMPORTMODEL}" = "Y" ] || [ "${IMPORTMODEL}" = "y" ]
  then
    echo "CAUTION: Importing model data is a DESTRUCTIVE action. All existing data in Volume '${COMPOSE_PROJECT_NAME}_zaware_derby' will be overwritten."
    echo -n "         Are you sure you want to continue? (y/N)  "
    read -e RESTOREVOL
    RESTOREVOL=${RESTOREVOL:-"n"}
    RESTOREVOL=`echo ${RESTOREVOL} | tr [:upper:] [:lower:]`
    if [ "${RESTOREVOL}" == "y" ]
    then
      importDerby
    fi
  fi
  # Don't do anything if there is no current datastore volume
  if [ $? -ne 0 ]
  then
    echo ""
    echo "INFO: No current datastore volume found."
    echo ""
    exit 0
  fi
  checkDatastore ${COMMON_CONTAINER_PREFIX}datastore
  if [ "${DSDOWN}" == "TRUE" ] || [ "${DSREADY}" == "FALSE" ]
  then
    echo "ERROR: Datastore container not in ready state. Unable to proceed."
    echo ""
    exit 1
  else
    ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}datastore bash -c "mkdir -p /tmp/dump"
    ${OCI_AGENT} cp ${INSTDIR}/lib/zaa-datastore-migration.jar ${COMMON_CONTAINER_PREFIX}datastore:/tmp/dump
    ${OCI_AGENT} cp ${ZAAPATH}/idx_dump.tar ${COMMON_CONTAINER_PREFIX}datastore:/tmp/dump
    ${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}datastore bash -c "cd /tmp/dump ; tar xf idx_dump.tar ; rm -f idx_dump.tar ; for IDX in ${IDX_LIST} ; do echo Processing index \'\${IDX}\'... ; java -jar /tmp/dump/zaa-datastore-migration.jar importtodatastore -s /tmp/dump -o datastore:9200 -x \${IDX} ; rm -f \${IDX}*.json.gz ; done ; cd - ; rm -Rf /tmp/dump"
  fi
elif [ "${OPT}" == "DUMP" ]
then
  getInstallDir CURRENT
  LEGACY_DS=`docker volume ls -q | grep zaware_elastic$`
  echo "This process will export prior log-based machine learning score results and -- optionally -- model data."
  echo "Do you want to export log-based machine learning model data? (Y/n)"
  read -e EXPORTMODEL
  EXPORTMODEL=${EXPORTMODEL:-"y"}   # accept no input as "YES"
  if [ "${EXPORTMODEL}" = "Y" ] || [ "${EXPORTMODEL}" = "y" ]
  then
    exportDerby
  fi
  DS_UP=false
  # Don't do anything if there is no legacy datastore volume
  if [ $? -ne 0 ]
  then
    echo ""
    echo "INFO: No legacy datastore volume found."
    echo ""
    exit 0
  fi
  startDatastore temp-datastore ${LEGACY_DS}
  checkDatastore temp-datastore
  if [ "${DSDOWN}" == "TRUE" ] || [ "${DSREADY}" == "FALSE" ]
  then
    echo "ERROR: Datastore container not in ready state. Unable to proceed."
    echo ""
    exit 1
  else
    ${OCI_AGENT} exec temp-datastore bash -c "mkdir -p /tmp/dump"
    ${OCI_AGENT} cp ${INSTDIR}/lib/zaa-datastore-migration.jar temp-datastore:/tmp/dump
    for IDX in ${IDX_LIST}
    do
      echo ${IDX} | grep -q ^itoa-
      if [ $? -eq 0 ]
      then
        OLDIDX=`echo ${IDX} | sed -e "s%^itoa-%zoa-%g"`
        echo "Locking legacy index ${OLDIDX}..."
        ${OCI_AGENT} exec temp-datastore bash -c "curl -XPUT http://localhost:9200/${OLDIDX}/_settings -H 'Content-Type: application/json' -d '{ \"settings\": { \"index.blocks.write\": true } }'" > /dev/null 2>&1
        echo "Cloning legacy index ${OLDIDX} into ${IDX}..."
        ${OCI_AGENT} exec temp-datastore bash -c "curl -XPOST http://localhost:9200/${OLDIDX}/_clone/${IDX}" > /dev/null 2>&1
      fi
    done 
    ${OCI_AGENT} exec temp-datastore bash -c "cd /tmp/dump ; for IDX in ${IDX_LIST} ; do echo Processing index \'\${IDX}\'... ; java -jar /tmp/dump/zaa-datastore-migration.jar exporttofile -i localhost:9200 -d /tmp/dump -x \${IDX} ; done ; tar --numeric-owner --owner=0 --group=0 -cf idx_dump.tar *.json.gz"
    ${OCI_AGENT} cp temp-datastore:/tmp/dump/idx_dump.tar ${ZAAPATH}/
    ${OCI_AGENT} exec temp-datastore bash -c "rm -Rf /tmp/dump"
    ${OCI_AGENT} stop temp-datastore > /dev/null 2>&1
  fi
else
  echo "INFO: Unknown option received; doing nothing."
fi
