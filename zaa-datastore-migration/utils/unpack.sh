#!/bin/bash

usage() {
  echo ""
  echo "Usage:"
  echo "  $0 [ --export | -e ]                Export Log Message Machine Learning data from an operational"
  echo "                                                                 legacy Z Anomaly Analytics datastore"
  echo "  $0 [ --import | -i ]                Import legacy Log Message Machine Learning data into an operational"
  echo "                                                                 current common datastore"
  echo "  $0 [ --dump | -d ]                  Dump legacy Log Message Machine Learning datastore volume if the"
  echo "                                                                 the legacy datastore service is no longer operational"
  echo ""
}

OPT=$1
HERE=`pwd`
SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
DATE=`date +"%Y%m%d-%H%M%S"`
if [ ! "${TMPDIR}x" = "x" ] && [ -w ${TMPDIR} ]
then
  # TMPDIR is set and is writable
  INSTDIR=${TMPDIR}
elif [ -w /tmp ]
then
  INSTDIR=/tmp
else
  INSTDIR=${SCRIPTDIR}
fi

INSTDIR=${INSTDIR}/datastore-migration.${DATE}
mkdir -p ${INSTDIR}

ARCHIVE=`awk '/^__ARCHIVE_BELOW__/ {print NR + 1; exit 0; }' $0`

tail -n+$ARCHIVE $0 | tar -C ${INSTDIR} -xzf -

case "${OPT}" in
  --export | -e )
      cd ${INSTDIR} && ./run.sh EXPORT ${INSTDIR}
      ;;
  --import | -i )
      cd ${INSTDIR} && ./run.sh IMPORT ${INSTDIR}
      ;;
  --dump | -d )
      cd ${INSTDIR} && ./run.sh DUMP ${INSTDIR}
      ;;
  * )
      usage
      ;;
esac

cd ${HERE}
rm -Rf ${INSTDIR}

exit 0

__ARCHIVE_BELOW__
