package com.zaa.migration.service;

import java.net.URI;
import java.net.URISyntaxException;
import java.security.GeneralSecurityException;
import java.util.concurrent.TimeUnit;

import org.eclipse.jetty.client.HttpClient;
import org.eclipse.jetty.client.HttpProxy;
import org.eclipse.jetty.client.AuthenticationStore;
import org.eclipse.jetty.client.ContentResponse;
import org.eclipse.jetty.client.transport.HttpClientTransportDynamic;
import org.eclipse.jetty.client.BasicAuthentication;
import org.eclipse.jetty.client.StringRequestContent;
import org.eclipse.jetty.http.HttpMethod;
import org.eclipse.jetty.io.ClientConnector;
import org.eclipse.jetty.util.ssl.SslContextFactory;
import org.json.JSONObject;

import com.zaa.migration.exception.MigrationException;
import com.zaa.migration.exception.RetriableException;

import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.LogManager;

public class DataStoreWriter {
  private static final Logger log = LogManager.getLogger(DataStoreWriter.class);

  private boolean connected = false;

  // Retry intervals for connection to server - up to 1 minute which then
  // continues forever.
  private static final long reconnectDelayMs[] = { 0, 100, 500, 1000, 2000, 4000, 8000, 30000, 60000 };
  private int reconnectDelayIndex = 0;
  private boolean errorMaxDelay = false;

  private String userid = null;
  private String password = null;
  private String connection;
  private URI uri = null;

  private String keyStore = null;
  private String keyStorePassword = null;
  private String trustStore = null;
  private String trustStorePassword = null;

  private String protocol = "http";
  private String proxyHost = null;
  private int proxyPort = DEFAULT_HTTP_PROXY_PORT;

    public static final int DEFAULT_JETTY_MAX_CONNECTIONS = 5;
    public static final int DEFAULT_JETTY_IDLE_TIMEOUT_SEC = 30;
    public static final int DEFAULT_JETTY_CONNECTION_TIMEOUT_SEC = 10;
    public static final int DEFAULT_JETTY_OPERATION_TIMEOUT_SEC = 60; 
    public static final int DEFAULT_MAX_COMMIT_FAILURES = 5;
    public static final int DEFAULT_HTTP_PROXY_PORT = 8080;
  
  private int jettyMaxConnections = DEFAULT_JETTY_MAX_CONNECTIONS;
  private int jettyIdleTimeoutSec = DEFAULT_JETTY_MAX_CONNECTIONS;
  private int jettyConnectionTimeoutSec = DEFAULT_JETTY_CONNECTION_TIMEOUT_SEC;
  private int jettyOperationTimeoutSec = DEFAULT_JETTY_OPERATION_TIMEOUT_SEC;
  private int maxCommitFailures = DEFAULT_MAX_COMMIT_FAILURES; 

  private String destination;
  private StringBuffer bulkMsg = new StringBuffer();

  private int commitFailures = 0; // This is only updated during commit by a single thread. No locking needed.

  private HttpClient httpClient;

  public void configure(String host) {
    connection = host;
  }

  public void connect() {
    setupConnection();

    while (!connected) {
      try {
        connectInternal(true);
      } catch (Exception e) {
        log.error("Cannot connect to Datastore server: {}", e.getMessage());
        if (!(e instanceof RetriableException))
          throw new MigrationException(e);
      }
    }
  }

  public void send(String index, String id, String jsonString, String idPostfix) {
    JSONObject line = new JSONObject();
    JSONObject label = new JSONObject();

    // If the document is not empty, add it to the index
    if ((jsonString != null) && !jsonString.isEmpty()) {
      label.put("_index", index);
      label.put("_id", id + idPostfix);
      line.put("index", label);
      bulkMsg.append(line.toString());
      bulkMsg.append("\n");

      bulkMsg.append(jsonString);
      bulkMsg.append("\n");
    }
  }

  public boolean commit() throws MigrationException {
    if (bulkMsg.length() == 0) {
      return true;
    }
    
    connectInternal(false);

    destination = uri + "/_bulk";
    ContentResponse response = null;
    try {
      response = httpClient.newRequest(destination).timeout(jettyOperationTimeoutSec, TimeUnit.SECONDS)
          .method(HttpMethod.POST).body(new StringRequestContent("application/json", bulkMsg.toString()))
          .send();

      int status = (response != null) ? response.getStatus() : -100;
      String responseString = (response != null) ? response.toString() : "UNKNOWN";
      log.debug("Bulk insert returned {} {}", status, responseString);

      if (status < 200 || status > 299) {
        throw new RetriableException(responseString);
      }

      // After a success, reset the number of failures.
      commitFailures = 0;
      bulkMsg.delete(0, bulkMsg.length());
      return true;
    } catch (Exception e) {
      log.error("Error commiting records to Datastore.", e);
      commitFailures++;
      //bulkMsg.delete(0, bulkMsg.length());

      if (commitFailures > maxCommitFailures) {
        throw new MigrationException("Reached maximum failures of commit processing. Last error: " + e.getMessage());
      } else {
        throw new RetriableException(e.getMessage());
      }
    }
  }
  
  public void delete(String index, String id, String idPostfix) {
    JSONObject line = new JSONObject();
    JSONObject label = new JSONObject();
    label.put("_index", index);
    label.put("_id", id + idPostfix);
    line.put("delete", label);
    bulkMsg.append(line.toString());
    bulkMsg.append("\n");
  }

  public void close() {
    try {
      connected = false;
      if (httpClient != null) {
        httpClient.stop();
      }
    } catch (Exception e) {
      ;
    } finally {
      httpClient = null;
      log.info("Connection to Datastore closed.");
    }
  }

  protected void connectInternal(boolean initialConnection) throws MigrationException {
    if (connected) {
      return;
    }

    if (httpClient == null) {
      httpClient = setupConnection();
    }

    try {
      httpClient.start();

      // Doing a health check. We don't care about the return information
      // only that the request succeeds. This is done synchronously. This particular
      // URL is documented as giving some build information about the Datastore
      // server.
      String healthDestination = uri + "/_cat/health";

      ContentResponse response = httpClient.newRequest(healthDestination)
          .timeout(jettyConnectionTimeoutSec, TimeUnit.SECONDS).method(HttpMethod.GET).send();
      int status = response.getStatus();

      log.debug("Connection test returned {}.", response.toString());

      // 4xx errors including 404 (page not found) are treated as immediately fatal as
      // it
      // suggests either a failed server or a bad configuration for this connector.
      if (status >= 400 && status <= 499)
        throw new GeneralSecurityException(response.getReason());
      else if (status < 200 || status > 299) // 2xx codes are success
        throw new RetriableException(response.getReason());

      log.info("Connection to Datastore established.");
      reconnectDelayIndex = 0;
      errorMaxDelay = false;
      connected = true;
    } catch (Exception e) {
      int idx = reconnectDelayIndex;

      // Try to connect forever (or until some higher layer gives up). The
      // delay array has a set of values to sleep for until we reach the
      // maximum delay. And then we continue using that final delay value.
      if (idx >= reconnectDelayMs.length) {
        idx = reconnectDelayMs.length - 1;
        // Give an error message once
        if (!errorMaxDelay) {
          log.error("Maximum connection delay value reached. Continuing to try connections...");
          errorMaxDelay = true;
        }
      }
      try {
        Thread.sleep(reconnectDelayMs[idx]);
        reconnectDelayIndex++;
      } catch (InterruptedException ie) {
        ;
      }

      throw handleException(e, initialConnection);
    }
  }

  private MigrationException handleException(Exception e, boolean initialConnection) {
    boolean isRetriable = false;
    boolean mustClose = true;

    log.info("Exception {} needs to be handled. ReconnectCount {}", e.getMessage(), reconnectDelayIndex);

    if (e instanceof GeneralSecurityException) {
      isRetriable = false;
      mustClose = true;
    } else {
      isRetriable = true;
      mustClose = false;
    }

    if (mustClose) {
      close();
    }

    if (isRetriable) {
      return new RetriableException(e);
    }

    return new MigrationException(e);
  }

  /*
   * Initialise the HTTP Client object with necessary configuration including
   * authentication, TLS options and any defined Jetty tuning parameters.
   */
  private HttpClient setupConnection() throws MigrationException  {
    SslContextFactory.Client sslContextFactory = new SslContextFactory.Client();

    // Point at the keystore and truststore. The passwords
    // are only set if necessary, as a default truststore may not be protected with
    // password.
    if (notNullOrEmpty(keyStore)) {
      protocol = "https";
      sslContextFactory.setKeyStorePath(keyStore);
      if (notNullOrEmpty(keyStorePassword)) {
        sslContextFactory.setKeyStorePassword(keyStorePassword);
      }
    }
    if (notNullOrEmpty(trustStore)) {
      protocol = "https";
      sslContextFactory.setTrustStorePath(trustStore);
      if (notNullOrEmpty(trustStorePassword)) {
        sslContextFactory.setTrustStorePassword(trustStorePassword);
      }
    }

    try {
      uri = new URI(protocol + "://" + connection);
    } catch (URISyntaxException e) {
      log.error("Invalid URI {}", uri.toString());
      throw new MigrationException(e);
    }

    // Force the use of TLSv1.2 or later
    // Set the Protocols and CipherSuites that are permitted
    setDefaults(sslContextFactory);

    ClientConnector clientConnector = new ClientConnector();
    clientConnector.setSslContextFactory(sslContextFactory);

    if (protocol.equals("https"))
      httpClient = new HttpClient(new HttpClientTransportDynamic(clientConnector));
    else
      httpClient = new HttpClient();

    // Authentication using userid/password is enabled here
    if (notNullOrEmpty(userid)) {
      AuthenticationStore auth = httpClient.getAuthenticationStore();
      auth.addAuthenticationResult(new BasicAuthentication.BasicResult(uri, userid, password));
    }

    // Tuning parameters for Jetty connections.
    int maxConnections = jettyMaxConnections;
    log.debug("Setting HTTP maxConnections to {}.", maxConnections);
    httpClient.setMaxConnectionsPerDestination(maxConnections);

    // Setting an idle timeout can reduce the number of active threads/connections
    // when set to non-zero value
    int idleTimeout = jettyIdleTimeoutSec;
    if (idleTimeout > 0) {
      log.debug("Setting idleTimeout to {} seconds", idleTimeout);
      httpClient.setIdleTimeout((long) (idleTimeout * 1000)); // Be explicit about casting to API datatype
    }

    // How long to wait for the server to respond during initial connection
    httpClient.setConnectTimeout(jettyConnectionTimeoutSec * 1000);

    setProxy(httpClient);

    return httpClient;
  }

  // set HTTP proxy settings
  private void setProxy(HttpClient httpClient) {
    if (proxyHost != null) {
      HttpProxy proxy = new HttpProxy(proxyHost, proxyPort);
      httpClient.getProxyConfiguration().getProxies().add(proxy);
    }
  }

  // Jetty 9.4.11 disables all ciphers beginning "SSL_" but when running under the
  // IBM JRE it
  // has the effect of removing ALL the ciphersuites because that JRE has a
  // different naming
  // pattern for the Ciphers.
  //
  // So we cannot rely on the Jetty default behaviour and if we can tell we're in
  // the IBM JRE we instead copy the patterns that Jetty disables except for one
  // overreaching expression.
  private void setDefaults(SslContextFactory sslContextFactory) {
    // Only support TLS 1.2
    String protocols[] = new String[] { "TLSv1.2" };
    sslContextFactory.setIncludeProtocols(protocols);

    String vendor = System.getProperty("java.vendor");
    if (vendor != null && vendor.toUpperCase().contains("IBM")) {
      log.debug("Doing manual exclusion of ciphersuites.");

      // Exclude weak / insecure ciphers
      sslContextFactory.setExcludeCipherSuites("^.*_(MD5|SHA|SHA1)$");
      // Exclude ciphers that don't support forward secrecy
      sslContextFactory.addExcludeCipherSuites("^TLS_RSA_.*$");

      // The Jetty code uses the simple SSL_.* pattern, but this pattern has a similar
      // effect
      // for the IBM JRE which uses 'SSL' instead of 'TLS' in many of the canonical
      // cipher names
      sslContextFactory.addExcludeCipherSuites("^SSL_RSA_.*$");

      // Exclude NULL ciphers (that are accidentally present due to Include patterns)
      sslContextFactory.addExcludeCipherSuites("^.*_NULL_.*$");
      // Exclude anon ciphers (that are accidentally present due to Include patterns)
      sslContextFactory.addExcludeCipherSuites("^.*_anon_.*$");
    }

    return;
  }

  boolean isNullOrEmpty(String s) {
    if (s == null || s.isEmpty())
      return true;
    return false;
  }

  boolean notNullOrEmpty(String s) {
    return !isNullOrEmpty(s);
  }
}
