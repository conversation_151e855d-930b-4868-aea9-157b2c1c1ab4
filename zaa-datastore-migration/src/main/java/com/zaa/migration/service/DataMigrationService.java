package com.zaa.migration.service;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.FileInputStream; 
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import org.apache.http.HttpHost;
import org.json.JSONObject;
import org.opensearch.action.DocWriteResponse.Result;
import org.opensearch.action.delete.DeleteRequest;
import org.opensearch.action.delete.DeleteResponse;
import org.opensearch.action.search.ClearScrollRequest;
import org.opensearch.action.search.ClearScrollResponse;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.action.search.SearchScrollRequest;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestClient;
import org.opensearch.client.RestClientBuilder;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.indices.GetIndexRequest;
import org.opensearch.common.Strings;
import org.opensearch.common.unit.TimeValue;
import org.opensearch.search.Scroll;
import org.opensearch.search.SearchHit;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.LogManager;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zaa.migration.exception.MigrationException;
import com.zaa.migration.exception.RetriableException;

import java.io.File;
import java.io.FileNotFoundException;

public class DataMigrationService {

  private static final Logger log = LogManager.getLogger(DataMigrationService.class);

  private int totCount;

  public void exportToFile(String ihostname, String iport, String outputFolder, String index, int size,
      int commitSize) throws Exception {
    downloadData(ihostname, iport, null, null, index, outputFolder, size, commitSize, false);
  }

  public void exportToDatastore(String ihostname, String iport, String ohostname, String oport, String index,
      int size, int commitSize) throws Exception {
    downloadData(ihostname, iport, ohostname, oport, index, null, size, commitSize, true);
  }

  private void downloadData(String ihost, String iport, String ohost, String oport, String indices,
      String outputFolder, int size, int commitSize, boolean writeToDataStore) throws Exception {

    RestClientBuilder builder = RestClient.builder(new HttpHost(ihost, Integer.parseInt(iport)));
    try (RestHighLevelClient client = new RestHighLevelClient(builder)) {

      // Request request = new Request("GET", "/_cat/indices?h=i&index=" + idx);
      // InputStream inputStream =
      // client.getLowLevelClient().performRequest(request).getEntity().getContent();
      // List<String> indexes = new BufferedReader(new
      // InputStreamReader(inputStream)).lines()
      // .collect(Collectors.toList());

      DataStoreWriter writer = null;
      if (writeToDataStore) {
        writer = new DataStoreWriter();
        writer.configure(ohost + ":" + oport);
        writer.connect();
      }

      List<String> indexes = Arrays.stream(indices.split(",")).map(String::trim).collect(Collectors.toList());
      for (String index : indexes) {

        totCount = 0;
        log.info("Getting data for index {}.", index);

        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.scroll(scroll);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(size);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = null;
        try {
          searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
          log.error(e.getMessage());
          continue;
        }
        String scrollId = searchResponse.getScrollId();
        log.info("{} - Total records in index {}", searchResponse.getHits().getTotalHits().value, index);

        SearchHit[] searchHits = searchResponse.getHits().getHits();
        while (searchHits != null && searchHits.length > 0) {
          if (writeToDataStore) {
            writeToDataStore(writer, searchHits, commitSize);
          } else {
            writeToFiles(searchHits, outputFolder, index);
          }
          SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
          scrollRequest.scroll(scroll);
          searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
          scrollId = searchResponse.getScrollId();
          searchHits = searchResponse.getHits().getHits();
        }

        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        ClearScrollResponse clearScrollResponse = client.clearScroll(clearScrollRequest,
            RequestOptions.DEFAULT);
        boolean succeeded = clearScrollResponse.isSucceeded();
        if (succeeded) {
          log.debug("{} Scroll closed.", scrollId);
        }
      }

      if (writer != null) {
        writer.close();
      }
      writer = null;
    }
  }

  private void writeToFiles(SearchHit[] searchHits, String outputFolder, String index) {
    int count = totCount + 1;

    PrintWriter pw = null;
    FileOutputStream fileStream;
    GZIPOutputStream gzipStream;
    OutputStreamWriter oswriter;
    File file;
    String filename = index + "-data-" + count + ".json.gz";
    try {
      file = new File(outputFolder + System.getProperty("file.separator") + filename);
      fileStream = new FileOutputStream(file);
      gzipStream = new GZIPOutputStream(fileStream);
      oswriter = new OutputStreamWriter(gzipStream, "UTF-8");
      pw = new PrintWriter(oswriter);

      for (SearchHit hit : searchHits) {
        String hello = Strings.toString(hit, false, true);
        pw.write(hello + System.lineSeparator());
        totCount++;
      }

      pw.close();
      oswriter.close();
      gzipStream.close();
      fileStream.close();

      log.info("{}-{} - Records written to file {}", count, totCount, filename);
    } catch (IOException e) {
      log.error("Error encountered for " + filename);
    }
  }

  @SuppressWarnings("unchecked")
  private void writeToDataStore(DataStoreWriter writer, SearchHit[] searchHits, int commitSize) {
    int commitCount = 0;
    for (SearchHit hit : searchHits) {
      String j = Strings.toString(hit, false, true);

      ObjectMapper mapper = new ObjectMapper();
      Map<String, Object> h;
      try {
        h = mapper.readValue(j, Map.class);
        JSONObject json = new JSONObject(j);
        writer.send(h.get("_index").toString(), h.get("_id").toString(), json.get("_source").toString(), "!dm");
        commitCount++;
        totCount++;
      } catch (JsonMappingException e) {
        log.error("JSON mapping error for record {}. Skipping record.", j);
      } catch (JsonProcessingException e) {
        log.error("JSON processing error for record {}. Skipping record.", j);
      }

      if (commitCount > 0 && (commitCount % commitSize) == 0) {
        commitData(writer);
        log.info("{} - Records committed to datastore.", totCount);
        commitCount = 0;
      }
    }

    if (commitCount > 0) {
      commitData(writer);
      log.info("{} - Records committed to datastore.", totCount);
      commitCount = 0;
    }
  }

  @SuppressWarnings("unchecked")
  public void importToDataStore(String host, String port, String inputFolder, String index, int commitSize) {
    File dir = new File(inputFolder);
    if (!dir.isDirectory()) {
      log.info("Input folder is not a directory.");
      return;
    }

    List<String> indices = Arrays.stream(index.split(",")).map(String::trim).collect(Collectors.toList());
    List<String> indexes = new ArrayList<String>();
    RestClientBuilder builder = RestClient.builder(new HttpHost(host, Integer.parseInt(port)));
    try (RestHighLevelClient client = new RestHighLevelClient(builder)) {
        for(String i : indices) {  
        GetIndexRequest request = new GetIndexRequest(i);
          boolean isExists = client.indices().exists(request, RequestOptions.DEFAULT);
          if (isExists) {
            indexes.add(i);
          } else {
            log.error("{} Index doesn't exist in datastore. Will skip importing data for this index. ", i);
          }
        }
    } catch (Exception e) {
        log.error("{} Error checking if index exists.");
        return;
    }
    if (indexes.size() <= 0) {
      log.info("Indices list is empty.");
      return;
    }

    for (String idx : indexes) {
      File[] files = dir.listFiles(f -> f.getName().matches(idx + ".*\\.json\\.gz"));

      DataStoreWriter writer;
      writer = new DataStoreWriter();
      writer.configure(host + ":" + port);
      writer.connect();

      int totalRecordsMigrated = 0;
      for (File filename : files) {
        if (!filename.isDirectory()) {

          log.info("Reading JSON records from file {}.", filename.getName());
          int fileRecordCount = 0;
          FileInputStream fileStream;
          GZIPInputStream gzipStream;
          Scanner scanner;
          try {
            fileStream = new FileInputStream(filename);
            gzipStream = new GZIPInputStream(fileStream);
            scanner = new Scanner(gzipStream, "UTF-8");
            int fileCommitCount = 0;
            int commitCount = 0;
            while (scanner.hasNextLine()) {
              fileRecordCount++;
              String j = scanner.nextLine();

              ObjectMapper mapper = new ObjectMapper();
              Map<String, Object> h;
              try {
                h = mapper.readValue(j, Map.class);
                JSONObject json = new JSONObject(j);

                if (indexes.contains(h.get("_index").toString())) {
                  writer.send(h.get("_index").toString(), h.get("_id").toString(),
                      json.get("_source").toString(), "!dm");
                  //writer.delete(h.get("_index").toString(), h.get("_id").toString(), "!dm");
                  totalRecordsMigrated++;
                  fileCommitCount++;
                  commitCount++;
                }
              } catch (JsonMappingException e) {
                log.error("JSON mapping error for record {}. Skipping record.", j);
              } catch (JsonProcessingException e) {
                log.error("JSON processing error for record {}. Skipping record.", j);
              } catch (Exception e) {
                log.error("General processing error for record {}. Skipping record.", j);
              }

              if (commitCount > 0 && (commitCount % commitSize) == 0) {
                commitData(writer);
                log.info("{} - Records committed to datastore.", fileCommitCount);
                commitCount = 0;
              }
            }
            scanner.close();
            gzipStream.close();
            fileStream.close();
            if (commitCount > 0) {
              commitData(writer);
              log.info("{} - Records committed to datastore.", fileCommitCount);
              commitCount = 0;
            }
            log.info("{} - Records read from file {}.", fileRecordCount, filename.getName());
            log.info("{} - Records committed from file {}.", fileCommitCount, filename.getName());

          } catch (IOException e) {
            log.error("Error encountered for " + filename.getName());
          }
        }
      }
      if (writer != null) {
        writer.close();
      }
      writer = null;
      log.info("{} - Total records migrated to datastore for Index {}.", totalRecordsMigrated, idx);
    }
  }

  private void commitData(DataStoreWriter writer) {
    boolean commit = false;
    while (!commit) {
      try {
        commit = writer.commit();
      } catch (Exception e) {
        log.error("Error commiting messages to Datastore", e);
        if (!(e instanceof RetriableException))
          throw new MigrationException(e);
      }
    }
  }

  public void removeDocument(String host, String port, String index, String id) throws IOException {

    RestClientBuilder builder = RestClient.builder(new HttpHost(host, Integer.parseInt(port)));
    try (RestHighLevelClient client = new RestHighLevelClient(builder)) {
      DeleteRequest request = new DeleteRequest(index, id);
      try {
        DeleteResponse response = client.delete(request, RequestOptions.DEFAULT);
        if (response.getResult() != Result.NOT_FOUND) {
          log.error("{} Document not found in {} index.", id, index);
        }
      } catch (Exception e) {
        log.error("{} Error deleting document from {} index.", id, index, e);
      }
    }
  }

}
