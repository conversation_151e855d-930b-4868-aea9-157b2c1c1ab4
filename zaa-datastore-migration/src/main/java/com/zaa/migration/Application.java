package com.zaa.migration;

import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.config.Configurator;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestClient;
import org.opensearch.client.RestClientBuilder;
import org.opensearch.client.RestHighLevelClient;

import com.zaa.migration.service.DataMigrationService;

import java.io.File;
import java.util.Arrays;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.MissingArgumentException;
import org.apache.commons.cli.Options;
import org.apache.http.HttpHost;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;

public class Application {

  private static final Logger log = LogManager.getLogger(Application.class);

  enum Command {
    exporttofile, exporttodatastore, importtodatastore
  }

  public static void main(String[] args) throws Exception {
    run(args);
  }

  public static void run(String... args) throws Exception {

    // Create Options object
    Options options = new Options();
    options.addOption("h", "help", false, "Print this usage information.");

    options.addOption("i", "inputdatastore", true, "Input datastore host and port info. Ex: <hostname/ip>:<port>");
    options.addOption("o", "outputdatastore", true, "Output datastore host and port info. Ex: <hostname/ip>:<port>");

    options.addOption("d", "destinationfolder", true, "Destination path to write output files. Ex: /opt/folder/dump");
    options.addOption("s", "sourcefolder", true, "Source path to import data. Ex: /opt/folder/dump");

    options.addOption("x", "index", true, "Comma seperated list of indices to import or export. Ex: <index1>,<index2>");
    options.addOption("c", "commitsize", true, "Page size when writing data to datastore.");
    options.addOption("r", "readsize", true, "Page size when reading data from datastore.");

    options.addOption("l", "loglevel", true, "FATAL, ERROR, WARN, INFO, DEBUG, TRACE, ALL");

    try {
      CommandLineParser parser = new DefaultParser();
      CommandLine commandLine = parser.parse(options, args);
      if (commandLine.getOptions().length > 0) {
        if (commandLine.hasOption('h')) {
          printHelp(options);
        } else {

          if (commandLine.hasOption('l')) {
            Configurator.setLevel("com.zaa.migration",
                Level.toLevel(commandLine.getOptionValue('l'), Level.INFO));
          }

          // Check if the command exists. if not, print command usage details
          Command command;
          try {
            command = Command.valueOf(args[0].toLowerCase());
          } catch (IndexOutOfBoundsException | IllegalArgumentException e) {
            log.error("Invalid command." + e.getMessage());
            printHelp(options);
            return;
          }

          // Validate params for options
          if (!validateOptions(command, commandLine)) {
            printHelp(options);
            return;
          }

          String inputdatastore = commandLine.getOptionValue('i');
          String outputdatastore = commandLine.getOptionValue('o');
          String destinationfolder = commandLine.getOptionValue('d');
          String sourcefolder = commandLine.getOptionValue('s');
          String index = commandLine.getOptionValue('x');

          int readSize;
          if (commandLine.hasOption('r')) {
            readSize = Integer.parseInt(commandLine.getOptionValue('r'));
          } else {
            readSize = 5000;
          }

          int commitSize;
          if (commandLine.hasOption('c')) {
            commitSize = Integer.parseInt(commandLine.getOptionValue('c'));
          } else {
            commitSize = 5000;
          }

          log.debug("inputdatastore :" + inputdatastore);
          log.debug("outputdatastore :" + outputdatastore);
          log.debug("destinationfolder :" + destinationfolder);
          log.debug("sourcefolder :" + sourcefolder);
          log.debug("index :" + index);
          log.debug("readSize :" + readSize);
          log.debug("commitSize :" + commitSize);

          String[] idatastore;
          String[] odatastore;
          boolean isValid = true;
          boolean success = true;
          DataMigrationService migrateData = new DataMigrationService();
          switch (command) {
          case exporttofile:
            // exporttofile -- inputdatastore destinationfolder index readsize
            idatastore = validateDatastoreOption("inputdatastore", inputdatastore);
            if (idatastore != null) {
              success = validateDatastore("inputdatastore", idatastore[0], idatastore[1]);
              if (!success)
                isValid = false;
            } else {
              isValid = false;
            }

            success = validateFolderOption("destinationfolder", destinationfolder);
            if (!success)
              isValid = false;

            if (isValid) {
              log.debug("Calling exporttofile Service.");
              migrateData.exportToFile(idatastore[0], idatastore[1], destinationfolder, index, readSize, commitSize);
            }
            break;
          case exporttodatastore:
            // exporttodatastore -- inputdatastore outputdatastore index readsize commitsize
            idatastore = validateDatastoreOption("inputdatastore", inputdatastore);
            if (idatastore != null) {
              success = validateDatastore("inputdatastore", idatastore[0], idatastore[1]);
              if (!success)
                isValid = false;
            } else {
              isValid = false;
            }

            odatastore = validateDatastoreOption("outputdatastore", outputdatastore);
            if (odatastore != null) {
              success = validateDatastore("outputdatastore", odatastore[0], odatastore[1]);
              if (!success)
                isValid = false;
            } else {
              isValid = false;
            }
            
            if (odatastore != null && odatastore != null) {
              if (Arrays.equals(idatastore, odatastore)) {
                log.info("Both inputdatastore and outputdatastore are same.");
                isValid = false;
              }
            }

            if (isValid) {
              log.debug("Calling exporttodatastore Service.");
              migrateData.exportToDatastore(idatastore[0], idatastore[1], odatastore[0], odatastore[1], index, readSize, commitSize);
            }
            break;
          case importtodatastore:
            // importtodatastore -- sourcefolder outputdatastore index commitsize
            success = validateFolderOption("sourcefolder", sourcefolder);
            if (!success)
              isValid = false;

            odatastore = validateDatastoreOption("outputdatastore", outputdatastore);
            if (odatastore != null) {
              success = validateDatastore("outputdatastore", odatastore[0], odatastore[1]);
              if (!success)
                isValid = false;
            } else {
              isValid = false;
            }

            if (isValid) {
              log.debug("Calling importtodatastore Service.");
              migrateData.importToDataStore(odatastore[0], odatastore[1], sourcefolder, index, commitSize);
            }
            break;
          default:
            break;
          }
        }
      } else {
        printHelp(options);
      }
    } catch (MissingArgumentException e) {
      printHelp(options);
    }
  }

  public static void printHelp(Options options) {
    String header = "\nCommands:\n" + "exporttofile        Export from datastore to file\n"
        + "                    Required options: inputdatastore, destinationfolder,\n"
        + "                                      index\n"
        + "exporttodatastore   Export from datastore to another datastore\n"
        + "                    Required options: inputdatastore, outputdatastore,\n "
        + "                                      index\n"
        + "importtodatastore   Import from file to datastore\n"
        + "                    Required options: sourcefolder, outputdatastore,\n "
        + "                                      index\n" + "\nOptions:\n";
    String footer = "";

    HelpFormatter formatter = new HelpFormatter();
    formatter.printHelp("COMMAND [OPTIONS]", header, options, footer, false);
  }

  // exporttofile -- inputdatastore destinationfolder index readsize
  // exporttodatastore -- inputdatastore outputdatastore index readsize commitsize
  // importtodatastore -- sourcefolder outputdatastore index commitsize
  private static boolean validateOptions(Command command, CommandLine commandLine) {
    boolean isValid = true;
    switch (command) {
    case exporttofile:
      if (!commandLine.hasOption('i') || !commandLine.hasOption('d') || !commandLine.hasOption('x'))
        isValid = false;
      break;
    case exporttodatastore:
      if (!commandLine.hasOption('i') || !commandLine.hasOption('o') || !commandLine.hasOption('x'))
        isValid = false;
      break;
    case importtodatastore:
      if (!commandLine.hasOption('s') || !commandLine.hasOption('o') || !commandLine.hasOption('x'))
        isValid = false;
      break;
    default:
      isValid = true;
      break;
    }
    return isValid;
  }

  private static boolean validateDatastore(String option, String host, String port) {
    RestClientBuilder builder = RestClient.builder(new HttpHost(host, Integer.parseInt(port)));
    try (RestHighLevelClient client = new RestHighLevelClient(builder)) {
      client.ping(RequestOptions.DEFAULT);
      return true;
    } catch (Exception e) {
      log.info("{}: Unable to connect to datastore {}:{}. ", option, host, port);
    }
    return false;
  }

  private static String[] validateDatastoreOption(String option, String datastore) {
    String[] result = Arrays.stream(datastore.split(":")).map(String::trim).toArray(String[]::new);
    if (result.length == 2) {
      try {
        Integer.parseInt(result[1]);
      } catch (NumberFormatException nfe) {
        log.info("{}: {} is not valid. ", option, datastore);
        return null;
      }
      return result;
    }
    log.info("{}: {} not valid. ", option, datastore);
    return null;
  }

  private static boolean validateFolderOption(String option, String folder) {
    File f = new File(folder);
    if (f.exists() && f.isDirectory()) {
      return true;
    }

    log.info("{}: {} either doesn't exists or is not a directory. ", option, folder);
    return false;
  }

}
