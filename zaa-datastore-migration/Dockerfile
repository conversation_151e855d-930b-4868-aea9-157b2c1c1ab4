##################################################################################
# INPUT IMAGE - ZAA Data Migration Tool
##################################################################################

FROM icr.io/zoa-oci/zoatools-maven:3.9.9-x86_64 AS migtoolbuilder

ARG VRMF=0.0.1-SNAPSHOT

COPY zaa-datastore-migration /zaa-datastore-migration

WORKDIR /zaa-datastore-migration

RUN mvn versions:set -DnewVersion=${VRMF} && \
    mvn -Dmaven.test.skip clean package

##################################################################################
# EXPORT JAR - Export ZAA Data Migration Tool
##################################################################################

FROM scratch AS export-stage

ARG VRMF=0.0.1-SNAPSHOT

COPY --from=migtoolbuilder /zaa-datastore-migration/target/zaa-datastore-migration-${VRMF}.jar /zaa-datastore-migration.jar
