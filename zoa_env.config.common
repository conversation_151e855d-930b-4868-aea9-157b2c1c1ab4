################################################################################
# SETTINGS FOR Z OPERATIONAL ANALYTICS COMMON SERVICES                         #
################################################################################

# Datastore settings
#-------------------------------------------------------------------------------
# ZAIOPS_DATASTORE_HEAP=<Datastore heap size in GB>
#
# Description:
#   The heap memory size to allocate to the Z Data Analytics Platform datastore
#   process.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZAIOPS_DATASTORE_HEAP="4"
#
# Notes:
#
#-------------------------------------------------------------------------------
ZAIOPS_DATASTORE_HEAP=4

#-------------------------------------------------------------------------------
# ZOA_DATASTORE_MAX_SEARCH_BUCKETS=<max number of datastore search buckets>
#
# Description:
#   The maximum number of search buckets in the datastore is set to 65,535 by
#   default.
#   If you encounter problems with search bucket exhaustion, you may increase
#   this value.
#
# Required value? Yes
#
# Default value: 65535
#
# Example:
#   ZOA_DATASTORE_MAX_SEARCH_BUCKETS=65535
#
# Notes:
#   Increasing this value can negatively impact the performance of the Z
#   Data Analytics Platform.
#
#-------------------------------------------------------------------------------
ZOA_DATASTORE_MAX_SEARCH_BUCKETS=65535


# Problem Insights service settings
#-------------------------------------------------------------------------------
# PI_CONSOLE_LOG_LEVEL=<Log level to be displayed in the regular Problem Insights service log output>
#
# Description:
#   This value specifies the log level to be displayed in the regular logging output of the
#   Problem Insights service.
#   The following values are possible:
#     * INFO     (audit, error, warning and informational messages)
#     * AUDIT    (audit, error and warning messages)
#     * WARNING  (error and warning messages)
#     * ERROR    (error messages)
#     * OFF      (no messages)
#
# Required value? Yes
#
# Default value: INFO
#
# Example:
#   PI_CONSOLE_LOG_LEVEL=AUDIT
#
# Notes:
#
#-------------------------------------------------------------------------------
PI_CONSOLE_LOG_LEVEL=

#-------------------------------------------------------------------------------
# PI_LOG_FORMAT=<Log format to be displayed in the Problem Insights service log output>
#
# Description:
#   This value specifies the log format to be displayed in the logging output of the
#   Problem Insights service.
#   The following values are possible:
#     * DEV
#     * JSON
#     * SIMPLE
#     * TBASIC
#
# Required value? Yes
#
# Default value: SIMPLE
#
# Example:
#   PI_LOG_FORMAT=JSON
#
# Notes:
#
#-------------------------------------------------------------------------------
PI_CONSOLE_LOG_FORMAT=

#-------------------------------------------------------------------------------
# PI_TRACE_LOG_LEVEL=<Log level to be displayed in the Problem Insights service trace output>
#
# Description:
#   This value specifies the log level to be displayed in the trace logging output of the
#   Problem Insights service.
#   The following values are possible:
#     * severe
#     * warning
#     * info
#     * finest
# Required value? Yes
#
# Default value: This property has no default value
#
# Example:
#   PI_TRACE_LOG_LEVEL=info
#
# Notes:
#
#-------------------------------------------------------------------------------
PI_TRACE_LOG_LEVEL=info

# Event forwarding settings
#-------------------------------------------------------------------------------
# EVENTS_ENABLED=<Whether or not to enable event forwarding>
#
# Description:
#   This value specifies whether or not events reported to the Problem Insights
#   server should be forwarded to an external event management system, chatops
#   program and/or email, SMS or MMS destination.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   EVENTS_ENABLED=true
#
# Notes:
#
#-------------------------------------------------------------------------------
EVENTS_ENABLED=false

#-------------------------------------------------------------------------------
# EVENTAPI_USER=<User name configured for Netcool/OMNIbus Probe for Message Bus>
#
# Description:
#   This value specifies the user name that is configured for the
#   Netcool/OMNIbus Probe for Message Bus to which
#   the Problem Insights server must connect.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   EVENTAPI_USER=ADMIN
#
# Notes:
#
#-------------------------------------------------------------------------------
EVENTAPI_USER=

#-------------------------------------------------------------------------------
# EVENTAPI_PASSWORD=<password for EVENTAPI_USER>
#
# Description:
#   This value specifies the password for the user name that is defined by the
#   EVENTAPI_USER property. The password value must be base64-encoded in this 
#   configuration file.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   EVENTAPI_PASSWORD=cGFzc3dvcmQ=
#
# Notes:
#   To base64-encode the password, perform the following command:
#     echo -n <clear_text_password> | base64
#
#-------------------------------------------------------------------------------
EVENTAPI_PASSWORD=

#-------------------------------------------------------------------------------
# EVENTAPI_URL=<URL configured for Netcool/OMNIbus Probe for Message Bus>
#
# Description:
#   This value specifies the URL that is configured for the Netcool/OMNIbus 
#   Probe for Message Bus. This URL includes the transport type (HTTP, for example),
#   the host name, and the port (80, for example) for the probe.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   EVENTAPI_URL=http://event.management.system.com:80
#
# Notes:
#
#-------------------------------------------------------------------------------
EVENTAPI_URL=

#-------------------------------------------------------------------------------
# CEM_USER=<User name for the web-based event management service>
#
# Description:
#   This value specifies the user name for the IBM Netcool Operations Insight
#   or IBM Watson AIOps Event Manager service to which the Problem Insights server 
#   must connect.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   CEM_USER=ADMIN
#
# Notes:
#
#-------------------------------------------------------------------------------
CEM_USER=

#-------------------------------------------------------------------------------
# CEM_PASSWORD=<Password for CEM_USER>
#
# Description:
#   This value specifies the password for the user name that is defined by the
#   CEM_USER property. The password value must be base64-encoded in this configuration
#   file.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   CEM_PASSWORD=cGFzc3dvcmQ=
#
# Notes:
#   To base64-encode the password, perform the following command:
#     echo -n <clear_text_password> | base64
#
#-------------------------------------------------------------------------------
CEM_PASSWORD=

#-------------------------------------------------------------------------------
# CEM_URL=<URL for webhook integration with the web-based event management service>
#
# Description:
#   This value specifies the URL for the IBM Netcool Operations Insight or IBM Watson 
#   AIOps Event Manager webhook integration.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   CEM_URL=http://event.management.service.com:5050/send/alert/example
#
# Notes:
#
#-------------------------------------------------------------------------------
CEM_URL=

#-------------------------------------------------------------------------------
# ZCHATOPS_USER=<User account name for IBM Z ChatOps>
#
# Description:
#   This value specifies the user account name for the IBM Z ChatOps
#   server to which the Problem Insights server must connect. Typically,
#   IBM Z ChatOps defines the user ID 'izoa' for integration with Z Operational
#   Analytics products.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZCHATOPS_USER=izoa
#
# Notes:
#
#-------------------------------------------------------------------------------
ZCHATOPS_USER=

#-------------------------------------------------------------------------------
# ZCHATOPS_PASSWORD=<Password for ZCHATOPS_USER>
#
# Description:
#   This value specifies the password for the user account name that is defined by the
#   ZCHATOPS_USER property. The password value must be base64-encoded in this configuration
#   file. The default password for the IBM Z ChatOps user ID 'izoa' is 'bnz4you!'.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZCHATOPS_PASSWORD=Ym56NHlvdSE=
#
# Notes:
#   To base64-encode the password, perform the following command:
#     echo -n <clear_text_password> | base64
#
#-------------------------------------------------------------------------------
ZCHATOPS_PASSWORD=

#-------------------------------------------------------------------------------
# ZCHATOPS_AUTH_URL=<URL for the IBM Z ChatOps authentication API>
#
# Description:
#   This value specifies the URL for the IBM Z ChatOps authentication API.
#   IBM Z ChatOps defines https://<z_chatops_host>:4001/ibm/bnz/v1/auth/login
#   as the authentication API endpoint by default.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZCHATOPS_AUTH_URL=https://zchatops.system.com:4001/ibm/bnz/v1/auth/login
#
# Notes:
#
#------------------------------------------------------------------------------
ZCHATOPS_AUTH_URL=

#-------------------------------------------------------------------------------
# ZCHATOPS_INCIDENT_URL=<URL for the IBM Z ChatOps incident API>
#
# Description:
#   This value specifies the URL for the IBM Z ChatOps incident API.
#   IBM Z ChatOps defines https://z_chatops_host>:4001/ibm/bnz/v1/incident
#   as the incident API endpoint by default.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZCHATOPS_INCIDENT_URL=https://zchatops.system.com:4001/ibm/bnz/v1/incident
#
# Notes:
#
#-------------------------------------------------------------------------------
ZCHATOPS_INCIDENT_URL=

#-------------------------------------------------------------------------------
# ZCHATOPS_CHANNEL=<IBM Z ChatOps channel where events should be posted>
#
# Description:
#   This value specifies the name of the channel in your chat application that is
#   connected to IBM Z ChatOps. This channel is where events from the
#   Problem Insights server are to be posted.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   ZCHATOPS_CHANNEL=izoa-devops
#
# Notes:
#
#-------------------------------------------------------------------------------
ZCHATOPS_CHANNEL=

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_HOST=<Host address for the email server>
#
# Description:
#   This value specifies the host address for the email server to which 
#   the Problem Insights server must connect. 
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   NOTIFICATION_EMAIL_HOST=mail.domain.com
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_HOST=

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_PORT=<Port for the email server>
#
# Description:
#   This value specifies the port for the email server to which 
#   the Problem Insights server must connect. 
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   NOTIFICATION_EMAIL_PORT=25
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_PORT=

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_USERNAME=<User name for the email server>
#
# Description:
#   This value specifies the user name for the email server to which 
#   the Problem Insights server must connect. 
#
# Required value? No
#
# Default value: This property has no default value.
#
# Example:
#   NOTIFICATION_EMAIL_USERNAME=
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_USERNAME=

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_PASSWORD=<Password for NOTIFICATION_EMAIL_USERNAME>
#
# Description:
#   This value specifies the password for the user name that is defined by the 
#   NOTIFICATION_EMAIL_USERNAME property. The password value must be base64-
#   encoded in this configuration file.
#
# Required value? No
#
# Default value: This property has no default value.
#
# Example:
#   NOTIFICATION_EMAIL_PASSWORD=
#
# Notes:
#   To base64-encode the password, perform the following command:
#     echo -n <clear_text_password> | base64
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_PASSWORD=

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_SSL_ENABLED=<True or false indication of whether TLS is enabled for email server>
#
# Description:
#   This value specifies whether TLS is enabled for the email server.
#
# Required value? Yes
#
# Default value: false
#
# Example:
#   NOTIFICATION_EMAIL_SSL_ENABLED=false
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_SSL_ENABLED=false

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_FROM_ADDRESS=<Sender address for the email notification>
#
# Description:
#   This value specifies the sender address (in the "From" field) for the 
#   email notification that is sent.   
#
# Required value? Yes
#
# Default value: <EMAIL>
#
# Example:
#   NOTIFICATION_EMAIL_FROM_ADDRESS=<EMAIL>
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_FROM_ADDRESS=<EMAIL>

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_FROM_NAME=<Sender name for the email notification>
#
# Description:
#   This value specifies the sender name (in the "From" field) for the 
#   email notification that is sent. 
# 
# Required value? Yes
#
# Default value: IBM Z Analytics
#
# Example:
#   NOTIFICATION_EMAIL_FROM_NAME=IBM Z Analytics
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_FROM_NAME="IBM Z Analytics"

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_SUBJECT_PREFIX=<Prefix for the subject in the email notification>
#
# Description:
#   This value specifies the prefix to use in the "Subject" field of the 
#   email notification.
#
# Required value? Yes
#
# Default value: IBM Z Analytics Notification
#
# Example:
#   NOTIFICATION_EMAIL_SUBJECT_PREFIX=IBM Z Analytics Notification
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_SUBJECT_PREFIX="IBM z Analytics Notification"

#-------------------------------------------------------------------------------
# NOTIFICATION_EMAIL_TO_ADDRESS=<Comma-separated list of email addresses to receive the email>
#
# Description:
#   This value specifies a comma-separated list of email addresses that must receive
#   the email notification.
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   NOTIFICATION_EMAIL_TO_ADDRESS=<EMAIL>,<EMAIL>
#
#-------------------------------------------------------------------------------
NOTIFICATION_EMAIL_TO_ADDRESS=

#-------------------------------------------------------------------------------
# FORWARD_MESSAGES=<Message IDs that identify the events to be forwarded>
#
# Description:
#   This value specifies the message IDs for the
#   events that are to be sent to other software, 
#   such as an event management system or service, 
#   a ChatOps application, an email server, or some or all of these. 
#                    
#   The message IDs are contained in XML files that are provided with the IBM Z Anomaly
#   Analytics and IBM Z Operational Log and Data Analytics products.
#   XML files that are provided by Z Operational Log and Data Analytics are 
#   installed in <PI_HOME>/usr/lang/en/ (for example, IZOADB2Insights.xml).
#   XML files that are provided by Z Anomaly Analytics are installed
#   in <PI_HOME>/wlp/usr/servers/piFrameworkServer/insightpacks/installed/
#   <insight_pack>/ (for example, IZOACICSMLInsights.xml).
#
#   You can specify a complete message ID, or a part of a message ID with the
#   wildcard character *, as shown in the following examples:
#
#   FORWARD_MESSAGES=DSN*
#   Forwards only messages that start with "DSN"
#
#   FORWARD_MESSAGES=DSN*,IEA367A
#   Forwards messages that start with "DSN" and forwards the message IEA367A
#
#   FORWARD_MESSAGES=DFH*, EYUCS0207W, DSN*
#   Forwards messages that start with "DFH" and "DSN" and forwards the message 
#   EYUCS0207W
#
# Required value? Yes
#
# Default value: This property has no default value.
#
# Example:
#   The following value is set to include many message IDs that are supported by
#   Z Operational Log and Data Analytics:
#   FORWARD_MESSAGES=DFHSO0123,DFHSM0102,DFHSM0103,EYUCS0207W,DFHLG0777,DFHFC0001,DFHFC0208I,DFHKE0303,DFHLG0772,DFHDB2037,DFHDM0106,DFHIR3785,DFHKE0001,DFHKE0002,DFHFC0001,DFHFC0002,DFHLG0774,DFHSM0127,DFHSM0128,DFHSM0131,DFHSM0133,DFHXM0213,DSNT408I,DSNU016I,DSNU017I,DSNX908I,DFHDB2110I,DSNV508I,DSNV086E,DSN3201I,DSNT375I,DSN9016I,DSNT376I,DSNL027I,DSNL030I,DSNB319A,DSNB325A,CSQY222E,CSQY224I,CSQP014E,CSQ5023E,CSQ5003A,CSQY228E,CSQP020E,CSQX033E,CSQX570E,CSQJ164I,CSQX053E,CSQX489E,CSQX544E,CSQE038E,CSQX027E,CSQX005E,CSQX558E,CSQE218E,CSQX548E,CSQR026I,CSQE239I,CSQE217I,CSQY221I,CSQE215I,CSQJ110E,CSQP017I,CSQY225E,CSQX490E,CSQ5009E,CSQX014E,CSQE041E,CSQP016E,CSQX573E,CSQX513E,CSQE035E,CSQX202E,CSQX599E,CSQX038E,CSQX569E,CSQX658E,EZZ4350I,EZZ8158I,EZZ4346I,EZZ7902I,EZZ8122I,EZZ7914I,EZZ4249I,EZZ7926I,EZZ8146I,EZZ7938I,EZZ8134I,EZZ4310I,IVT5560I,EZD0047I,EZZ9679E,EZZ7941I,EZZ9299E,EZZ7901I,EZZ9671E,EZD1187E,IST561I,EZZ8133I,EZZ4345I,EZZ7903I,EZZ8121I,EZZ7915I,EZZ8157I,EZZ8145I,EZZ7939I,EZZ9678E,EZZ7930I,EZZ7942I,EZZ7954I,EZZ9670E,IST930I,IST562I,EZD1974E,EZZ7924I,EZZ8132I,EZZ4348I,EZZ8168I,EZZ4215I,EZZ8120I,EZZ4349I,EZZ7923I,IST563I,EZZ7913I,EZD1973E,EZZ7925I,EZZ8058I,EZZ4347I,EZZ4311I,EZZ8074I,EZZ9676E,EZZ7940I,EZD0004I,EZZ7964I,EZZ9301E,EZZ4308I,EZZ7900I,EZZ7912I,EZZ7475I,IST564I,IVT5592I,EZZ7946I,EZZ8166I,EZZ4205I,EZZ4342I,EZZ4221I,EZZ7918I,EZD1172E,EZZ8061I,EZZ7864I,EZZ4339I,EZZ4218I,EZZ7921I,EZZ7933I,EZD2028I,EZD2020A,EZZ7945I,EZD1209E,IST565I,IVT5591I,EZZ7947I,EZZ8165I,EZZ4216I,EZD1171I,EZZ4204I,EZZ9308E,IVT5563I,EZZ4341I,EZZ4220I,EZZ7907I,EZZ7919I,EZZ8141I,EZZ9311E,EZZ4338I,EZD1170I,EZZ7922I,EZZ9674E,IST566I,IST154I,EZD1211E,EZZ8136I,EZZ4247I,EZZ7904I,EZZ7916I,EZZ8140I,IVT5562I,EZZ4344I,EZZ4223I,EZZ8164I,EZD1194E,EZZ8152I,EZZ9307E,EZZ9673E,EZZ7931I,EZZ7943I,EZZ7955I,IST999E,EZZ4248E,EZD1210E,EZZ7957I,EZD1193I,EZZ8135I,EZZ7905I,EZZ8123I,EZZ7917I,EZZ8151I,EZZ8054I,IVT5561I,EZZ4343I,EZZ4222I,EZZ8163I,EZZ7960I,EZZ4207I,EZZ7920I,EZZ9672E,EZD1192I,EZZ7932I,EZZ7944I,EZZ7956I,EZD0040I,EZD0800I,IEA367A,IEA652A,IEA405E,IEA139E,IEA359E,IEA705I,IEA231A,IEA556I,IEA611I,IEA602I,IEA133E,IEA794I,IEA360A,IEA230E,IEA995I,IEA792I,IEA067I,IEA798W,IEF453I,IEF452I,IEF450I,IEA404A,IEA806I,IEA607E
#
#   The following value is set to include anomaly events, "new message" events,  
#   and "never before seen message" events that are supported by the log-based
#   machine learning feature in Z Anomaly Analytics:
#   FORWARD_MESSAGES=LOGMSG00,LOGMSG01,LOGMSG02
#
#   The following value is set to include anomaly events for the CICS and Db2
#   subsystems that are provided by the metric-based machine learning feature
#   in Z Anomaly Analytics:
#   FORWARD_MESSAGES=CICSMLSSC,DB2MLSSC
#
#-------------------------------------------------------------------------------
FORWARD_MESSAGES=

#-------------------------------------------------------------------------------
# PI_DB_RETENTION_PERIOD=<number of days that should be retained when purging event data>
#
# Description:
#   This property specifies the retention period, in days, for event data stored in the
#   Problem Insights server database.
#   The value is used by the 'prune-pi-db' command. This command can be used to remove 
#   old event data from the database. All event data older than the specified value 
#   will be removed from the database.
#   After changing this value, you must run the 'up piserver' command to persist the
#   configuration changes.
#
# Required value? No
#
# Default value: 90
#
# Example:
#   PI_DB_RETENTION_PERIOD=30
#
#-------------------------------------------------------------------------------
PI_DB_RETENTION_PERIOD=90
