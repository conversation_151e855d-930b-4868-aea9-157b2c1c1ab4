services:
  datastore:
    build:
      context: ..
      dockerfile: zoa-common-docker-MF/opensearch/Dockerfile
      args:
        OS_VER: ${OSTAG}
        ARCH: ${UBU_ARCH}
        # REPO: ${zoa_common_docker_mf_repo:-na}
        REPO: ${zoa_common_docker_mf_repo:-na}
        #COMMIT: ${zoa_common_docker_mf_commit:-na}
        COMMIT: ${zoa_common_docker_mf_commit:-na}
        VRMF: ${VRMF}
        FORKBRANCH: ${FORKBRANCH}
        FALLBACKBRANCH: ${FALLBACK_BRANCH}
        BUILDER: ${BUILDER}
        TOKEN: ${TOKEN}
    image: icr.io/zoa-oci/zoa-datastore:${TAG}-x86_64
    container_name: zoa-datastore
    hostname: datastore
    environment:
      - discovery.type=single-node
        #- DATASTORE_HEAP=${ZAIOPS_DATASTORE_HEAP}
      - DATASTORE_HEAP=4
    volumes:
      - zaiops_datastore:/usr/share/opensearch/data
    ports:
      #- ${ZAIOPS_DATASTORE_PORT}:9200
      - 9200:9200
    restart: always
    logging:
      #driver: ${LOGGING_DRIVER}
      driver: json-file
      options:
        #mode: ${LOGGING_MODE}
        mode: non-blocking
    networks:
      - zaiops
  gateway:
    build:
      context: ..
      dockerfile: zoa-spring-cloud-gateway-MF/docker/Dockerfile
      args:
        NETTY_VERSION: ${NETTYVER}
        ARCH: ${OPENJDK_ARCH}
        REPO: ${zoa_spring_cloud_gateway_mf_repo:-na}
        COMMIT: ${zoa_spring_cloud_gateway_mf_commit:-na}
        VRMF: ${VRMF}
    image: icr.io/zoa-oci/zoa-gateway:${TAG}-x86_64
    ports:
      - ${IZOA_GATEWAY_PORT}:8085
    environment:
      #- ZUUL_ROUTES_PIFRAMEWORK_URL=${ZUUL_ROUTES_PIFRAMEWORK_URL}
      - PI_FRAMEWORK_ROUTE=${PI_FRAMEWORK_ROUTE}
      - ZAIOPS_GW_PASS=${ZAIOPS_ZOASVC_PASS}
      - SSL_DEBUG=${SSL_DEBUG}
      # To change log level to debug, uncomments the next two lines.
      # Other possible values for log levels are TRACE DEBUG INFO WARN ERROR FATAL
      # - LOGGING_LEVEL_ROOT=DEBUG
      # - LOGGING_LEVEL_COM_IBM_ZSYSTEM_ZMANAGED_PIGATEWAY=DEBUG
    hostname: gateway
    container_name: zoa-gateway
    # Linux OS only !!!!
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # set host=host.docker.internal in .zoa_factory.config on linux for xdebug
    #   - ${host:-host}:host-gateway
    networks:
      - zaiops
    volumes:
      - zaiops_shared:/shared:ro
    restart: always    
    logging:
      driver: ${LOGGING_DRIVER}
      options:
        mode: ${LOGGING_MODE}
  auth:
    build:
      context: ..
      dockerfile: izoa-pi-keycloak-MF/server/Dockerfile
      args:
        KEYCLOAK_VERSION: ${KCVER}
        QUARKUS_VERSION: ${QUARKUSVER}
        WILFDLY_VERSION: ${WILDFLYVER}
        ARCH: ${OPENJDK_ARCH}
        REPO: ${izoa_pi_keycloak_mf_repo:-na}
        COMMIT: ${izoa_pi_keycloak_mf_commit:-na}
        VRMF: ${VRMF}
    image: icr.io/zoa-oci/zoa-auth:${TAG}-x86_64
    container_name: zoa-auth
    hostname: auth
    environment:
      # To change log level to debug, uncomments the next two lines.
      - ZAIOPS_KC_PASS=${ZAIOPS_ZOASVC_PASS}
    #ports:
    #  - ${ZAIOPS_KEYCLOAK_PORT}:8443
    #  - 8080:8080      
    restart: always
    logging:
      driver: ${LOGGING_DRIVER}
      options:
        mode: ${LOGGING_MODE}
    networks:
      - zaiops
    volumes:
      - zaiops_keycloak:/opt/keycloak/data/
      - zaiops_shared:/shared:ro
  discovery:
    build:
      context: ../izoa-pi-discovery-server-MF
      dockerfile: docker/Dockerfile
      args:
        REPO: ${izoa_pi_discovery_server_mf_repo:-na}
        COMMIT: ${izoa_pi_discovery_server_mf_commit:-na}
        VRMF: ${VRMF}
    image: icr.io/zoa-oci/zoa-service-discovery:${TAG}-x86_64
    ports:
      - 8761:8761
    restart: always
    logging:
      driver: ${LOGGING_DRIVER}
      options:
        mode: ${LOGGING_MODE}
    hostname: discovery
    container_name: zoa-service-discovery
    networks:
      - zaiops
    environment:
      # To change log level to debug, uncomments the next two lines.
      # Other possible values for log levels are TRACE DEBUG INFO WARN ERROR FATAL
      # - LOGGING_LEVEL_ROOT=DEBUG
      # - LOGGING_LEVEL_COM_IBM_ZSYSTEM_ZMANAGED_IZOAPIDISCOVERYSERVER=DEBUG
      - JAVA_OPTS=
         -DEUREKA_URI=http://localhost:8761/eureka
  kafkabroker:
    build:
      context: ..
      dockerfile: zoa-common-docker-MF/kafka/kafkabroker/Dockerfile
      args:
        REPO: ${zoa_common_docker_mf_repo:-na}
        COMMIT: ${zoa_common_docker_mf_commit:-na}
        KAFKA_VERSION: ${KAFKA_VERSION}
        AF_USER: ${AFUSER}
        AF_PWD: ${AFPWD}
        VRMF: ${VRMF}
    image: icr.io/zoa-oci/zoa-kafkabroker:${TAG}-x86_64
    environment:
      # For multi-host setups, uncomment the next line and comment out the
      # ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST must be set to the host's address
      # to allow for proper connections from external clients (like CDP)
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}
      - ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS}
      # To enable SASL/PLAIN Authentication uncomment the next line
      # - KAFKA_AUTHENTICATION_ENABLED=true
      # - KAFKA_HEAP_OPTS: -Xmx2g -Xms2g
      # - KAFKA_SSL_ENABLED=false      
    ports:
      - ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}
      - ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}
    volumes:
      - zaiops_kafkabroker:/opt/kafka/data
    hostname: kafkabroker
    container_name: zoa-kafkabroker
    restart: always
    logging:
      driver: ${LOGGING_DRIVER}
      options:
        mode: ${LOGGING_MODE}
    networks:
      - zaiops
  piserver:
    build:
      context: ..
      dockerfile: zoa-common-docker-MF/piserver/Dockerfile
      args:
        REPO: ${piserver_mf_repo:-na}
        COMMIT: ${piserver_mf_commit:-na}
    image: icr.io/zoa-oci/zoa-piserver:${TAG}-x86_64
    # ports:
    #   - ${PISERVER_PORT}:9446
    environment:
      - SSL_DEBUG=${SSL_DEBUG}
      - PI_SSL_ENABLED=${PI_SSL_ENABLED}
    restart: always
    logging:
      driver: ${LOGGING_DRIVER}
      options:
        mode: ${LOGGING_MODE}
    hostname: piserver
    container_name: zoa-piserver
    networks:
      - zaiops
    volumes:
      - zaiops_shared:/shared:ro
      - zaiops_piserver_db:/opt/ibm/piserver/wlp/usr/servers/piFrameworkServer/derby
      - zaiops_piserver_usr:/opt/ibm/piserver/usr
      - zaiops_piserver_ip:/opt/ibm/piserver/wlp/usr/servers/piFrameworkServer/insightpacks  
      - zaiops_piserver_staging:/opt/ibm/staging

volumes:
  zaiops_datastore:
  zaiops_kafkabroker:
  zaiops_keycloak:
  zaiops_piserver_db:
  zaiops_piserver_usr:
  zaiops_piserver_ip:
  zaiops_piserver_staging:
  zaiops_shared:
    external: true

networks:
  zaiops:
    driver: bridge
