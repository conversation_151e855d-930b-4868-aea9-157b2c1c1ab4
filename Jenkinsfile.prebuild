// Using Scripted Pipeline for maximum flexibility

// This is a custom data structure we'll use to define our parallel builds:
List<StageDef> stageDefs = [
        new StageDef("x86_64")]

// The 'branches' structure is a map from branch name to branch code. This is the
// argument we'll give to the 'parallel' build step later:
def branches = [:]
def s390xCommonTarImg = "zoacommon-s390x"
def s390xCoreTarImg = "zoacore-s390x"
def x86CommonTarImg = "zoacommon-x86_64"
def x86CoreTarImg = "zoacore-x86_64"

properties ([
  [ $class: 'BuildDiscarderProperty', strategy: 
    [ $class: 'LogRotator', 
        artifactDaysToKeepStr: '10', 
        artifactNumToKeepStr: '10',
        daysToKeepStr: '10',
        numToKeepStr: '10'
    ]
  ] 
])

// This build needs to run every time since the constituent pieces are spread over
//   many repos
// Loop through the stage definitions and define the parallel stages:
for (stageDef in stageDefs) {

    // Never inline this!
    String interp = stageDef.interp

    String gitOrg = "IZOA"
    String gitRepo = "zoa-common-docker-MF"
    String branchName = "Build ZOA common Docker images for ${interp}"
    String labelName = "docker-${interp}-backup"
    String gitBranch = "${env.BRANCH_NAME}"
    String fallbackBranch = "develop"
    String buildId = "${env.BUILD_NUMBER}"
    String version = "latest"
    String compactVersion = "v511-dev"
    String vrmf = "5.1.1-dev"
    String forkBranch = gitBranch
    if (gitBranch.equals("develop")) {
      labelName = "docker-${interp}"
    }
    if (gitBranch ==~ /.*_release$/) {
        version = gitBranch.substring(0,4).replaceAll(/([0-9])([0-9])([0-9])([0-9])/, '$1.$2.$3-$4')
        compactVersion = gitBranch.substring(0,4).replaceAll(/([0-9])([0-9])([0-9])([0-9])/, 'v$1$2$3-0$4')
        labelName = "docker-${interp}"
    }
    if (gitBranch.equals("service_v5.1.0")) {
        version = "5.1.1-22"
        compactVersion = "v511-22"
        vrmf = "5.1.1-22"
        fallbackBranch = gitBranch
        labelName = "docker-${interp}"
    }
    def nowDate = new Date()
    final buildDate = nowDate.format('yyyyMMdd-HHmm')
    print("Label name is ${labelName}.")
    print("Git branch is ${gitBranch}.")
    print("Version is ${version}.")
    print("Compact version is ${compactVersion}.")
    print("Jenkins build ID is ${buildId}.")
    print("Build date is ${buildDate}.")

    branches[branchName] = {

        // Start the branch with a node definition. We explicitly exclude the
        // master node, so only the two slaves will do builds:
        node(labelName) {
            def workspace = env.WORKSPACE
            print("Current workspace is ${workspace}.")
            String targetDir = 'GITWORK/' + gitRepo
            sh(script: 'rm -Rf GITWORK && mkdir -p ' + targetDir, returnStdout: true)
            sh(script: 'cd GITWORK && <NAME_EMAIL>:' + gitOrg + '/' + gitRepo + '.git -b ' + gitBranch, returnStdout: true)
            def buildProps = readProperties file: workspace + '/GITWORK/' + gitRepo + '/.buildenv'
            withEnv(["GITBRANCH=" + gitBranch,
                    "FALLBACK_BRANCH=" + fallbackBranch,
                    "ARCH=" + interp,
                    "VER=" + buildProps['TAG'],
                    "VRMF=" + vrmf,
                    "COMPACT_VER=" + compactVersion,
                    "KAFKAVER=" + buildProps['KAFKA_VERSION'],
                    "CONNECT_SINK_VER=" + buildProps['CONNECT_SINK_VER'],
                    "KCVER=" + buildProps['KCVER'],
                    "OSVER=" + buildProps['OSTAG'],
                    "NETTYVER=" + buildProps['NETTYVER'],
                    "QUARKUSVER=" + buildProps['QUARKUSVER'],
                    "WILDFLYVER=" + buildProps['WILDFLYVER'],
                    "ZRDDSVER=" + buildProps['ZRDDS_VERSION'],
                    "PGKCVER=" + buildProps['PGKCTAG'],
                    "FORKBRANCH=" + forkBranch,
                    "BUILD_DATE=" + buildDate,
                    "BUILD_ID=" + buildId,
                    "S390X_CORE_TAR=" + s390xCoreTarImg,
                    "S390X_COMMON_TAR=" + s390xCommonTarImg,
                    "X86_CORE_TAR=" + x86CoreTarImg,
                    "X86_COMMON_TAR=" + x86CommonTarImg
            ]) {
                stage("Clone required GitHub repos on ${interp}") {
                    try {
                        // Clone all required GitHub projects
                        sh '''
                            rm -Rf DIST && mkdir -p DIST/core DIST/common
                            REPFILE=GIT_REPORT.txt
                            cd GITWORK
                            echo "************************************************************************************************" >> ${REPFILE}
                            echo "GitHub Report for Build ${BUILD_ID} on ${BUILD_DATE}" >> ${REPFILE}
                            echo "************************************************************************************************" >> ${REPFILE}
                            printf "%-30s %-1s %-20s %-1s %-40s\n" "REPO" "|" "BRANCH" "|" "COMMIT HASH" >> ${REPFILE}
                            echo "************************************************************************************************" >> ${REPFILE}
                            for REPO in zoa-common-docker-MF izoa-pi-keycloak-MF izoa-pi-keycloak-encryption-MF izoa-keycloak-2fa-email-authenticator keycloak-racf-spi keycloak-filter-provider-users
                            do
                              RESULT=$( git ls-remote --heads ******************:IZOA/${REPO} ${GITBRANCH} | awk '{ print $2 }' )
                              if [ -z ${RESULT} ]
                              then
                                PULLBRANCH=${FALLBACK_BRANCH}
                              else
                                PULLBRANCH=${GITBRANCH}
                              fi
                              if [ "${REPO}" != "zoa-common-docker-MF" ]
                              then
                                <NAME_EMAIL>:IZOA/${REPO}.git -b ${PULLBRANCH}
                              fi
                            done
                            find -name "*.sh" | xargs chmod 755
                        '''
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Build customized netty libraries on ${interp}") {
                    try {
                        sh '''
                            WORKROOT=${PWD}
                            if [ "${ARCH}" = "x86_64" ]
                            then
                                export UBU_ARCH="amd64"
                            else
                                export UBU_ARCH=${ARCH}
                            fi
                            cd GITWORK/zoa-common-docker-MF
                            JAVA_HOME=/usr/lib/jvm/java-17-openjdk-${UBU_ARCH}
                            export PATH=${HOME}/tools/apache-maven-3.6.3/bin:${JAVA_HOME}/bin:${PATH}

                            git clone https://github.com/netty/netty.git
                            cd netty
                            git checkout ${NETTYVER}
                            git apply ../patches/${NETTYVER}/netty-resolver-dns.patch
                            mvn -pl :netty-common -Dmaven.test.skip clean package
                            mvn -pl :netty-resolver-dns -Dmaven.test.skip clean package
                            cd ${WORKROOT}
                            cp GITWORK/zoa-common-docker-MF/netty/resolver-dns/target/netty-resolver-dns-*.jar GITWORK/zoa-common-docker-MF/
                            cp GITWORK/zoa-common-docker-MF/netty/common/target/netty-common-*.jar GITWORK/zoa-common-docker-MF/
                            cd GITWORK/zoa-common-docker-MF
                            tar cvf ${WORKROOT}/DIST/netty_jars.tar netty*.jar
                        '''
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Fetch testcontainers library on ${interp}") {
                    try {
                        withCredentials([usernamePassword(credentialsId: 'IZOA_Functional_Artifactory',
                              passwordVariable: 'AFPWD',
                              usernameVariable: 'AFUSER')]) {
                            sh '''
                                cd GITWORK/izoa-pi-keycloak-MF/server/buildutils/
                                wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/external/org.testcontainers/org.testcontainers.testcontainers-1.17.3.jar
                            '''
                        }
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Pre-build Keycloak and Kafka on ${interp}") {
                    try {
                        withCredentials([usernamePassword(credentialsId: 'IZOA_Functional_Artifactory',
                                passwordVariable: 'AFPWD',
                                usernameVariable: 'AFUSER'),
                            usernamePassword(credentialsId: 'IZOA_Build_GHtoken',
                                passwordVariable: 'GHTOKEN',
                                usernameVariable: 'GHUSER'),
                            string(credentialsId: 'zldazaadevfunc',
                                variable: 'IBMCLOUD_API_KEY')]) {
                            sh '''
                                ibmcloud login -r us-east
                                ibmcloud cr login --client docker
                              '''
                            sh '''
                                set +x
                                set -a
                                . ${HOME}/.af
                                set +a

                                export PATH=/usr/local/bin:/usr/local/sbin:${PATH}
                                set -x
                                WORKROOT=${PWD}
                                cd GITWORK
                                THISDIR=${PWD}
                                export AFUSER AFPWD
                                export TAG=${VER}
                                export OSTAG=${OSVER}
                                export ZRDDS_VERSION=${ZRDDSVER}
                                export KAFKA_VERSION=${KAFKAVER}
                                export PGKCTAG=${PGKCVER}
                                export NETTYVER
                                export VRMF
                                export FORKBRANCH FALLBACK_BRANCH
                                export BUILDER=${GHUSER}
                                export TOKEN=${GHTOKEN}
                                if [ "${ARCH}" = "x86_64" ]
                                then
                                  export UBU_ARCH="amd64"
                                  export OPENJDK_ARCH="x64"
                                else
                                  export UBU_ARCH=${ARCH}
                                  export OPENJDK_ARCH=${ARCH}
                                fi
                                cd zoa-common-docker-MF
                                export DOCKER_BUILDKIT_OLD=${DOCKER_BUILDKIT} DOCKER_BUILDKIT=1
                                echo "Pre-building Keycloak..."
                                docker build --build-arg FORKBRANCH=${FORKBRANCH} --build-arg FALLBACKBRANCH=${FALLBACK_BRANCH} --build-arg BUILDER=${BUILDER} --build-arg TOKEN=${TOKEN} --build-arg KEYCLOAK_VERSION=${KCVER} --build-arg QUARKUS_VERSION=${QUARKUSVER} --build-arg WILDFLY_VERSION=${WILDFLYVER} --build-arg NETTY_VERSION=${NETTYVER} --build-arg ARCH=${OPENJDK_ARCH} --build-arg VRMF=${VRMF} --output type=tar,dest=keycloak_out.tar --file ../izoa-pi-keycloak-MF/server/Dockerfile.prebuild ..
                                mv keycloak_out.tar ${WORKROOT}/DIST/
                                echo ""
                                #############################
                                # BEGIN KAFKA DECISION PART 1
                                #############################
                                # If the decision is made not to build Kafka from source, then comment out the following 2 lines...
                                echo "Pre-building Apache Kafka..."
                                docker build --build-arg FORKBRANCH=${FORKBRANCH} --build-arg FALLBACKBRANCH=${FALLBACK_BRANCH} --build-arg BUILDER=${BUILDER} --build-arg TOKEN=${TOKEN} --build-arg KAFKA_VERSION=${KAFKA_VERSION} --build-arg NETTY_VERSION=${NETTYVER} --output type=tar,dest=kafka_out.tar --file kafka/kafkabroker/Dockerfile.prebuild .
                                mv kafka_out.tar ${WORKROOT}/DIST/
                                # ... and instead add a line to fetch a static kafka_out.tar from Artifactory; e.g.:
                                # wget --no-check-certificate --user=${AFUSER} --password=${AFPWD} https://na.artifactory.swg-devops.com/artifactory/sys-izoa-generic-local/prereqs/external/kafka/${KAFKA_VERSION}/kafka_out.tar
                                #############################
                                # END KAFKA DECISION PART 1
                                #############################
                                echo ""
                                if [ "${DOCKER_BUILDKIT_OLD}x" = "x" ]
                                then
                                  unset DOCKER_BUILDKIT
                                else
                                  export DOCKER_BUILDKIT=${DOCKER_BUILDKIT_OLD}
                                fi
                            '''
                        }
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'FAILURE'
                    }
                }
                stage("Publish pre-built artifacts for ${interp}") {
                    try {
                        sh '''
                            cd DIST
                            jf rt upload keycloak_out.tar sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/
                            jf rt upload netty_jars.tar sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/
                            #############################
                            # BEGIN KAFKA DECISION PART 2
                            #############################
                            # If the decision is made not to build Kafka from source, then comment out the following 2 lines...
                            jf rt upload kafka_out.tar sys-izoa-generic-local/zoacommon/docker/${COMPACT_VER}/${GITBRANCH}/
                            #############################
                            # END KAFKA DECISION PART 2
                            #############################
                        '''
                    }
                    catch (ignored) {
                        unstable(message: "'${STAGE_NAME}' failed.")
                        currentBuild.result = 'UNSTABLE'
                    }
                }
            }
        }
    }
}

parallel branches

class StageDef implements Serializable {

    String interp

    StageDef(final String interp) {
        this.interp = interp
    }
}
