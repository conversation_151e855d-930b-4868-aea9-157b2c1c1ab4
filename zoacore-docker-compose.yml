services:
  gateway:
    image: icr.io/zoa-oci/zoa-gateway:${TAG}-x86_64
    ports:
      - ${IZOA_GATEWAY_PORT}:8085
    environment:
      - EXTERNAL_GATEWAY_HOST=${EXTERNAL_GATEWAY_HOST}
      - IZOA_GATEWAY_PORT=${IZOA_GATEWAY_PORT}
      - KC_INTERNAL_HOST=${KC_INTERNAL_HOST}
      - PI_FRAMEWORK_ROUTE=${PI_FRAMEWORK_ROUTE}
      - RATELIMIT_LIMIT=${ZAIOPS_TOTAL_RATE_LIMIT}
      - SERVER_HTTP2_ENABLED=${ENABLE_HTTP2}
      - SSL_DEBUG=${SSL_DEBUG}
      - ZAIOPS_GW_PASS=${ZAIOPS_ZOASVC_PASS}
      - ZAIOPS_KC_CONTEXT_ROOT=${ZAIOPS_KC_CONTEXT_ROOT}
      - ZAIOPS_KC_REALM=${ZAIOPS_KC_REALM}
      - ZRDDS_API_HOST=${ZRDDS_API_HOST:-zrddsapi}
      - ZRDDS_CORE_HOST=${ZRDDS_CORE_HOST:-zrddsapi}
      - TOPOLOGY_UI=${TOPOLOGY_UI:-zrddsui}
      - ZRDDS_KB_HOST=${ZRDDS_KB_HOST:-kafkabridge}
      # To change log level to debug, uncomments the next two lines.
      # Other possible values for log levels are TRACE DEBUG INFO WARN ERROR FATAL
      # - LOGGING_LEVEL_ROOT=DEBUG
      # - LOGGING_LEVEL_COM_IBM_ZSYSTEM_ZMANAGED_PIGATEWAY=DEBUG
    hostname: gateway
    user: "${ZOA_UID}:0"
    container_name: zoa-gateway
    # Linux OS only !!!!
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # set host=host.docker.internal in .zoa_factory.config on linux for xdebug
    #   - ${host:-host}:host-gateway
    networks:
      - zaiops
    volumes:
      - zaiops_shared:/shared:ro
    restart: always
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
  auth:
    image: icr.io/zoa-oci/zoa-auth:${TAG}-x86_64
    container_name: zoa-auth
    hostname: auth
    user: "${ZOA_UID}:0"
    environment:
      - EXTERNAL_GATEWAY_HOST=${EXTERNAL_GATEWAY_HOST}
      - HOSTNAME_STRICT=${HOSTNAME_STRICT}
      - IZOA_GATEWAY_PORT=${IZOA_GATEWAY_PORT}
      - KC_FORCE_EXTERNAL_HOSTNAME=${KC_FORCE_EXTERNAL_HOSTNAME}
      - KC_TRUSTSTORE_PATHS=${KC_TRUSTSTORE_PATHS}
      - SSL_DEBUG=${SSL_DEBUG}
      - ZAIOPS_KC_BOOTSTRAP_ADMIN=${ZAIOPS_KC_BOOTSTRAP_ADMIN}
      - ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED=${ZAIOPS_KC_BOOTSTRAP_ADMIN_ENABLED}
      - ZAIOPS_KC_BOOTSTRAP_PASSWORD=${ZAIOPS_KC_BOOTSTRAP_PASSWORD}
      - ZAIOPS_KC_CACHE_MODE=${ZAIOPS_KC_CACHE_MODE}
      - ZAIOPS_KC_CONTEXT_ROOT=${ZAIOPS_KC_CONTEXT_ROOT}
      - ZAIOPS_KC_DB=${ZAIOPS_KC_DB}
      - ZAIOPS_KC_DB_PWD=${ZAIOPS_KC_DB_PWD}
      - ZAIOPS_KC_DB_URL=${ZAIOPS_KC_DB_URL}
      - ZAIOPS_KC_DB_USER=${ZAIOPS_KC_DB_USER}
      - ZAIOPS_KC_HTTPS_PORT=${ZAIOPS_KC_HTTPS_PORT}
      - ZAIOPS_KC_KEYSTORE_FILE=${ZAIOPS_KC_KEYSTORE_FILE}
      - ZAIOPS_KC_KEYSTORE_TYPE=${ZAIOPS_KC_KEYSTORE_TYPE}
      - ZAIOPS_KC_PASS=${ZAIOPS_ZOASVC_PASS}
      - ZAIOPS_KC_TRUSTSTORE_FILE=${ZAIOPS_KC_TRUSTSTORE_FILE}
      - ZAIOPS_KC_TRUSTSTORE_TYPE=${ZAIOPS_KC_TRUSTSTORE_TYPE}
      - ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED=${ZAIOPS_KEYCLOAK_HEALTHAPI_ENABLED:-false}
      - ZAIOPS_KEYCLOAK_IP=${ZAIOPS_KEYCLOAK_IP}
      - ZAIOPS_KEYCLOAK_LOGLEVEL=${ZAIOPS_KEYCLOAK_LOGLEVEL:-INFO}
      - ZAIOPS_KEYCLOAK_METRICAPI_ENABLED=${ZAIOPS_KEYCLOAK_METRICAPI_ENABLED:-false}
      - ZAIOPS_KEYCLOAK_PORT=${ZAIOPS_KEYCLOAK_PORT}
      - ZAIOPS_PROXY_HEADERS=${ZAIOPS_PROXY_HEADERS}
      - ZAIOPS_TLS_VERSION=${ZAIOPS_TLS_VERSION}
    #ports:
    #  - ${ZAIOPS_KEYCLOAK_PORT}:8443
    restart: always
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops
    volumes:
      - zaiops_keycloak:/opt/keycloak/data/h2
      - zaiops_shared:/shared:ro
  discovery:
    image: icr.io/zoa-oci/zoa-service-discovery:${TAG}-x86_64
    # TODO: Remove the 'ports' section before shipping this code
    # ports:
    #   - 8761:8761
    restart: always
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    hostname: discovery
    user: "${ZOA_UID}:0"
    container_name: zoa-service-discovery
    networks:
      - zaiops
    environment:
      # To change log level to debug, uncomments the next two lines.
      # Other possible values for log levels are TRACE DEBUG INFO WARN ERROR FATAL
      # - LOGGING_LEVEL_ROOT=DEBUG
      # - LOGGING_LEVEL_COM_IBM_ZSYSTEM_ZMANAGED_IZOAPIDISCOVERYSERVER=DEBUG
      - JAVA_OPTS=
         -DEUREKA_URI=http://localhost:8761/eureka
  kafkabroker:
    image: icr.io/zoa-oci/zoa-kafkabroker:${TAG}-x86_64
    environment:
      - KAFKA_NODE_ROLE=broker
      - KAFKA_HEAP_OPTS=-Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g
      - KAFKA_OPTS=-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}
      - ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS}
      - ZAIOPS_KAFKA_RETENTION_HOURS=${ZAIOPS_KAFKA_RETENTION_HOURS}
      # To enable SASL/PLAIN Authentication uncomment the next line
      # - KAFKA_AUTHENTICATION_ENABLED=true
      # - KAFKA_SSL_ENABLED=false
      - SSL_DEBUG=${SSL_DEBUG}
    ports:
      - ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}:9092
      - ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}:9093
    volumes:
      - zaiops_kafkabroker:/opt/kafka/data
      - zaiops_shared:/shared:ro
    hostname: kafkabroker
    user: "${ZOA_UID}:0"
    container_name: zoa-kafkabroker
    restart: always
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops
  kafkacontroller:
    image: icr.io/zoa-oci/zoa-kafkabroker:${TAG}-x86_64
    environment:
      - KAFKA_NODE_ROLE=controller
      - KAFKA_HEAP_OPTS=-Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g
      - KAFKA_OPTS=-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache
      - ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS}
      - ZAIOPS_KAFKA_RETENTION_HOURS=${ZAIOPS_KAFKA_RETENTION_HOURS}
      # To enable SASL/PLAIN Authentication uncomment the next line
      # - KAFKA_AUTHENTICATION_ENABLED=true
      # - KAFKA_SSL_ENABLED=false
      - SSL_DEBUG=${SSL_DEBUG}
    volumes:
      - zaiops_kafkabroker:/opt/kafka/data
      - zaiops_shared:/shared:ro
    hostname: kafkacontroller
    user: "${ZOA_UID}:0"
    container_name: zoa-kafkacontroller
    restart: always
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops

volumes:
  zaiops_kafkabroker:
  zaiops_keycloak:
  zaiops_shared:
    external: true

networks:
  zaiops:
    driver: bridge
