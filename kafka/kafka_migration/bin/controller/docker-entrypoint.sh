#! /bin/bash

LEGACY_BROKER_LOG_DIR=${KAFKA_HOME}/data/kafka-logs
NEW_BROKER_LOG_DIR=${KAFKA_HOME}/data/kafka-broker-logs
CONTROLLER_LOG_DIR=${KAFKA_HOME}/data/kafka-controller-logs
CONTROLLERCONFIG=${KAFKA_HOME}/config/kraft/controller.properties
LOGCONFIG=${KAFKA_HOME}/config/log4j.properties

NODE_ID=$( grep ^node.id ${CONTROLLERCONFIG} | cut -f 2 -d '=' )
NODE_ROLE=$( grep ^process.roles ${CONTROLLERCONFIG} | cut -f 2 -d '=' )

if [ -d ${LEGACY_BROKER_LOG_DIR} ] && [ ! -d ${NEW_BROKER_LOG_DIR} ]
then
  echo "Found legacy broker log directory at '${LEGACY_BROKER_LOG_DIR}'. WIll move it to '${NEW_BROKER_LOG_DIR}'."
  mv ${LEGACY_BROKER_LOG_DIR} ${NEW_BROKER_LOG_DIR}
fi
# Ensure storage is correctly formatted
echo "Formatting Node '${NODE_ID}' storage for Cluster ID '${CLUSTER_ID}' and '${NODE_ROLE}' role using configuration file '${KAFKA_HOME}/config/kraft/${NODE_ROLE}.properties'..."
${KAFKA_HOME}/bin/kafka-storage.sh format -t ${CLUSTER_ID} -c ${KAFKA_HOME}/config/kraft/${NODE_ROLE}.properties
# Start Kafka process based on node role
echo "Starting Node '${NODE_ID}' with '${NODE_ROLE}' role using configuration file '${KAFKA_HOME}/config/kraft/${NODE_ROLE}.properties'..."
exec "${KAFKA_HOME}/bin/kafka-server-start.sh" "${KAFKA_HOME}/config/kraft/${NODE_ROLE}.properties"
