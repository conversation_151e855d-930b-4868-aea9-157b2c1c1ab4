#!/bin/bash

KAFKA_ZOOKEEPER_HOST="${ZAIOPS_KAFKA_ZOOKEEPER_HOST:-zookeeper}"
KAFKA_ZOOKEEPER_PORT="${ZAIOPS_KAFKA_ZOOKEEPER_PORT:-2181}"
KAFKA_SSL_ENABLED="${KAFKA_SSL_ENABLED:-false}"
KAFKA_AUTHENTICATION_ENABLED="${KAFKA_AUTHENTICATION_ENABLED:-false}"
KAFKA_PASS=`echo "${ZAIOPS_KAFKA_PASS}" | base64 -d`
KAFKA_RETENTION_HOURS="${ZAIOPS_KAFKA_RETENTION_HOURS:-24}"

# Prepare TLS artifacts
if [ -f /shared/config/zoasvc.tls ]
then
  ORIGIN=`pwd`
  mkdir -p /ssl && rm -f /ssl/*
  cd /ssl
  cat /shared/config/zoasvc.tls | base64 -d | tar xz
  cd ${ORIGIN}
else
  echo "ERROR: TLS artifacts not found."
  exit 1
fi

# Check if Zookeeper is running.
# Wait for 60 sec for <PERSON><PERSON> to come up.
# If it is not up by the set 60sec timeout we will continue starting the Zookeeper.
echo "Waiting for Zookeeper to be ready. ${KAFKA_ZOOKEEPER_HOST}:${KAFKA_ZOOKEEPER_PORT}"
until $(wait-for-it.sh --quiet --strict --timeout=60 ${KAFKA_ZOOKEEPER_HOST}:${KAFKA_ZOOKEEPER_PORT} > /dev/null); do
  echo "Waiting for Zookeeper to be ready. ${KAFKA_ZOOKEEPER_HOST}:${KAFKA_ZOOKEEPER_PORT}"
done
echo "Zookeeper ready. ${KAFKA_ZOOKEEPER_HOST}:${KAFKA_ZOOKEEPER_PORT}"
echo "Sleeping for 20 seconds to allow Zookeeper to purge stale connections..."
sleep 20

# Starting Kafka Broker
exec "$KAFKA_HOME/bin/kafka-server-start.sh" "$KAFKA_HOME/config/server.properties"
