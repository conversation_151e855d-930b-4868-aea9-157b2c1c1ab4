services:
  zookeeper:
    image: ibm-zai<PERSON>/zoa-zookeeper:${TAG}-${ARCH}
    volumes:
      - z<PERSON><PERSON>_zookeeper:/opt/kafka/data
      - ./config/zookeeper:/opt/kafka/config
    hostname: zookeeper
    container_name: zoa-zookeeper
    environment:
      - KAFKA_HEAP_OPTS=-Xmx512m -Xms512m
      - KAFKA_OPTS=-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache
    networks:
      - zaiops
  kafkabroker:
    image: ibm-zaiops/zoa-kafkabroker:${TAG}-${ARCH}
    environment:
      - KAFKA_HEAP_OPTS=-Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g
      - KAFKA_OPTS=-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}
      - ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}
      - ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS}
      - ZAIOPS_KAFKA_RETENTION_HOURS=${ZAIOPS_KAFKA_RETENTION_HOURS}
      - SSL_DEBUG=false
    ports:
      - ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}
      - ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}
    volumes:
      - zaiops_kafkabroker:/opt/kafka/data
      - zaiops_shared:/shared:ro
      - ./config/kafkabroker:/opt/kafka/config
      - ./bin/broker/docker-entrypoint.sh:/usr/local/bin/docker-entrypoint.sh
    hostname: kafkabroker
    container_name: zoa-kafkabroker
    networks:
      - zaiops
  kafkacontroller:
    image: ibm-zaiops/zoa-kafkabroker:${TAG}-${ARCH}
    environment:
      - KAFKA_NODE_ROLE=controller
      - KAFKA_HEAP_OPTS=-Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g
      - KAFKA_OPTS=-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache
      - ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS}
      - ZAIOPS_KAFKA_RETENTION_HOURS=${ZAIOPS_KAFKA_RETENTION_HOURS}
      - CLUSTER_ID=${CLUSTER_ID}
      - SSL_DEBUG=false
    volumes:
      - zaiops_kafkabroker:/opt/kafka/data
      - zaiops_shared:/shared:ro
      - ./config/kafkabroker:/opt/kafka/config
      - ./bin/controller/docker-entrypoint.sh:/usr/local/bin/docker-entrypoint.sh
    hostname: kafkacontroller
    container_name: zoa-kafkacontroller
    networks:
      - zaiops

volumes:
  zaiops_kafkabroker:
  zaiops_zookeeper:
  zaiops_shared:
    external: true

networks:
  zaiops:
    driver: bridge
