#!/bin/bash

SCRIPTDIR="$(cd "$(dirname "$0")" && pwd)"
OCI_AGENT=${1}
if [ "${OCI_AGENT}" != "docker" ] && [ "${OCI_AGENT}" != "podman" ]; then
  echo ""
  echo "ERROR: Provided OCI agent '${OCI_AGENT}' is invalid or not supported."
  echo ""
  echo "Usage:"
  echo " $0 [ docker | podman ]"
  echo ""
  exit 1
fi

if [ "${OCI_AGENT}" == "docker" ]; then
  COMMON_CONTAINER_PREFIX="zoa-"
else
  COMMON_CONTAINER_PREFIX=""
fi

export ARCH=$(uname -m)

MIGRATION_TIMEOUT=3600

podmanZkUp() {
  echo "Starting Zookeeper service..."
  ${OCI_AGENT} run \
    --name ${COMMON_CONTAINER_PREFIX}zookeeper -d \
    --security-opt no-new-privileges \
    ${LOGOPTS[@]} \
    -e KAFKA_HEAP_OPTS="-Xmx512m -Xms512m" \
    -e KAFKA_OPTS="-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache" \
    -u ${ZOA_UID}:0 \
    -v ${COMPOSE_PROJECT_NAME}_zaiops_zookeeper:/opt/kafka/data \
    -v ./config/zookeeper:/opt/kafka/config \
    -h zookeeper --network ${COMPOSE_PROJECT_NAME}_zaiops \
    ibm-zaiops/zoa-zookeeper:${TAG}-${ARCH} >/dev/null
}

podmanKcUp() {
  echo "Starting Kafka Controller service..."
  ${OCI_AGENT} run \
    --name ${COMMON_CONTAINER_PREFIX}kafkacontroller -d \
    --security-opt no-new-privileges \
    ${LOGOPTS[@]} \
    -e KAFKA_NODE_ROLE=controller \
    -e KAFKA_HEAP_OPTS="-Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g" \
    -e KAFKA_OPTS="-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache" \
    -e ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS} \
    -e ZAIOPS_KAFKA_RETENTION_HOURS=${ZAIOPS_KAFKA_RETENTION_HOURS} \
    -e CLUSTER_ID=${CLUSTER_ID} \
    -e SSL_DEBUG=false \
    -u ${ZOA_UID}:0 \
    -v ${COMPOSE_PROJECT_NAME}_zaiops_kafkabroker:/opt/kafka/data \
    -v zaiops_shared:/shared:ro \
    -v ./config/kafkabroker:/opt/kafka/config \
    -v ./bin/controller/docker-entrypoint.sh:/usr/local/bin/docker-entrypoint.sh \
    -h kafkacontroller --network ${COMPOSE_PROJECT_NAME}_zaiops \
    ibm-zaiops/zoa-kafkabroker:${TAG}-${ARCH} >/dev/null
}

podmanKbUp() {
  echo "Starting Kafka Broker service..."
  ${OCI_AGENT} run \
    --name ${COMMON_CONTAINER_PREFIX}kafkabroker -d \
    --security-opt no-new-privileges \
    ${LOGOPTS[@]} \
    -e KAFKA_HEAP_OPTS="-Xmx${ZAIOPS_KAFKA_HEAP}g -Xms${ZAIOPS_KAFKA_HEAP}g" \
    -e KAFKA_OPTS="-Xdump:java:none -Xdump:java:events=gpf+abort+traceassert+corruptcache" \
    -e ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST} \
    -e ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT} \
    -e ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT=${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT} \
    -e ZAIOPS_KAFKA_PASS=${ZAIOPS_ZOASVC_PASS} \
    -e ZAIOPS_KAFKA_RETENTION_HOURS=${ZAIOPS_KAFKA_RETENTION_HOURS} \
    -e SSL_DEBUG=false \
    -u ${ZOA_UID}:0 \
    -v ${COMPOSE_PROJECT_NAME}_zaiops_kafkabroker:/opt/kafka/data \
    -v zaiops_shared:/shared:ro \
    -v ./config/kafkabroker:/opt/kafka/config \
    -v ./bin/broker/docker-entrypoint.sh:/usr/local/bin/docker-entrypoint.sh \
    -p ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT} \
    -p ${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT} \
    -h kafkabroker --network ${COMPOSE_PROJECT_NAME}_zaiops \
    ibm-zaiops/zoa-kafkabroker:${TAG}-${ARCH} >/dev/null
}

## MAIN
cd ${SCRIPTDIR}

#Make sure all services for original deployment are running
${SCRIPTDIR}/../bin/${OCI_AGENT}ManageZoa.sh up

# Get cluster ID from zookeeper
CLUSTER_ID=$(${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}zookeeper bash -c "/opt/kafka/bin/zookeeper-shell.sh zookeeper:2181 get /cluster/id" | grep -o \"id\":\".*\" | cut -f 2 -d ':' | tr -d \")
echo "Cluster ID is '${CLUSTER_ID}'."

# Get Zookeeper effective configuration
${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}zookeeper bash -c "cd /opt/kafka/config ; find . -type f | xargs tar cf /tmp/zookeeper_config.tar"
${OCI_AGENT} cp ${COMMON_CONTAINER_PREFIX}zookeeper:/tmp/zookeeper_config.tar .
mkdir -p config/zookeeper
tar xf zookeeper_config.tar -C config/zookeeper && rm zookeeper_config.tar

# Get Kafka Broker effective configuration
${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkabroker bash -c "cd /opt/kafka/config ; find . -type f | xargs tar cf /tmp/kafkabroker_config.tar"
${OCI_AGENT} cp ${COMMON_CONTAINER_PREFIX}kafkabroker:/tmp/kafkabroker_config.tar .
mkdir -p config/kafkabroker
tar xf kafkabroker_config.tar -C config/kafkabroker && rm kafkabroker_config.tar

# Update .env file based on settings for original deployment
echo "CLUSTER_ID=${CLUSTER_ID}" >${SCRIPTDIR}/.env
grep ^TAG ${SCRIPTDIR}/../.zoa_factory.config >>${SCRIPTDIR}/.env
grep ^COMPOSE_PROJECT_NAME ${SCRIPTDIR}/../.zoa_factory.config >>${SCRIPTDIR}/.env
grep ^ZAIOPS_KAFKA_RETENTION_HOURS ${SCRIPTDIR}/../zoa_env.config >>${SCRIPTDIR}/.env
grep ^ZAIOPS_ZOASVC_PASS ${SCRIPTDIR}/../zoa_env.config >>${SCRIPTDIR}/.env
grep ^ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT ${SCRIPTDIR}/../zoa_env.config >>${SCRIPTDIR}/.env
grep ^ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT ${SCRIPTDIR}/../zoa_env.config >>${SCRIPTDIR}/.env
grep ^ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST ${SCRIPTDIR}/../zoa_env.config >>${SCRIPTDIR}/.env
grep ^ZAIOPS_KAFKA_HEAP ${SCRIPTDIR}/../zoa_env.config >>${SCRIPTDIR}/.env

# Update controller configuration file
echo 'zookeeper.metadata.migration.enable=true' >>${SCRIPTDIR}/config/kafkabroker/kraft/controller.properties
echo 'zookeeper.connect=zookeeper:2181' >>${SCRIPTDIR}/config/kafkabroker/kraft/controller.properties
echo 'inter.broker.listener.name=PLAINTEXT' >>${SCRIPTDIR}/config/kafkabroker/kraft/controller.properties
sed -i -e "s%localhost%kafkacontroller%g" ${SCRIPTDIR}/config/kafkabroker/kraft/controller.properties
sed -i -e "s%9093%9092%g" ${SCRIPTDIR}/config/kafkabroker/kraft/controller.properties
sed -i -e "s%^log.dirs=.*$%log.dirs=\/opt\/kafka\/data\/kafka-controller-logs%g" ${SCRIPTDIR}/config/kafkabroker/kraft/controller.properties

# Update broker configuration file
echo 'inter.broker.protocol.version=3.7' >>${SCRIPTDIR}/config/kafkabroker/server.properties
echo 'zookeeper.metadata.migration.enable=true' >>${SCRIPTDIR}/config/kafkabroker/server.properties
echo 'controller.quorum.voters=1@kafkacontroller:9092' >>${SCRIPTDIR}/config/kafkabroker/server.properties
echo 'controller.listener.names=CONTROLLER' >>${SCRIPTDIR}/config/kafkabroker/server.properties
sed -i -e "s%^log.dirs=.*$%log.dirs=\/opt\/kafka\/data\/kafka-broker-logs%g" ${SCRIPTDIR}/config/kafkabroker/server.properties
sed -i -e "s%^zookeeper.connect=.*$%zookeeper.connect=zookeeper:2181%g" ${SCRIPTDIR}/config/kafkabroker/server.properties
sed -i -r -e "s%(^listener.security.protocol.map=.*$)%\1,CONTROLLER:PLAINTEXT%g" ${SCRIPTDIR}/config/kafkabroker/server.properties

# Update log4j configuration files
sed -i -e "s%INFO%DEBUG%g" ${SCRIPTDIR}/config/kafkabroker/log4j.properties
sed -i -e "s%INFO%DEBUG%g" ${SCRIPTDIR}/config/zookeeper/log4j.properties

# Shut down any running containers from original deployment
${SCRIPTDIR}/../bin/${OCI_AGENT}ManageZoa.sh down

. ${SCRIPTDIR}/.env

# Start zookeeper
if [ "${OCI_AGENT}" == "docker" ]; then
  docker compose up -d zookeeper
else
  podmanZkUp
fi
sleep 15
# Start controller
if [ "${OCI_AGENT}" == "docker" ]; then
  docker compose up -d kafkacontroller
else
  podmanKcUp
fi
sleep 15
# Start broker
if [ "${OCI_AGENT}" == "docker" ]; then
  docker compose up -d kafkabroker
else
  podmanKbUp
fi
sleep 15

COUNT=0
while :; do
  ${OCI_AGENT} logs ${COMMON_CONTAINER_PREFIX}kafkacontroller 2>&1 | grep -q "Completed migration"
  if [ $? -eq 0 ]; then
    echo "Metadata migration completed after ${COUNT} seconds."
    break
  elif [ ${COUNT} -ge ${MIGRATION_TIMEOUT} ]; then
    # Abort if metadata migration still isn't complete after 1 hour
    echo "Metadata migration still not completed after ${COUNT} seconds."
    exit 1
  else
    sleep 5
    COUNT=$((${COUNT} + 5))
  fi
done

${OCI_AGENT} stop ${COMMON_CONTAINER_PREFIX}kafkabroker
# Remove broker meta.properties file so that it can be regenerated once broker starts up in KRaft mode;
#   Kafka seems to be get confused when it is supposed to start up a KRaft broker based on a Zookeeper-originated
#   meta.properties file and claims that it can't find the node ID.
${OCI_AGENT} exec ${COMMON_CONTAINER_PREFIX}kafkacontroller bash -c 'rm -f /opt/kafka/data/kafka-broker-logs/meta.properties'
if [ "${OCI_AGENT}" == "docker" ]; then
  docker compose down
else
  podman stop ${COMMON_CONTAINER_PREFIX}kafkacontroller ${COMMON_CONTAINER_PREFIX}zookeeper
  podman rm ${COMMON_CONTAINER_PREFIX}kafkacontroller ${COMMON_CONTAINER_PREFIX}zookeeper
fi
