# Image creation stage
FROM icr.io/zoa-oci/zoacommon-jre21-micro:21.0.5_11-x86_64

ARG REPO=unknown
ARG COMMIT=unknown
ARG KAFKA_TYPE=2.13
ARG KAFKA_VERSION=_see.buildenv
ARG AF_USER=unknown
ARG AF_PWD=unknown

LABEL feature="IBM Z AIOps - Common Services - Core"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

ENV KAFKA_HOME=/opt/kafka
ENV PATH=${PATH}:${KAFKA_HOME}/bin

COPY ./zoa-common-docker-MF/kafka_out.tar /

RUN tar xvf /kafka_out.tar -C /tmp/ \
    && tar xzf /tmp/staging/kafka/distribution/kafka_${KAFKA_TYPE}-${KAFKA_VERSION}.tgz \
    && rm -f /kafka_out.tar \
    && rm -Rf /tmp/staging \
    && mv /kafka_${KAFKA_TYPE}-${KAFKA_VERSION} /opt/kafka \
    && mkdir -p /opt/kafka/data \
    && mkdir -p /ssl \
    && mkdir -p /opt/kafka/plugin

COPY ./zoa-common-docker-MF/kafka/kafkabroker/config/broker.properties ./zoa-common-docker-MF/kafka/kafkabroker/config/controller.properties ./zoa-common-docker-MF/kafka/kafkabroker/config/log4j.properties /opt/kafka/config
COPY ./zoa-common-docker-MF/kafka/kafkabroker/bin/create-topics.sh ./zoa-common-docker-MF/kafka/kafkabroker/bin/docker-entrypoint.sh ./zoa-common-docker-MF/kafka/kafkabroker/bin/healthcheck.sh /usr/local/bin/

# EXPOSE 9092 9093

RUN chmod +x /usr/local/bin/*.sh \
  && chgrp -R 0 /opt/kafka /ssl \
  && chmod -R g=u /opt/kafka /ssl

USER kafka

HEALTHCHECK NONE
# HEALTHCHECK CMD /usr/local/bin/healthcheck.sh

CMD ["docker-entrypoint.sh"]
