##################################################################################
# BUILD APACHE KAFKA - Compilation stage
##################################################################################

FROM icr.io/zoa-oci/zoatools-maven:3.9.9-x86_64 AS kafkabuilder

ARG KAFKA_TYPE=2.13
ARG KAFKA_VERSION=_see.buildenv
ARG NETTY_VERSION=see_.buildenv
ARG FORKBRANCH=develop
ARG FALLBACKBRANCH=develop
ARG BUILDER=TBD
ARG TOKEN=TBD

user root

RUN mkdir -p /kafka_build
COPY ./netty-resolver-dns-*.jar ./netty-common-*.jar /kafka_build/
WORKDIR /kafka_build

RUN ORIGIN=${PWD} && \
  NETTY_SHORTVER=${NETTY_VERSION##netty-} && \
  mvn install:install-file -Dfile=netty-resolver-dns-${NETTY_SHORTVER}.jar -DgroupId=io.netty -DartifactId=netty-resolver-dns -Dversion=${NETTY_SHORTVER} -Dpackaging=jar && \
  mvn install:install-file -Dfile=netty-common-${NETTY_SHORTVER}.jar -DgroupId=io.netty -DartifactId=netty-common -Dversion=${NETTY_SHORTVER} -Dpackaging=jar && \
  PASSKEY=$( echo ${TOKEN} | base64 -d ) && \
  mkdir -p /staging/kafka/libs /staging/kafka/distribution && \
  RESULT=$( git ls-remote --heads "https://${BUILDER}:${PASSKEY}@github.ibm.com/ZLDAforks/kafka-kafka" ${KAFKA_VERSION}z-${FORKBRANCH} | awk '{ print $2 }' ) && \
  if [ -z ${RESULT} ] ; then PULLBRANCH=${FALLBACKBRANCH} ; else PULLBRANCH=${FORKBRANCH} ; fi && \
  git clone "https://${BUILDER}:${PASSKEY}@github.ibm.com/ZLDAforks/kafka-kafka.git" && \
  cd kafka-kafka && \
  git checkout ${KAFKA_VERSION}z-${PULLBRANCH} && \
  ./gradlew clean releaseTarGz && \
  for JAR in $( find . -name "*.jar" | grep \/build\/ | grep -v dependant-libs ) ; do cp ${JAR} /staging/kafka/libs ; done && \
  cp core/build/distributions/kafka_${KAFKA_TYPE}-${KAFKA_VERSION}.tgz /staging/kafka/distribution

##################################################################################
# EXPORT IMAGE - Export Kafka artifacts
##################################################################################

FROM scratch AS export-stage

COPY --from=kafkabuilder /staging/kafka /staging/kafka
