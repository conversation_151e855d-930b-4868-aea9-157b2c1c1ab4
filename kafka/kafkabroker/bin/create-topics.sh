#! /bin/bash

<PERSON><PERSON><PERSON>_BOOTSTRAP_SERVER_HOST="kafkabroker"
KAFKA_BOOTSTRAP_SERVER_PORT="19092"

# Check if Kafka Broker is running.
# Wait for 60 sec for Kafka Broker to come up.
# If it is not up by the set 60sec timeout we will continue checking for Kafka Broker to be up.
echo "Waiting for Kafka broker at ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT} to be ready,,,"
until $(wait-for-it.sh --quiet --strict --timeout=60 ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT} > /dev/null)
do
  echo "Waiting for Kafka broker at ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT} to be ready..."
done
echo "<PERSON>fka broker at ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT} ready."

# Wait 10 sec for the cluster to be ready. 
# sleep 10

#  KAFKA_CREATE_TOPICS expected format -- name:cleanup.policy
KAFKA_CREATE_TOPICS="${ZAIOPS_KAFKA_CREATE_TOPICS:-CDP-CSV-HEADER:compact}"
IFS=","
for topicToCreate in ${KAFKA_CREATE_TOPICS}
do
  echo "Creating Topic ${topicToCreate}..."
  IFS=':' read -r -a topicConfig <<< "${topicToCreate}"
  config=
  if [ -n "${topicConfig[1]}" ]
  then
    config="--config=cleanup.policy=${topicConfig[1]}"
  fi
  COMMAND="${KAFKA_HOME}/bin/kafka-topics.sh --create --if-not-exists --bootstrap-server ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT} --topic ${topicConfig[0]} ${config} "
  eval "${COMMAND}"
done

wait
