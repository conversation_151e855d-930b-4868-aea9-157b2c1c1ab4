#! /bin/bash

<PERSON>AFKA_SSL_ENABLED="${KAFKA_SSL_ENABLED:-true}"
KAFKA_AUTHENTICATION_ENABLED="${KAFKA_AUTHENTICATION_ENABLED:-false}"
KAFKA_PASS=`echo "${ZAIOPS_KAFKA_PASS}" | base64 -d`
KAFKA_RETENTION_HOURS="${ZAIOPS_KAFKA_RETENTION_HOURS:-24}"
NODE_ROLE="${KAFKA_NODE_ROLE:-unset}"

# Prepare TLS artifacts
if [ -f /shared/config/zoasvc.tls ]
then
  ORIGIN=`pwd`
  mkdir -p /ssl && rm -f /ssl/*
  cd /ssl
  cat /shared/config/zoasvc.tls | base64 -d | tar xz
  cd ${ORIGIN}
else
  echo "ERROR: TLS artifacts not found."
  exit 1
fi  

if [ "${SUBDOMAIN}x" != "x" ]
then
  CONTROLLER_HOST=kafkacontroller.${SUBDOMAIN}
  BROKER_HOST=kafkabroker.${SUBDOMAIN}
else
  CONTROLLER_HOST=kafkacontroller
  BROKER_HOST=kafkabroker
fi

SSL_DEBUG_PARM=""
if [ "$( echo ${SSL_DEBUG} | tr [:lower:] [:upper:] )" = "TRUE" ]
then
  SSL_DEBUG_PARM="-Djavax.net.debug=ssl"
  export KAFKA_OPTS="${KAFKA_OPTS} ${SSL_DEBUG_PARM}"
fi

BROKER_LOG_DIR=${KAFKA_HOME}/data/kafka-broker-logs
CONTROLLER_LOG_DIR=${KAFKA_HOME}/data/kafka-controller-logs
BROKERCONFIG=${KAFKA_HOME}/config/broker.properties
CONTROLLERCONFIG=${KAFKA_HOME}/config/controller.properties
LOGCONFIG=${KAFKA_HOME}/config/log4j.properties
ALISTENERS="advertised.listeners=INTERNAL://${BROKER_HOST}:19092"
LISTENERS="listeners=INTERNAL://:19092"
MAP="listener.security.protocol.map=CONTROLLER:PLAINTEXT,INTERNAL:PLAINTEXT"

if [ "${NODE_ROLE}" == "broker" ]
then
  # Configure broker.properties file
  sed -i -e "s%__CONTROLLER_HOST__%${CONTROLLER_HOST}%g" ${BROKERCONFIG}
  sed -i -e "s%__BROKER_HOST__%${BROKER_HOST}%g" ${BROKERCONFIG}
  sed -i -e "s%^ssl.truststore.password=.*%ssl.truststore.password=${KAFKA_PASS}%g" ${BROKERCONFIG}
  sed -i -e "s%^ssl.keystore.password=.*%ssl.keystore.password=${KAFKA_PASS}%g" ${BROKERCONFIG}
  sed -i -e "s%^ssl.key.password=.*%ssl.key.password=${KAFKA_PASS}%g" ${BROKERCONFIG}
  sed -i -e "s%^log.retention.hours=.*%log.retention.hours=${KAFKA_RETENTION_HOURS}%g" ${BROKERCONFIG}
  if [[ -n "${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}" ]]
  then
    if ${KAFKA_AUTHENTICATION_ENABLED}
    then
      ALISTENERS="${ALISTENERS},BROKER_NONTLS://${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}"
      LISTENERS="${LISTENERS},BROKER_NONTLS://:9092"
      MAP="${MAP},BROKER_NONTLS:SASL_PLAINTEXT"
      if ${KAFKA_SSL_ENABLED}
      then
        ALISTENERS="${ALISTENERS},BROKER_TLS://${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}"
        LISTENERS="${LISTENERS},BROKER_TLS://:9093"
        MAP="${MAP},BROKER_TLS:SASL_SSL"
      fi
    else
      ALISTENERS="${ALISTENERS},BROKER_NONTLS://${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_PORT}"
      LISTENERS="${LISTENERS},BROKER_NONTLS://:9092"
      MAP="${MAP},BROKER_NONTLS:PLAINTEXT"
      if ${KAFKA_SSL_ENABLED}
      then
        ALISTENERS="${ALISTENERS},BROKER_TLS://${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_HOST}:${ZAIOPS_KAFKA_BOOTSTRAP_SERVER_SSL_PORT}"
        LISTENERS="${LISTENERS},BROKER_TLS://:9093"
        MAP="${MAP},BROKER_TLS:SSL"
      fi
    fi
  fi
  sed -i -e "s%^advertised.listeners=.*$%${ALISTENERS}%" ${BROKERCONFIG}
  sed -i -e "s%^listeners=.*$%${LISTENERS}%g" ${BROKERCONFIG}
  sed -i -e "s%^listener.security.protocol.map=.*$%${MAP}%g" ${BROKERCONFIG}
  # In the future, dynamically update Node ID
  # sed -i -e "s%^node.id=.*%node.id=<SOME_ID>%g" ${BROKERCONFIG}
  # Get Node ID
  NODE_ID=$( grep ^node.id ${BROKERCONFIG} | cut -f 2 -d '=' )
  # Call create topics scripts
  create-topics.sh &
elif [ "${NODE_ROLE}" == "controller" ]
then
  # Configure controller.properties file
  sed -i -e "s%__CONTROLLER_HOST__%${CONTROLLER_HOST}%g" ${CONTROLLERCONFIG}
  sed -i -e "s%__BROKER_HOST__%${BROKER_HOST}%g" ${CONTROLLERCONFIG}
  sed -i -e "s%^ssl.truststore.password=.*%ssl.truststore.password=${KAFKA_PASS}%g" ${CONTROLLERCONFIG}
  sed -i -e "s%^ssl.keystore.password=.*%ssl.keystore.password=${KAFKA_PASS}%g" ${CONTROLLERCONFIG}
  sed -i -e "s%^ssl.key.password=.*%ssl.key.password=${KAFKA_PASS}%g" ${CONTROLLERCONFIG}
  sed -i -e "s%^log.retention.hours=.*%log.retention.hours=${KAFKA_RETENTION_HOURS}%g" ${CONTROLLERCONFIG}
  if ${KAFKA_AUTHENTICATION_ENABLED}
  then
    MAP="${MAP},BROKER_NONTLS:SASL_PLAINTEXT"
    if ${KAFKA_SSL_ENABLED}
    then
      MAP="${MAP},BROKER_TLS:SASL_SSL"
    fi
  else
    MAP="${MAP},BROKER_NONTLS:PLAINTEXT"
    if ${KAFKA_SSL_ENABLED}
    then
      MAP="${MAP},BROKER_TLS:SSL"
    fi
  fi
  sed -i -e "s%^listener.security.protocol.map=.*$%${MAP}%g" ${CONTROLLERCONFIG}
  # In the future, dynamically update Node ID
  # sed -i -e "s%^node.id=.*%node.id=<SOME_ID>%g" ${CONTROLLERCONFIG}
  # Get Node ID
  NODE_ID=$( grep ^node.id ${CONTROLLERCONFIG} | cut -f 2 -d '=' )
else
  echo "ERROR: Unsupported node role '${NODE_ROLE}'. Unable to proceed."
  exit 1
fi 

# Check for existing cluster ID:
# Controller 1 takes the lead; it is the only node allowed to check for legacy cluster IDs
#   and to generate a cluster ID if none is found
if [ "${NODE_ROLE}" == "controller" ] && [ "${NODE_ID}" == "1" ]
then
  # Controller log directory first
  if [ -f ${CONTROLLER_LOG_DIR}/meta.properties ]
  then
    CLUSTER_ID=$( grep cluster.id ${CONTROLLER_LOG_DIR}/meta.properties | cut -f 2- -d '=' )
    echo "Found cluster ID '${CLUSTER_ID}' in ${CONTROLLER_LOG_DIR}/meta.properties."
  fi
  # No need to look back at the broker directory. Either an explicit migration happened prior 
  # to bringing up this node, or else this should be treated as a clean setug
  # If there is no cluster ID, generate one
  if [ "${CLUSTER_ID}x" == "x" ]
  then
    echo "No existing cluster ID found. Will generate one."
    CLUSTER_ID=$( ${KAFKA_HOME}/bin/kafka-storage.sh random-uuid )
  fi
else
  if [ -f ${CONTROLLER_LOG_DIR}/meta.properties ]
  then
    CLUSTER_ID=$( grep cluster.id ${CONTROLLER_LOG_DIR}/meta.properties | cut -f 2- -d '=' )
    echo "Found cluster ID '${CLUSTER_ID}' in ${CONTROLLER_LOG_DIR}/meta.properties."
  else
    echo "No existing controller-provided cluster ID found. Waiting for Controller Node 1 to generate one."
    # Wait for Node 1 to process the formatting step, which will write the cluster ID to ${CONTROLLER_LOG_DIR}/meta.properties
    COUNT=0
    while :
    do
      grep -q cluster.id ${CONTROLLER_LOG_DIR}/meta.properties 2>/dev/null
      if [ $? -eq 0 ]
      then
        echo "Cluster ID available after ${COUNT} seconds."
        CLUSTER_ID=$( grep cluster.id ${CONTROLLER_LOG_DIR}/meta.properties | cut -f 2- -d '=' )
        break
      elif [ ${COUNT} -ge 120 ]
      then
        echo "Still no cluster ID available after ${COUNT} seconds; giving up."
        exit 1
      else
        sleep 5
        COUNT=$(( ${COUNT} + 5 ))
      fi
    done
  fi
fi

# Ensure storage is correctly formatted
echo "Formatting Node '${NODE_ID}' storage for Cluster ID '${CLUSTER_ID}' and '${NODE_ROLE}' role using configuration file '${KAFKA_HOME}/config/${NODE_ROLE}.properties'..."
${KAFKA_HOME}/bin/kafka-storage.sh format -t ${CLUSTER_ID} -c ${KAFKA_HOME}/config/${NODE_ROLE}.properties
# Start Kafka process based on node role
echo "Starting Node '${NODE_ID}' with '${NODE_ROLE}' role using configuration file '${KAFKA_HOME}/config/${NODE_ROLE}.properties'..."
exec "${KAFKA_HOME}/bin/kafka-server-start.sh" "${KAFKA_HOME}/config/${NODE_ROLE}.properties"
